allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

subprojects { project ->
    if (project.plugins.hasPlugin("com.android.application") || project.plugins.hasPlugin("com.android.library")) {
        def android = project.extensions.findByName("android")
        if (android != null && android.hasProperty("namespace") && android.namespace == null) {
            android.namespace = project.group
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}