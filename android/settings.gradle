pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

      repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
    plugins {
            id "dev.flutter.flutter-plugin-loader" version "1.0.0"
            id "com.android.application" version '8.9.0' apply false
            id "org.jetbrains.kotlin.android" version "1.9.10" apply false
    }

include ":app"

// apply from: "${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle/app_plugin_loader.gradle"
