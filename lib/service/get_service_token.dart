import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

import '../main.dart';


class GetTokenService {
  static Future<String> getDeviceId() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    try {
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id; // Android device ID
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor!; // iOS device ID
      } else {
        deviceId = 'Unknown';
      }
    } catch (e) {
      deviceId = 'Error: $e';
    }

    return deviceId;
  }

  static String getApiKey() {
    if (appDB.user?.APIKey != null && appDB.user?.APIKey != '') {
      print("app db data  =====>${appDB.user?.APIKey}");

      APIKey = appDB.user?.APIKey ?? '';
    } else {
      // AppNavigation.pushAndRemoveAllScreen(navigatorKey.currentContext!,  LoginScreen());
    }

    return APIKey;
  }
}
