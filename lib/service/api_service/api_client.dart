import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

class ApiClient {
  Dio? dio;

  Dio apiClientInstance(Map<String, dynamic> apiheader,
      {required BuildContext context}) {
    BaseOptions options = BaseOptions(
      baseUrl: ServerConstant.base_url,
      connectTimeout: Duration(milliseconds: 600000),
      receiveTimeout: Duration(milliseconds: 600000),
    );

    dio = Dio(options);

    dio!.interceptors.add(LogInterceptor(responseBody: true));
    dio!.interceptors.add(
      InterceptorsWrapper(
        onRequest:
            (RequestOptions option, RequestInterceptorHandler handler) async {
          Map<String, dynamic> header = apiheader;
          option.headers.addAll(header);
          return handler.next(option);
        },
        onResponse: (Response response, ResponseInterceptorHandler handler) {
          return handler.next(response);
        },
        onError: (DioException e, ErrorInterceptorHandler handler) {
          print('Dio DEFAULT Error Message :---------------> ${e.message}');
          if (e.type == DioExceptionType.unknown) {
            print('<<<<<<<-------------DEFAULT Error---------->>>>>>');
          } else if (e.type == DioExceptionType.connectionTimeout) {
            print('<<<<<<<-------------CONNECT_TIMEOUT---------->>>>>>');
          } else if (e.type == DioExceptionType.receiveTimeout) {
            print('<<<<<<<-------------RECEIVE_TIMEOUT---------->>>>>>');
          }
          if (e.response != null && e.response!.statusCode! == 404) {
            print(e.response!.statusCode!.toString());
          }
          if (e.response != null && e.response!.statusCode! == 422) {
            // showSnackbar(AppLocalizations.of(context)!.invalidMno, context);
          }
          if (e.response != null && e.response!.statusCode! == 401) {
            // showSnackbar(AppLocalizations.of(context)!.invalidMno, context);
            print('<<<<<<<-------------401---------->>>>>>');
            print(e.response!.statusCode!.toString());
            print(e.response?.data.toString());
            print(e.response?.statusMessage);
            if (e.response?.data.toString() ==
                "LoginFromUnauthorizedIPAddress") {
              customSnackBar(
                context: navigatorKey.currentContext!,
                message: AppLocalizations.of(navigatorKey.currentContext!)!
                    .ipAuthoriseError,
                actionButtonText:
                    AppLocalizations.of(navigatorKey.currentContext!)!
                        .closeText
                        .toUpperCase(),
              );
            } else if (e.response?.data.toString() == "LoginFailed") {
              customSnackBar(
                context: navigatorKey.currentContext!,
                message: AppLocalizations.of(navigatorKey.currentContext!)!
                    .inCorrectUserNamePassword,
                actionButtonText:
                    AppLocalizations.of(navigatorKey.currentContext!)!
                        .closeText
                        .toUpperCase(),
              );
            }
          }
          if (e.response!.statusCode! == 500) {
            // showSnackbar(AppLocalizations.of(context)!.servererror, context);
          } else {
            print("else error ==> ${e.message}");
          }
          return handler.next(e);
        },
      ),
    );
    return dio!;
  }
}
