class ServerConstant {
  // //Base URL
  static String base_url = '';

  //scanner Api
  static const scanner = 'Helper/Skin';
  static const login = 'Login/TryLogin';

  //change password api
  static const change_password = 'Login/ChangePassword';

  //update profile photo api
  static const profile_photo = 'Document/SetMyProfilePhoto';
  static const personData = 'Person/MyData';

  //clocking api
  static const my_clocking_status = 'Clocking/MyClockingStatus';
  static const my_clocking_in = 'Clocking/ClockIn';
  static const my_clocking_out = 'Clocking/ClockOut';

  static const resetPassword = 'Login/ResetPassword';
  static const swapOpenForWeek = 'SwapAndOpen/MyServicesPerWeek';
  static const swapOpenForMonth = 'SwapAndOpen/MyServicesPerMonth';
  static const firstShift = 'Schedule/MyFirstSchedule';

  //payslip api
  static const payslip = 'Document/MyPaySlips';
  static const payslipDocument = 'Document/MyDocument';

  //department list api
  static const departmentList = 'OrganisationInfo/OrganisationStructure';

  //hours api
  static const hoursMonthList = 'Timesheet/MyTimesheetMonthlyHours';
  static const hoursWorkWeek = 'Timesheet/MyTimesheetPerWeek';
  static const hoursWeekList = 'Timesheet/MyTimesheetPerMonth';
  static const myTimeSheetList = 'Timesheet/MyTimesheetData';
  static const activityList = 'HoursCommon/CostCenters';
  static const departmentListData = 'HoursCommon/DepartmentsWithRightPerson';
  static const timeSheetEmptyRow = 'Timesheet/MyTimesheetDataEmptyRow';
  static const departmentSetting = 'HoursCommon/DepartmentSettings';
  static const taskList = 'HoursCommon/CalendarItemsPerson';
  static const costCentersNonTimeBoundPerson =
      'HoursCommon/CostCentersNonTimeboundPerson';
  static const myTimeSheetDataSave = 'Timesheet/MyTimesheetDataSave';
  static const costCentersExclusions = 'HoursCommon/CostCentersExclusions';
  static const organisationalUnitHierarchy =
      'HoursCommon/OrganisationalUnitHierarchy';
  static const departmentsWithRightPerson =
      'HoursCommon/DepartmentsWithRightPerson';

  //availability api
  static const availabilityYearWeek = 'Availability/MyAvailabilityYearWeek';
  static const availabilityYearWeekSave =
      'Availability/MyAvailabilityYearWeekSave';
  static const availabilityYearWeekDetailList =
      'Availability/MyAvailabilityYearWeekDay';
  static const availabilityYearWeekDaySave =
      'Availability/MyAvailabilityYearWeekDaySave';

  //feedback api
  static const feedBack = 'Task/AddUserRequestTask';

  //notification api
  static const notificationList = 'Notification/MyNotifications';
  static const readNotification = 'Notification/ReadMyNotification';

  //absence api
  static const myBalancesOverview = 'Absence/MyBalancesOverview';
  static const timesheetLeaveInYear = 'Absence/TimesheetLeaveInYear';
  static const leaveType = 'Absence/TimesheetLeaveTypes';
  static const myHoursPeriods = 'Absence/MyHoursInPeriod';
  static const timesheetInsertLeaveRequest =
      'Absence/TimesheetInsertLeaveRequest';

  //news api
  static const news = 'Info/MyNews';
  static const newsAutoOpen = 'Info/MyAutoOpenNews';
  static const newsAutoOpenSuccess = 'Info/AutoOpenNewsRead';
  static const publicDocument = 'Document/PublicDocument';
  static const getProfilePic = 'Document/ProfilePhoto';

  //schedule api
  static const schedulePerWeek = 'Schedule/MySchedulePerWeek';
  static const schedulePerMonth = 'Schedule/MySchedulePerMonth';
  static const scheduleDepartmentList = 'Schedule/MyScheduleDepartments';
  static const schedulePerDate = 'Schedule/MyDepartmentSchedules';
  static const scheduleCalendarData = 'Schedule/MySchedulePerMonthCalendar';
  static const scheduleSwapAndOpenMonth = 'SwapAndOpen/MyServicesPerMonth';
  static const scheduleSwapAndOpenWeek = 'SwapAndOpen/MyServicesPerWeek';
  static const scheduleSetSwapService = 'SwapAndOpen/SetSwapServiceState';
  static const toggleMyOpenServiceSubscription =
      'SwapAndOpen/ToggleMyOpenServiceSubscription';

  static const getDeclrationList = 'Declaration/MyDeclarations';
  static const getDeclrationTypes = 'Declaration/DeclarationTypes';
  static const createDeclaration = 'Declaration/CreateDeclaration';

  //Header value
  static const Header_Content_Key = 'Content-Type';
  static const Header_Content_value = 'application/json';
  static const Header_Content_valueFormData = 'application/form-data';
  static const Header_Content_valueUnlecoded =
      'application/x-www-form-urlencoded';
  static const Header_Authorization_KEY = 'Authorization';
  static const Header_XAPI_KEY = 'X-API-Key';
  static const Header_Accept_Key = 'Accept';
  static const BEARER_TOKEN = 'Bearer ';
}
