import 'package:flutter/material.dart';

class AppColors {
  static const Color transparent = Colors.transparent;

  static const Color black = Colors.black;
  static const Color white = Colors.white;
  static const Color blue = Colors.blue;
  static const Color red = Colors.red;

  static const Color primaryColor = Color(0xff068C5D);
  static const Color lightGreenColor = Color(0xff60704E);

  static const Color greyColor = Color(0xff989aa2);
  static const Color darkGreyColor = Color(0xff666666);
  static const Color darkModeGreyColor = Color(0xffa0a0a0);
  static const Color snackBarColor = Color(0xffd0d0d0);
  static const Color snackBarDarkColor = Color(0xff333333);
  static const Color iconLightGreyColor = Color(0xff969698);
  static const Color iconDarkGreyColor = Color(0xff757575);

  static const Color lightBlackColor = Color(0xff222428);
  static const Color mediumBlackColor = Color(0xff1a1b1e);
  static const Color cardBlackColor = Color(0xff1a1b1e);
  static const Color blackDarkColor = Color(0xff121212);

  static const Color listGridGreyColor1 = Color(0xfff1f1f1);
  static const Color listGridDarkGreyColor1 = Color(0xff222428);

  static const Color listGridGreyColor2 = Color(0xff1a1b1e);
  static const Color listGridDarkGreyColor2 = Color(0xffffffff);

  static const Color cardWhiteColor = Colors.white;

  static const Color limeGreenColor = Color(0xff10dc60);

  // static const Color lightBlackColor = Color(0xff1a1b1e);
  static const Color darkGreenColor = Color(0xff0A5B3D);
  static const Color mediumGreenColor = Color.fromRGBO(3, 60, 37, 1);

  static const Color darkModeRedColor = Color(0xffff4961);
  static const Color lightModeRedColor = Color(0xffd33939);

  // shift colors
  static const Color roosterColor = Color(0xff008E58);
  static const Color ruilenColor = Color(0xff4DE0ED);
  static const Color openDienstenColor = Color(0xffFFC45F);

  static const List<BoxShadow>? commonBoxShadow = [
    BoxShadow(
      color: Colors.grey,
      //spreadRadius: 5,
      blurRadius: 2,
      offset: Offset(0, 2),
    )
  ];
}
