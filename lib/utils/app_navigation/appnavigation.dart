import 'package:flutter/widgets.dart';

class AppNavigation {
  static Future<void> nextScreen(BuildContext context, Widget screen,
      {Function? action}) async {
    Function action1 = action ?? () {};
    // return Navigator.push(
    //     context,
    //     PageRouteBuilder(
    //       transitionDuration: const Duration(milliseconds: 600),
    //       pageBuilder: (context, animation, secondaryAnimation) => screen,
    //       transitionsBuilder: (context, animation, secondaryAnimation, child) {
    //         return FadeTransition(
    //           opacity: animation,
    //           child: child,
    //         );
    //       },
    //     )).then((value) => action1());

    return await Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => screen,
          // transitionsBuilder: (context, animation, secondaryAnimation, child) {
          //   const begin = Offset(1.0, 0.0);
          //   const end = Offset.zero;
          //   const curve = Curves.ease;
          //   var tween =
          //       Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          //   return SlideTransition(
          //     position: animation.drive(tween),
          //     child: child,
          //   );
          // },
        )).then((value) => action1());
  }

  static dynamic previousScreen(BuildContext context, [dynamic result]) {
    return Navigator.pop(context, result);
  }

  static pushAndRemoveAllScreen(BuildContext context, Widget screen) {
    return Navigator.pushAndRemoveUntil(
        context,
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 600),
          pageBuilder: (context, animation, secondaryAnimation) => screen,
          // transitionsBuilder: (context, animation, secondaryAnimation, child) {
          //   return FadeTransition(
          //     opacity: animation,
          //     child: child,
          //   );
          // },
        ),
        (route) => false);
  }

  static replaceScreen(BuildContext context, Widget screen) {
    return Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        transitionDuration: const Duration(milliseconds: 600),
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }
}
// import 'package:flutter/widgets.dart';

// class AppNavigation {
//   static nextScreen(BuildContext context, Widget screen, {Function? action}) {
//     Function action1 = action ?? () {};
//     return Navigator.push(
//         context,
//         PageRouteBuilder(
//           transitionDuration: const Duration(milliseconds: 600),
//           pageBuilder: (context, animation, secondaryAnimation) => screen,
//           transitionsBuilder: (context, animation, secondaryAnimation, child) {
//             return FadeTransition(
//               opacity: animation,
//               child: child,
//             );
//           },
//         )).then((value) => action1());

//     // return Navigator.push(
//     //     context,
//     //     PageRouteBuilder(
//     //       pageBuilder: (context, animation, secondaryAnimation) => screen,
//     //       transitionsBuilder: (context, animation, secondaryAnimation, child) {
//     //         const begin = Offset(1.0, 0.0);
//     //         const end = Offset.zero;
//     //         const curve = Curves.ease;
//     //         var tween =
//     //             Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
//     //         return SlideTransition(
//     //           position: animation.drive(tween),
//     //           child: child,
//     //         );
//     //       },
//     //     )).then((value) => action1());
//   }

//   static previousScreen(BuildContext context) {
//     return Navigator.pop(context);
//   }

//   static pushAndRemoveAllScreen(BuildContext context, Widget screen) {
//     return Navigator.pushAndRemoveUntil(
//         context,
//         PageRouteBuilder(
//           pageBuilder: (context, animation, secondaryAnimation) => screen,
//           transitionsBuilder: (context, animation, secondaryAnimation, child) {
//             const begin = Offset(1.0, 0.0);
//             const end = Offset.zero;
//             const curve = Curves.ease;
//             var tween =
//                 Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
//             return SlideTransition(
//               position: animation.drive(tween),
//               child: child,
//             );
//           },
//         ),
//         (route) => false);
//   }

//   static replaceScreen(BuildContext context, Widget screen) {
//     return Navigator.of(context).pushReplacement(
//       PageRouteBuilder(
//         pageBuilder: (context, animation, secondaryAnimation) => screen,
//         transitionsBuilder: (context, animation, secondaryAnimation, child) {
//           const begin = Offset(1.0, 0.0);
//           const end = Offset.zero;
//           const curve = Curves.ease;
//           var tween =
//               Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
//           return SlideTransition(
//             position: animation.drive(tween),
//             child: child,
//           );
//         },
//       ),
//     );
//   }
// }
