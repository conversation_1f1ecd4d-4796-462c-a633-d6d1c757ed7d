class DateLanguageConverter {
  static const Map<String, String> dutchToEnglishDays = {
    'maandag': 'Monday',
    'dinsdag': 'Tuesday',
    'woensdag': 'Wednesday',
    'donderdag': 'Thursday',
    'vrijdag': 'Friday',
    'zaterdag': 'Saturday',
    'zondag': 'Sunday',
  };

  static const Map<String, String> dutchToEnglishMonths = {
    'januari': 'January',
    'februari': 'February',
    'maart': 'March',
    'april': 'April',
    'mei': 'May',
    'juni': 'June',
    'juli': 'July',
    'augustus': 'August',
    'september': 'September',
    'oktober': 'October',
    'november': 'November',
    'december': 'December',
  };

  static isDateTimeInDutch(String input) {
    return dutchToEnglishDays.keys.any(input.toLowerCase().contains) ||
        dutchToEnglishMonths.keys.any(input.toLowerCase().contains);
  }

 static String normalizeDateToEnglish(String input) {
    String result = input.toLowerCase();
    bool containsDutch = false;

    // Check and replace Dutch day names
    dutchToEnglishDays.forEach((dutch, english) {
      if (result.contains(dutch)) {
        result = result.replaceAll(dutch, english.toLowerCase());
        containsDutch = true;
      }
    });

    // Check and replace Dutch month names
    dutchToEnglishMonths.forEach((dutch, english) {
      if (result.contains(dutch)) {
        result = result.replaceAll(dutch, english.toLowerCase());
        containsDutch = true;
      }
    });

    // Capitalize only if it was Dutch
    if (containsDutch) {
      result = result
          .split(' ')
          .map((word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1)}'
              : '')
          .join(' ');
    } else {
      result = input; // already English, return original
    }

    return result;
  }

  static String convertDutchToEnglish(String input) {
    String result = input.toLowerCase();

    dutchToEnglishDays.forEach((dutch, english) {
      result = result.replaceAll(dutch, english.toLowerCase());
    });

    dutchToEnglishMonths.forEach((dutch, english) {
      result = result.replaceAll(dutch, english.toLowerCase());
    });

    return _capitalizeEachWord(result);
  }

  static String convertEnglishToDutch(String input) {
    String result = input.toLowerCase();

    // Reverse the maps
    final englishToDutchDays = {
      for (var e in dutchToEnglishDays.entries) e.value.toLowerCase(): e.key
    };
    final englishToDutchMonths = {
      for (var e in dutchToEnglishMonths.entries) e.value.toLowerCase(): e.key
    };

    englishToDutchDays.forEach((english, dutch) {
      result = result.replaceAll(english, dutch);
    });

    englishToDutchMonths.forEach((english, dutch) {
      result = result.replaceAll(english, dutch);
    });

    return _capitalizeEachWord(result);
  }

  static String _capitalizeEachWord(String input) {
    return input
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1)}'
            : '')
        .join(' ');
  }
}
