import 'package:staff_medewerker/main.dart';

class ShiftTimeFormatter {
  /// Formats shift time display based on hideShiftEndTime setting
  /// 
  /// Parameters:
  /// - startTime: The start time of the shift (required)
  /// - endTime: The end time of the shift (optional)
  /// - separator: The separator between start and end time (default: ' - ')
  /// 
  /// Returns:
  /// - If hideShiftEndTime is enabled: only start time
  /// - If hideShiftEndTime is disabled: "startTime - endTime" format
  /// - If endTime is null, empty, or "00:00": only start time regardless of setting
  static String formatShiftTime(String? startTime, String? endTime, {String separator = ' - '}) {
    // Return empty if start time is null or empty
    if (startTime == null || startTime.isEmpty) {
      return '';
    }
    
    // Clean start time
    String cleanStartTime = startTime.trim();
    if (cleanStartTime.isEmpty) {
      return '';
    }
    
    // If hideShiftEndTime setting is enabled, only show start time
    if (appDB.hideShiftEndTime) {
      return cleanStartTime;
    }
    
    // Check if end time should be hidden (null, empty, or 00:00)
    if (endTime == null || 
        endTime.isEmpty || 
        endTime.trim().isEmpty ||
        endTime.trim() == '00:00' ||
        endTime.trim() == '0:00') {
      return cleanStartTime;
    }
    
    String cleanEndTime = endTime.trim();
    
    // If end time is valid, show both start and end time
    return '$cleanStartTime$separator$cleanEndTime';
  }
  
  /// Formats shift time for display in lists/cards
  static String formatShiftTimeForList(String? startTime, String? endTime) {
    return formatShiftTime(startTime, endTime, separator: ' - ');
  }
  
  /// Formats shift time for display in calendar views
  static String formatShiftTimeForCalendar(String? startTime, String? endTime) {
    return formatShiftTime(startTime, endTime, separator: '-');
  }
  
  /// Checks if end time should be displayed based on settings and value
  static bool shouldShowEndTime(String? endTime) {
    if (appDB.hideShiftEndTime) {
      return false;
    }
    
    if (endTime == null || 
        endTime.isEmpty || 
        endTime.trim().isEmpty ||
        endTime.trim() == '00:00' ||
        endTime.trim() == '0:00') {
      return false;
    }
    
    return true;
  }
}
