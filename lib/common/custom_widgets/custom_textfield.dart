import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String? labelText;
  final InputBorder? border;
  final int? maxLines;
  final Color? focusedBorder;


  CustomTextField({
    required this.controller,
    required this.hintText, this. border,  this.labelText, this.maxLines,
    this.focusedBorder
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,


      // validator: (value) {
      //   if (value == "" || value!.isEmpty){
      //     return AppLocalizations.of(context)!.passwordCantEmptyText;
      //   }
      //   return null;
      // },
      style: TextStyle(fontSize: AppSize.sp14),
      decoration: InputDecoration(
        enabledBorder: border??UnderlineInputBorder(
          borderSide: BorderSide(width: 0),
        ),
        focusedBorder: border??UnderlineInputBorder(
            borderSide: BorderSide(width: 2, color: focusedBorder??AppColors.limeGreenColor)
        ),
        hintText: hintText,
        floatingLabelBehavior:FloatingLabelBehavior.always,
        labelText: labelText,

        hintStyle: context.textTheme.bodyMedium?.copyWith(fontSize: AppSize.sp14,color: context.themeColors.greyColor),
        contentPadding: EdgeInsets.symmetric(horizontal: AppSize.w10),
      ),
      keyboardType: TextInputType.multiline,
      maxLines: maxLines??null,
      cursorColor: context.themeColors.greyColor,
    );
  }
}
