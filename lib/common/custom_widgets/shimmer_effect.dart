import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class ShimmerWidget extends StatelessWidget {
  final Color? baseColor;
  final Color? highlightColor;
  final double? height;
  final double? width;
  final EdgeInsets? margin;
  final Widget? child;

  ShimmerWidget({this.baseColor, this.highlightColor, this.height, this.width,this.margin, this.child});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? AppColors.greyColor.withOpacity(0.2),
      highlightColor: highlightColor ?? AppColors.white.withOpacity(0.2),
      child:child ??  Container(
        margin: margin??EdgeInsets.all(0),
        decoration: BoxDecoration(
          color: Colors.grey[300]!,
        ),
        width: width ?? double.infinity,
        height: height ?? AppSize.h20,
      ),
    );
  }
}
