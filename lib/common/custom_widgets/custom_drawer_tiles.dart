import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class CustomDrawerListTile extends StatelessWidget {
  final String titleText;
  final IconData? leadingIcon;
  final Function()? onTap;
  final Widget? trailing;
  final bool? isSvgIcon;
  final String? svgIconPath;
  final TextStyle? titleStyle;
  final ColorFilter? colorFilter;
  final Color? iconColor;

  CustomDrawerListTile({
    Key? key,
    required this.titleText,
    this.leadingIcon,
    this.onTap,
    this.trailing,
    this.isSvgIcon = false,
    this.svgIconPath,
    this.titleStyle,
    this.colorFilter,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: context.themeColors.mediumBlackColor,
      child: ListTile(
        title: Text(
          titleText,
          style: titleStyle ??
              context.textTheme.headlineLarge?.copyWith(
                  fontSize: AppSize.sp14, fontWeight: FontWeight.normal),
        ),
        leading: isSvgIcon == true
            ? SvgPicture.asset(
                svgIconPath ?? "",
                height: 24,
                width: 24,
                colorFilter: colorFilter ??
                    ColorFilter.mode(
                        iconColor ?? context.themeColors.darkGreyColor,
                        BlendMode.srcIn),
              )
            : Icon(leadingIcon,
                color: iconColor ?? context.themeColors.darkGreyColor),
        trailing: trailing,
        onTap: onTap,
        dense: true,
      ),
    );
  }
}
