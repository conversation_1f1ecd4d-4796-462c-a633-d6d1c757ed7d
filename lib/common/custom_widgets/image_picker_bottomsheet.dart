import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';

void imagePickerBottomSheet(
    {required BuildContext context,
    required VoidCallback onCameraPress,
    required VoidCallback onGalleryPress}) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Container(
        color: context.themeColors.darkBlackColor,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            ListTile(
              title: Text(
                AppLocalizations.of(context)!.optionText,
                style: context.textTheme.bodyMedium!.copyWith(
                    fontSize: AppSize.sp15,
                    color: context.themeColors.textColor),
              ),
              dense: true,
            ),
            commonListTile(
              leadingIcon: Ionicons.camera,
              titleText: AppLocalizations.of(context)!.cameraText,
              onTap: () async {
                onCameraPress();
                AppNavigation.previousScreen(context);
              },
              context: context,
            ),
            commonListTile(
              leadingIcon: Ionicons.images,
              titleText: AppLocalizations.of(context)!.selectPhotoText,
              onTap: () async {
                onGalleryPress();
                AppNavigation.previousScreen(context);
              },
              context: context,
            ),
          ],
        ),
      );
    },
  );
}

Widget commonListTile({
  required IconData leadingIcon,
  required String titleText,
  required VoidCallback onTap,
  BuildContext? context,
}) {
  return ListTile(
    leading: Icon(
      leadingIcon,
      color: context?.themeColors.iconColor,
    ),
    title: Text(
      titleText,
      style: (context != null)
          ? context.textTheme.bodyMedium!.copyWith(
              fontSize: AppSize.sp14,
              color: context.themeColors.textColor,
            )
          : TextStyle(fontSize: AppSize.sp14), // Default style
    ),
    onTap: onTap,
  );
}
