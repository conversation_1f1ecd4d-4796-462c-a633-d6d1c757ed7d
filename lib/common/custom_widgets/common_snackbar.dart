import 'package:flutter/material.dart';
import 'package:flash/flash.dart';
import 'package:flash/flash_helper.dart'; // Add this import for showFlash extension
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/utils/appsize.dart';

// Legacy SnackBar function - kept for backward compatibility
void customSnackBar({
  required BuildContext context,
  required String message,
  String? actionButtonText,
  int snackBarDuration = 4,
  VoidCallback? onPressed,
}) {
  message.showAlert(
      duration: Duration(
        seconds: snackBarDuration,
      ),
      actionButtonText: actionButtonText,
      onPressed: onPressed);
  // return ScaffoldMessenger.of(context)
  //   ..removeCurrentSnackBar()
  //   ..showSnackBar(SnackBar(
  //     content: Text(
  //       message,
  //       style: context.textTheme.bodyMedium!
  //           .copyWith(color: context.themeColors.mediumBlackColor),
  //     ),
  //     duration: Duration(seconds: snackBarDuration),
  //     backgroundColor: context.themeColors.snackbarBGColor,
  //     elevation: 10,
  //     behavior: SnackBarBehavior.floating,
  //     margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
  //     action: actionButtonText != null
  //         ? SnackBarAction(
  //             label: actionButtonText.toUpperCase(),
  //             onPressed: () {
  //               ScaffoldMessenger.of(context).removeCurrentSnackBar();
  //               onPressed?.call();
  //             },
  //             textColor: context.themeColors.mediumBlackColor,
  //           )
  //         : null,
  //   ));
}

// void hideSnackBar(BuildContext context) {
//   ScaffoldMessenger.of(context).hideCurrentSnackBar();
// }

// New FlushBar Extension
extension StringFlashBarExtension on String {
  /// Show error alert using FlushBar
  void showErrorAlert(
          {Duration? duration,
          VoidCallback? onPressed,
          String? actionButtonText}) =>
      navigatorKey.currentContext!.showFlash<void>(
        builder: (context, controller) {
          return FlashBar(
            controller: controller,
            behavior: FlashBehavior.floating,
            content: Text(
              this,
              style: navigatorKey.currentContext!.textTheme.bodyMedium
                  ?.copyWith(
                      color: navigatorKey
                          .currentContext!.themeColors.mediumBlackColor),
            ),
            backgroundColor:
                navigatorKey.currentContext!.themeColors.snackbarBGColor,
            indicatorColor: const Color(0xFFE57373), // Red indicator for error
            icon: const Icon(Icons.error_outline, color: Color(0xFFE57373)),
            shouldIconPulse: false,
            margin: EdgeInsets.symmetric(
              horizontal: AppSize.h16,
              vertical: AppSize.h16,
            ),
            forwardAnimationCurve: Curves.bounceOut,
            primaryAction: actionButtonText != null
                ? TextButton(
                    onPressed: () {
                      controller.dismiss();
                      onPressed?.call();
                    },
                    child: Text(
                      actionButtonText.toUpperCase(),
                      style: TextStyle(
                        color: navigatorKey
                            .currentContext!.themeColors.mediumBlackColor,
                      ),
                    ),
                  )
                : null,
          );
        },
        duration: duration ??
            const Duration(seconds: 4), // Match original snackbar duration
      );

  /// Show success alert using FlushBar
  void showSuccessAlert(
          {Duration? duration,
          VoidCallback? onPressed,
          String? actionButtonText}) =>
      navigatorKey.currentContext!.showFlash<void>(
        builder: (context, controller) {
          return FlashBar(
            controller: controller,
            behavior: FlashBehavior.floating,
            content: Text(
              this,
              style: navigatorKey.currentContext!.textTheme.bodyMedium
                  ?.copyWith(
                      color: navigatorKey
                          .currentContext!.themeColors.mediumBlackColor),
            ),
            backgroundColor:
                navigatorKey.currentContext!.themeColors.snackbarBGColor,
            indicatorColor:
                const Color(0xFF81C784), // Green indicator for success
            icon: const Icon(Icons.check_circle_outline,
                color: Color(0xFF81C784)),
            shouldIconPulse: false,
            margin: EdgeInsets.symmetric(
              horizontal: AppSize.h16,
              vertical: AppSize.h16,
            ),
            forwardAnimationCurve: Curves.bounceOut,
            primaryAction: actionButtonText != null
                ? TextButton(
                    onPressed: () {
                      controller.dismiss();
                      onPressed?.call();
                    },
                    child: Text(
                      actionButtonText.toUpperCase(),
                      style: TextStyle(
                        color: navigatorKey
                            .currentContext!.themeColors.mediumBlackColor,
                      ),
                    ),
                  )
                : null,
          );
        },
        duration: duration ??
            const Duration(seconds: 4), // Match original snackbar duration
      );

  /// Show info alert using FlushBar
  void showInfoAlert(
          {Duration? duration,
          VoidCallback? onPressed,
          String? actionButtonText}) =>
      navigatorKey.currentContext!.showFlash<void>(
        builder: (context, controller) {
          return FlashBar(
            controller: controller,
            behavior: FlashBehavior.floating,
            content: Text(
              this,
              style: navigatorKey.currentContext!.textTheme.bodyMedium
                  ?.copyWith(
                      color: navigatorKey
                          .currentContext!.themeColors.mediumBlackColor),
            ),
            backgroundColor:
                navigatorKey.currentContext!.themeColors.snackbarBGColor,
            icon: const Icon(
              Icons.info_outline,
            ),
            shouldIconPulse: false,
            margin: EdgeInsets.symmetric(
              horizontal: AppSize.h16,
              vertical: AppSize.h16,
            ),
            forwardAnimationCurve: Curves.bounceOut,
            primaryAction: actionButtonText != null
                ? TextButton(
                    onPressed: () {
                      controller.dismiss();
                      onPressed?.call();
                    },
                    child: Text(
                      actionButtonText.toUpperCase(),
                      style: TextStyle(
                        color: navigatorKey
                            .currentContext!.themeColors.mediumBlackColor,
                      ),
                    ),
                  )
                : null,
          );
        },
        duration: duration ??
            const Duration(seconds: 4), // Match original snackbar duration
      );

  /// Show general alert using FlushBar (matches original customSnackBar style)
  void showAlert({
    Duration? duration,
    String? actionButtonText,
    VoidCallback? onPressed,
  }) =>
      navigatorKey.currentContext!.showFlash<void>(
        builder: (context, controller) {
          return FlashBar(
            controller: controller,
            behavior: FlashBehavior.floating,
            content: Text(
              this,
              style: navigatorKey.currentContext!.textTheme.bodyMedium
                  ?.copyWith(
                      color: navigatorKey
                          .currentContext!.themeColors.mediumBlackColor),
            ),
            backgroundColor:
                navigatorKey.currentContext!.themeColors.snackbarBGColor,
            margin: EdgeInsets.symmetric(
              horizontal: AppSize.h16,
              vertical: AppSize.h16,
            ),
            forwardAnimationCurve: Curves.bounceOut,
            primaryAction: actionButtonText != null
                ? TextButton(
                    onPressed: () {
                      controller.dismiss();
                      onPressed?.call();
                    },
                    child: Text(
                      actionButtonText.toUpperCase(),
                      style: TextStyle(
                        color: navigatorKey
                            .currentContext!.themeColors.mediumBlackColor,
                      ),
                    ),
                  )
                : null,
          );
        },
        duration: duration ??
            const Duration(seconds: 4), // Match original snackbar duration
      );
}
