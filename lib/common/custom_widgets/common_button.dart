import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class ReusableContainerButton extends StatelessWidget {
  final double? width;
  final double? height;
  final double? elevation;
  final Color? backgroundColor;
  final VoidCallback? onPressed;
  final String buttonText;
  final IconData? isTrailingIcon;
  final TextStyle? textStyle;
  final double? trailingIconSize;
  final bool isLoading;

  final BorderRadius? borderRadius;

  ReusableContainerButton(
      {this.width,
      this.isLoading = false,
      this.height,
      this.backgroundColor,
      required this.onPressed,
      required this.buttonText,
      this.isTrailingIcon,
      this.textStyle,
      this.trailingIconSize,
      this.borderRadius, this.elevation});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation:elevation ?? 3,
      color: Colors.transparent,
      child: Ink(
        width: width ?? double.infinity,
        height: height ?? AppSize.h42,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.primaryColor,
          borderRadius: borderRadius ?? BorderRadius.circular(AppSize.r4),
        ),
        child: InkWell(
          borderRadius: borderRadius ?? BorderRadius.circular(AppSize.r4),
          onTap: onPressed,
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  isLoading
                      ? SizedBox(
                          height: AppSize.h10,
                          width: AppSize.h10,
                          child: CircularProgressIndicator(
                            strokeWidth: AppSize.h1,
                            color: AppColors.white,
                          ),
                        )
                      : Text(
                          buttonText,
                          style: textStyle ?? context.textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                  if (isTrailingIcon != null) ...[
                    const Spacer(),
                    Icon(
                      isTrailingIcon,
                      color: Colors.white,
                      size: trailingIconSize ?? AppSize.h18,
                    ),
                  ]
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
