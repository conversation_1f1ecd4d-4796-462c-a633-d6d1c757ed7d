import 'package:flutter/material.dart';

@immutable
class ThemeTextColors extends ThemeExtension<ThemeTextColors> {
  const ThemeTextColors({
    required this.text,

  });

  final Color text;


  @override
  ThemeExtension<ThemeTextColors> copyWith({
    Color? text,

  }) {
    return ThemeTextColors(
      text: text ?? this.text,

    );
  }

  @override
  ThemeExtension<ThemeTextColors> lerp(covariant ThemeExtension<ThemeTextColors>? other, double t) {
    if (other is! ThemeTextColors) {
      return this;
    }
    return ThemeTextColors(
      text: Color.lerp(text, other.text, t)!,

    );
  }
}
