import 'package:flutter/material.dart';

@immutable
class ThemeColors extends ThemeExtension<ThemeColors> {
  const ThemeColors(
      {required this.primaryColor,
      required this.secondaryWhiteColor,
      required this.lightGreenColor,
      required this.greyColor,
      required this.lightBlackColor,
      required this.darkGreyColor,
      required this.drawerColor,
      required this.mediumBlackColor,
      required this.textColor,
      required this.cardColor,
      required this.snackbarBGColor,
      required this.darkBlackColor,
      required this.iconColor,
      required this.listGridColor1,
      required this.listGridColor2,
      required this.listWeekGridColor,
      required this.cardShadowColor,
      required this.homeBackgroundColor,
      required this.homeContainerColor,
      required this.declarativeBackgroundColor,
      required this.declarativeScaffoldBackgroundColor,
      required this.homeShadowColor,
      required this.buttonRedColor,
      required this.usePinColor,
      required this.dividerAvailbilityColor,
      required this.bottomBarBackgroundColor,
      required this.cardAbsenceColor,
      required this.calendarTodayDateColor,
      required this.declarationTypeColor,
      required this.calendarSecondIndexColor,
      required this.scheduleContainerColor,
      required this.scheduleDividerColor,
      required this.declarativeDatePickBackgroundColor,
      required this.scheduleSelectedContainerColor});

  final Color primaryColor;

  final Color scheduleDividerColor;
  final Color declarativeScaffoldBackgroundColor;
  final Color declarativeDatePickBackgroundColor;
  final Color calendarSecondIndexColor;
  final Color dividerAvailbilityColor;
  final Color secondaryWhiteColor;
  final Color lightGreenColor;
  final Color greyColor;
  final Color lightBlackColor;
  final Color darkGreyColor;
  final Color drawerColor;
  final Color mediumBlackColor;
  final Color declarationTypeColor;
  final Color cardColor;
  final Color textColor;
  final Color snackbarBGColor;
  final Color listWeekGridColor;
  final Color darkBlackColor;
  final Color iconColor;
  final Color listGridColor1;
  final Color listGridColor2;
  final Color cardShadowColor;
  final Color buttonRedColor;
  final Color usePinColor;
  final Color homeBackgroundColor;
  final Color homeContainerColor;
  final Color homeShadowColor;
  final Color bottomBarBackgroundColor;
  final Color cardAbsenceColor;
  final Color calendarTodayDateColor;
  final Color scheduleContainerColor;
  final Color scheduleSelectedContainerColor;
  final Color declarativeBackgroundColor;

  @override
  ThemeExtension<ThemeColors> copyWith({
    Color? declarativeScaffoldBackgroundColor,
    Color? dividerAvailbilityColor,
    Color? scheduleSelectedContainerColor,
    Color? scheduleContainerColor,
    Color? listWeekGridColor,
    Color? primaryColor,
    Color? secondaryGreenColor,
    Color? calendarTodayDateColor,Color? declarationTypeColor,
    Color? lightGreenColor,
    Color? greyColor,
    Color? lightBlackColor,
    Color? darkGreyColor,
    Color? drawerColor,
    Color? mediumBlackColor,
    Color? textColor,
    Color? cardColor,
    Color? snackbarBGColor,
    Color? darkBlackColor,
    Color? iconColor,
    Color? listGridColor1,
    Color? scheduleDividerColor,
    Color? listGridColor2,
    Color? cardShadowColor,
    Color? homeBackgroundColor,
    Color? calendarSecondIndexColor,
    Color? homeContainerColor,
    Color? homeShadowColor,
    Color? buttonRedColor,
    Color? usePinColor,
    Color? bottomBarBackgroundColor,
    Color? cardAbsenceColor,
    Color? declarativeBackgroundColor,Color? declarativeDatePickBackgroundColor,
  }) {
    return ThemeColors(
      declarativeDatePickBackgroundColor: declarativeDatePickBackgroundColor ?? this.declarativeDatePickBackgroundColor,
      declarativeScaffoldBackgroundColor: declarativeScaffoldBackgroundColor ?? this.declarativeScaffoldBackgroundColor,
      declarativeBackgroundColor: declarativeBackgroundColor ?? this.declarativeBackgroundColor,
      declarationTypeColor: declarationTypeColor ?? this.declarationTypeColor,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryWhiteColor: secondaryGreenColor ?? secondaryWhiteColor,
      lightGreenColor: lightGreenColor ?? this.lightGreenColor,
      greyColor: greyColor ?? this.greyColor,
      lightBlackColor: lightBlackColor ?? this.lightBlackColor,
      darkGreyColor: darkGreyColor ?? this.darkGreyColor,
      drawerColor: drawerColor ?? this.drawerColor,
      mediumBlackColor: mediumBlackColor ?? this.mediumBlackColor,
      textColor: textColor ?? this.textColor,
      cardColor: cardColor ?? this.cardColor,
      snackbarBGColor: snackbarBGColor ?? this.snackbarBGColor,
      darkBlackColor: darkBlackColor ?? this.darkBlackColor,
      iconColor: iconColor ?? this.iconColor,
      listGridColor1: listGridColor1 ?? this.listGridColor1,
      listGridColor2: listGridColor2 ?? this.listGridColor2,
      cardShadowColor: cardShadowColor ?? this.cardShadowColor,
      cardAbsenceColor: cardAbsenceColor ?? this.cardAbsenceColor,
      homeBackgroundColor: homeBackgroundColor ?? this.homeBackgroundColor,
      homeContainerColor: homeContainerColor ?? this.homeContainerColor,
      homeShadowColor: homeShadowColor ?? this.homeShadowColor,
      buttonRedColor: buttonRedColor ?? this.buttonRedColor,
      usePinColor: usePinColor ?? this.usePinColor,
      dividerAvailbilityColor: dividerAvailbilityColor ?? this.dividerAvailbilityColor,
      bottomBarBackgroundColor: bottomBarBackgroundColor ?? this.bottomBarBackgroundColor,
      calendarTodayDateColor: calendarTodayDateColor ?? this.calendarTodayDateColor,
      calendarSecondIndexColor: calendarSecondIndexColor ?? this.calendarSecondIndexColor,
      scheduleContainerColor: scheduleContainerColor ?? this.scheduleContainerColor,
      scheduleSelectedContainerColor: scheduleSelectedContainerColor ?? this.scheduleSelectedContainerColor,
      scheduleDividerColor: scheduleDividerColor ?? this.scheduleDividerColor,
      listWeekGridColor: listWeekGridColor ?? this.listWeekGridColor,
    );
  }

  @override
  ThemeExtension<ThemeColors> lerp(covariant ThemeExtension<ThemeColors>? other, double t) {
    if (other is! ThemeColors) {
      return this;
    }
    return ThemeColors(
      declarativeDatePickBackgroundColor: Color.lerp(declarativeDatePickBackgroundColor, other.declarativeDatePickBackgroundColor, t)!,
      declarativeScaffoldBackgroundColor: Color.lerp(declarativeScaffoldBackgroundColor, other.declarativeScaffoldBackgroundColor, t)!,
      declarativeBackgroundColor: Color.lerp(declarativeBackgroundColor, other.declarativeBackgroundColor, t)!,
      declarationTypeColor: Color.lerp(declarationTypeColor, other.declarationTypeColor, t)!,
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t)!,
      secondaryWhiteColor: Color.lerp(secondaryWhiteColor, other.secondaryWhiteColor, t)!,
      lightGreenColor: Color.lerp(lightGreenColor, other.lightGreenColor, t)!,
      greyColor: Color.lerp(greyColor, other.greyColor, t)!,
      lightBlackColor: Color.lerp(lightBlackColor, other.lightBlackColor, t)!,
      darkGreyColor: Color.lerp(darkGreyColor, other.darkGreyColor, t)!,
      drawerColor: Color.lerp(drawerColor, other.drawerColor, t)!,
      mediumBlackColor: Color.lerp(mediumBlackColor, other.mediumBlackColor, t)!,
      textColor: Color.lerp(textColor, other.textColor, t)!,
      cardColor: Color.lerp(cardColor, other.cardColor, t)!,
      snackbarBGColor: Color.lerp(snackbarBGColor, other.snackbarBGColor, t)!,
      darkBlackColor: Color.lerp(darkBlackColor, other.darkBlackColor, t)!,
      iconColor: Color.lerp(iconColor, other.iconColor, t)!,
      listGridColor1: Color.lerp(listGridColor1, other.listGridColor1, t)!,
      listGridColor2: Color.lerp(listGridColor2, other.listGridColor2, t)!,
      cardShadowColor: Color.lerp(cardShadowColor, other.cardShadowColor, t)!,
      buttonRedColor: Color.lerp(buttonRedColor, other.buttonRedColor, t)!,
      usePinColor: Color.lerp(usePinColor, other.usePinColor, t)!,
      cardAbsenceColor: Color.lerp(cardAbsenceColor, other.cardAbsenceColor, t)!,
      homeBackgroundColor: Color.lerp(homeBackgroundColor, other.homeBackgroundColor, t)!,
      homeContainerColor: Color.lerp(homeContainerColor, other.homeContainerColor, t)!,
      homeShadowColor: Color.lerp(homeShadowColor, other.homeShadowColor, t)!,
      dividerAvailbilityColor: Color.lerp(dividerAvailbilityColor, other.dividerAvailbilityColor, t)!,
      bottomBarBackgroundColor: Color.lerp(bottomBarBackgroundColor, other.bottomBarBackgroundColor, t)!,
      calendarTodayDateColor: Color.lerp(calendarTodayDateColor, other.calendarTodayDateColor, t)!,
      calendarSecondIndexColor: Color.lerp(calendarSecondIndexColor, other.calendarSecondIndexColor, t)!,
      scheduleContainerColor: Color.lerp(scheduleContainerColor, other.scheduleContainerColor, t)!,
      scheduleSelectedContainerColor:
          Color.lerp(scheduleSelectedContainerColor, other.scheduleSelectedContainerColor, t)!,
      scheduleDividerColor: Color.lerp(scheduleDividerColor, other.scheduleDividerColor, t)!,
      listWeekGridColor: Color.lerp(listWeekGridColor, other.listWeekGridColor, t)!,
    );
  }
}
