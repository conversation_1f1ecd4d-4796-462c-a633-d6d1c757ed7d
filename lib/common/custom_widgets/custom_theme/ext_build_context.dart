import 'package:flutter/material.dart'
    show Brightness, BuildContext, ColorScheme, MediaQuery, TextTheme, Theme, ThemeData;
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_colors.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_text_colors.dart';


/// extension for [BuildContext]
extension BuildContextEx on BuildContext {
  /// to get theme
  ThemeData get theme => Theme.of(this);

  /// to get colorScheme
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// to get text theme
  TextTheme get textTheme => Theme.of(this).textTheme;

  ThemeColors get themeColors => Theme.of(this).extension<ThemeColors>()!;

  ThemeTextColors get themeTextColors => Theme.of(this).extension<ThemeTextColors>()!;

  /// to get width from media query
  double get width => MediaQuery.sizeOf(this).width;

  /// to get width in pixels
  double get widthPixel => MediaQuery.sizeOf(this).width * MediaQuery.of(this).devicePixelRatio;

  /// to get height from media query
  double get height => MediaQuery.sizeOf(this).height;

  /// to get height in pixels
  double get heightPixel => MediaQuery.sizeOf(this).height * MediaQuery.of(this).devicePixelRatio;

  /// to theme brightness [Brightness.dark] or [Brightness.light]
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;


}
