import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_colors.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_text_colors.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../utils/appsize.dart';

const _themeColors = ThemeColors(
  declarativeScaffoldBackgroundColor: AppColors.lightBlackColor,
  declarativeDatePickBackgroundColor: AppColors.lightBlackColor,
    primaryColor: AppColors.primaryColor,
    secondaryWhiteColor: AppColors.black,
    lightGreenColor: AppColors.lightGreenColor,
    greyColor: AppColors.greyColor,
    textColor: AppColors.white,
    lightBlackColor: AppColors.lightBlackColor,
    darkGreyColor: AppColors.darkModeGreyColor,
    drawerColor: AppColors.lightBlackColor,
    mediumBlackColor: AppColors.mediumBlackColor,
    declarationTypeColor: AppColors.mediumBlackColor,
    cardColor: AppColors.cardBlackColor,
    snackbarBGColor: AppColors.snackBarColor,
    darkBlackColor: AppColors.blackDarkColor,
    iconColor: AppColors.iconLightGreyColor,
    listGridColor1: AppColors.lightBlackColor,
    listGridColor2: AppColors.listGridDarkGreyColor2,
    cardShadowColor: AppColors.blackDarkColor,
    homeBackgroundColor: AppColors.lightBlackColor,
    homeContainerColor: Color.fromRGBO(26, 27, 30, 1),
    declarativeBackgroundColor: Color.fromRGBO(26, 27, 30, 1),
    homeShadowColor: Color.fromRGBO(26, 27, 30, 1),
    buttonRedColor: AppColors.darkModeRedColor,
    dividerAvailbilityColor: Color.fromRGBO(34, 34, 34, 1),
    cardAbsenceColor: AppColors.lightBlackColor,
    usePinColor: AppColors.black,
    bottomBarBackgroundColor: Color.fromRGBO(18, 18, 18, 1),
    calendarTodayDateColor: Color.fromRGBO(34, 36, 40, 1),
    calendarSecondIndexColor: Color.fromRGBO(34, 36, 40, 1),
    scheduleContainerColor: Color.fromRGBO(38, 38, 38, 1),
    scheduleSelectedContainerColor: Color.fromRGBO(79, 79, 79, 1),
    scheduleDividerColor: Color.fromRGBO(51, 51, 51, 1),
    listWeekGridColor: Color.fromRGBO(81, 82, 84, 1));

const _themeTextColors = ThemeTextColors(
  text: AppColors.white,
);

final darkTheme = ThemeData.dark(useMaterial3: false).copyWith(
  scaffoldBackgroundColor: _themeColors.lightBlackColor,
  textTheme: ThemeData.light(useMaterial3: false).textTheme.copyWith(
      headlineLarge: TextStyle(color: _themeTextColors.text, fontWeight: FontWeight.w700, fontSize: AppSize.sp26),
      titleLarge: TextStyle(
        color: _themeTextColors.text,
        fontSize: AppSize.sp20,
        fontWeight: FontWeight.w500,
      ),
      titleMedium: TextStyle(
        color: _themeTextColors.text,
        fontSize: AppSize.sp16,
        fontWeight: FontWeight.w400,
      ),
      titleSmall: TextStyle(color: _themeTextColors.text, fontWeight: FontWeight.w300, fontSize: AppSize.sp14),
      labelSmall: TextStyle(
          color: _themeTextColors.text,
          //fontFamily: "Poppins",
          fontWeight: FontWeight.w200,
          fontSize: AppSize.sp12)),
  appBarTheme: AppBarTheme(
    backgroundColor: _themeColors.primaryColor,
    titleTextStyle: TextStyle(
      fontWeight: FontWeight.w700,
      color: _themeTextColors.text,
      fontSize: AppSize.sp16,
    ),
  ),
  timePickerTheme: TimePickerThemeData(
      hourMinuteTextColor: AppColors.white,
      // cancelButtonStyle: ButtonStyle(c),
      hourMinuteColor: AppColors.primaryColor,
      confirmButtonStyle: ButtonStyle(
        backgroundColor: MaterialStateProperty.resolveWith(
          (states) => AppColors.primaryColor, // Change this to the desired color
        ),
      ),
      hourMinuteShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0), // Set the shape of the hour and minute picker
      ),
      cancelButtonStyle: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith((states) => Colors.red))),
  colorScheme: ColorScheme.light(primary: AppColors.primaryColor),
  // chipTheme: ChipThemeData(
  //   backgroundColor: AppColors.greyColor,
  //   selectedColor: _themeColors.primaryColor,
  //   side: BorderSide.none,
  //   shape: RoundedRectangleBorder(
  //     borderRadius: BorderRadius.circular(AppSize.r10),
  //   ),
  // ),
  extensions: <ThemeExtension<dynamic>>[
    _themeColors,
    _themeTextColors,
  ],
);
