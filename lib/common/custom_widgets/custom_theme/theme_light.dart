import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_colors.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/theme_text_colors.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../utils/appsize.dart';

/// Application Theme
///
final _lightThemeData = ThemeData.light(useMaterial3: false);

const _themeColors = ThemeColors(
    declarativeDatePickBackgroundColor: Color.fromRGBO(241, 242, 247, 1),
    primaryColor: AppColors.primaryColor,
    declarativeScaffoldBackgroundColor: Color.fromRGBO(239, 239, 239, 1),
    secondaryWhiteColor: AppColors.white,
    textColor: AppColors.black,
    lightGreenColor: AppColors.lightGreenColor,
    greyColor: AppColors.greyColor,
    lightBlackColor: AppColors.lightBlackColor,
    dividerAvailbilityColor: Color.fromRGBO(222, 222, 222, 1),
    darkGreyColor: AppColors.darkGreyColor,
    drawerColor: AppColors.white,
    mediumBlackColor: AppColors.white,
    declarationTypeColor: Color.fromRGBO(224, 224, 224, 1),
    cardColor: AppColors.cardWhiteColor,
    snackbarBGColor: AppColors.snackBarDarkColor,
    darkBlackColor: AppColors.white,
    iconColor: AppColors.iconDarkGreyColor,
    listGridColor1: AppColors.listGridGreyColor1,
    listGridColor2: AppColors.listGridGreyColor2,
    cardShadowColor: AppColors.greyColor,
    homeBackgroundColor: Color.fromRGBO(240, 240, 240, 1),
    homeContainerColor: AppColors.white,
    declarativeBackgroundColor: Color.fromRGBO(255, 253, 253, 1),
    homeShadowColor: Colors.grey,
    buttonRedColor: AppColors.lightModeRedColor,
    usePinColor: AppColors.white,
    bottomBarBackgroundColor: AppColors.white,
    cardAbsenceColor: AppColors.white,
    calendarTodayDateColor: Color.fromRGBO(241, 241, 241, 1),
    calendarSecondIndexColor: Color.fromRGBO(241, 241, 241, 1),
    scheduleContainerColor: Color.fromRGBO(235, 235, 235, 1),
    scheduleSelectedContainerColor: AppColors.white,
    listWeekGridColor: AppColors.listGridGreyColor1,
    scheduleDividerColor: Color.fromRGBO(219, 219, 219, 1));

const _themeTextColors = ThemeTextColors(
  text: AppColors.black,
);
Color primaryColor1 = Color(0xff005232);
final lightTheme = _lightThemeData.copyWith(
  scaffoldBackgroundColor: _themeColors.secondaryWhiteColor,
  textTheme: ThemeData.light(useMaterial3: false).textTheme.copyWith(
      headlineLarge: TextStyle(color: _themeTextColors.text, fontWeight: FontWeight.w700, fontSize: AppSize.sp26),
      titleLarge: TextStyle(
        color: _themeTextColors.text,
        fontSize: AppSize.sp18,
        fontWeight: FontWeight.w500,
      ),
      titleMedium: TextStyle(
        color: _themeTextColors.text,
        fontSize: AppSize.sp16,
        fontWeight: FontWeight.w400,
      ),
      titleSmall: TextStyle(color: _themeTextColors.text, fontWeight: FontWeight.w300, fontSize: AppSize.sp14),
      labelSmall: TextStyle(
          color: _themeTextColors.text,
          //fontFamily: "Poppins",
          fontWeight: FontWeight.w200,
          fontSize: AppSize.sp12)),
  appBarTheme: AppBarTheme(
    backgroundColor: _themeColors.primaryColor,
    titleTextStyle: TextStyle(
      fontWeight: FontWeight.w700,
      color: _themeTextColors.text,
      fontSize: AppSize.sp16,
    ),
  ),
  timePickerTheme: TimePickerThemeData(
      hourMinuteTextColor: AppColors.white,
      // cancelButtonStyle: ButtonStyle(c),
      hourMinuteColor: AppColors.primaryColor,
      confirmButtonStyle: ButtonStyle(
        backgroundColor: MaterialStateProperty.resolveWith(
          (states) => AppColors.primaryColor, // Change this to the desired color
        ),
      ),
      hourMinuteShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0), // Set the shape of the hour and minute picker
      ),
      cancelButtonStyle: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith((states) => Colors.red))),
  colorScheme: ColorScheme.light(primary: AppColors.primaryColor),
  // chipTheme: ChipThemeData(
  //   backgroundColor: AppColors.greyColor,
  //   selectedColor: _themeColors.primaryColor,
  //   side: BorderSide.none,
  //   shape: RoundedRectangleBorder(
  //     borderRadius: BorderRadius.circular(AppSize.r10),
  //   ),
  // ),
  extensions: <ThemeExtension<dynamic>>[
    _themeColors,
    _themeTextColors,
  ],
);
