import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Function()? onTap;
  final Function()? actionOnTap;
  final bool isLeading;
  final bool actions;
  final List<Widget>? actionList;
  final Widget? leading;
  final Widget? titleIcon;
  final Widget? titleWidget;
  final bool centerTitle;
  final double? leadingWidth;
  final bool? automaticallyImplyLeading;
  CustomAppBar({
    required this.title,
    this.onTap,
    this.isLeading = false,
    this.automaticallyImplyLeading = true,
    this.actions = false,
    this.actionOnTap,
    this.actionList,
    this.leading,
    this.centerTitle = false,
    this.titleIcon,
    this.leadingWidth,
    this.titleWidget,
  });

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
        automaticallyImplyLeading: automaticallyImplyLeading ?? true,
        leading: isLeading
            ? leading ??
                GestureDetector(
                    onTap: onTap ??
                        () {
                      log('onTap ===>');
                          AppNavigation.previousScreen(context);
                        },
                    child: Icon(
                      Ionicons.arrow_back_outline,
                    ))
            : null,
        title: Row(
          children: [
            Text(title ?? '',
                style: context.textTheme.headlineLarge!
                    .copyWith(color: Colors.white, fontSize: AppSize.sp17, fontWeight: FontWeight.w500)),
            SpaceH(AppSize.w4),
            titleIcon ?? Container()
          ],
        ),
        actions: actions ? actionList : null,
        leadingWidth: leadingWidth,
        centerTitle: centerTitle ? true : false,
        backgroundColor: context.themeColors.primaryColor);
  }
}
