import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

class CustomSwitch extends StatelessWidget {
  final bool value;
  final Color? activeColor;
  final Color? inactiveTrackColor;
  final Color? inactiveThumbColor;
  final Color? activeTrackColor;
  final MaterialStateProperty<Color?>? thumbColor;
  final Function(bool) onChanged;

  CustomSwitch({
    required this.value,
    this.activeColor,
    required this.onChanged, this.inactiveTrackColor, this.thumbColor, this.inactiveThumbColor, this.activeTrackColor,
  });

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      activeColor: activeColor??context.themeColors.primaryColor,
      onChanged: onChanged,
      inactiveTrackColor: inactiveTrackColor,
      activeTrackColor: activeTrackColor,
      thumbColor: thumbColor,
      inactiveThumbColor: inactiveThumbColor,

    );
  }
}
