import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';

import '../../utils/appsize.dart';
import '../../utils/asset_path/assets_path.dart';
import '../../utils/colors/app_colors.dart';

class CommonCircularProfilePicture extends StatelessWidget {
  final String? url;
  final String? localUrl;
  final double? height;
  final double? width;
  final double? cornerRadius;
  final ImageProvider<Object>? imageProvider1;
  const CommonCircularProfilePicture({
    super.key,
    required this.url,
    this.height,
    this.localUrl,
    this.width,
    this.cornerRadius,
    this.imageProvider1,
  });

  @override
  Widget build(BuildContext context) {
    // print("url ====>$url");
    // print("url ====>$localUrl");
    return Container(
      height: height ?? AppSize.h40,
      width: width ?? AppSize.w40,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
      child: url != null && url!.trim().isNotEmpty && url != "null"
          ? CachedNetworkImage(
              imageUrl: url ?? '',
              imageBuilder: (context, imageProvider) => Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  // borderRadius: BorderRadius.circular(cornerRadius ?? AppSize.sp25),
                  image: DecorationImage(
                    image: imageProvider,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              placeholder: (context, url) => ShimmerWidget(
                // baseColor: AppColors.red,
                // highlightColor: AppColors.primaryColor,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.white,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Icon(Icons.person),
            )
          : localUrl == null
              ? Image.asset(
                  AssetsPath.defaultProfileImage,
                  fit: BoxFit.fill,
                )
              : Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(shape: BoxShape.circle),
                  child: ClipOval(
                    child: Image.file(
                      File(localUrl ?? ''),
                      fit: BoxFit.cover,
                    ),
                  )),
    );
  }
}
