import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CustomListTile extends StatelessWidget {
  final String title;
  final Widget? trailingWidget;
  final Function()? onTap;
  final Color? bgColor;
  final bool isBorder;
  final EdgeInsets? padding;

  // final String? trailingText;

  CustomListTile(
      {required this.title,
      this.trailingWidget,
      // this.trailingText,
      this.onTap,
      this.bgColor,
      this.isBorder = false,
      this.padding});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: ListTile(
        contentPadding: padding ?? null,
        splashColor: AppColors.iconLightGreyColor,
        dense: true,
        shape: isBorder
            ? Border(
                bottom: BorderSide(width: 1, color: context.themeColors.greyColor.withOpacity(0.5)),
              )
            : null,
        tileColor: bgColor ?? context.themeColors.cardColor,
        title: Text(
          title,
          style: context.textTheme.bodyMedium?.copyWith(
            color: context.themeColors.textColor,
            fontSize: AppSize.sp14,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            trailingWidget ?? Container(),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
