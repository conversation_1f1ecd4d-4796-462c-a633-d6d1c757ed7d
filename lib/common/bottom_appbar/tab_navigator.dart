// import 'package:flutter/material.dart';
// import 'package:staff_medewerker/screens/hours_module/ui/hours_main_screen.dart';
// import 'package:staff_medewerker/screens/news_module/news_screen.dart';
// import 'package:staff_medewerker/screens/schedule_module/ui/schedule_screen.dart';
//
// import '../../screens/availability_module/ui/availability_screen.dart';
// import '../../screens/home_module/ui/home_screen.dart';
//
// class TabNavigatorRoutes {
//   static const String root = '/';
// }
//
// class TabNavigator extends StatefulWidget {
//   final GlobalKey<NavigatorState>? navigatorKey;
//   final int index;
//   final bool isFromFirstTime;
//
//   const TabNavigator({Key? key, required this.navigatorKey, required this.index, this.isFromFirstTime = false})
//       : super(key: key);
//
//   @override
//   State<TabNavigator> createState() => _TabNavigatorState();
// }
//
// class _TabNavigatorState extends State<TabNavigator> {
//   Map<String, WidgetBuilder> _routeBuilders(BuildContext context) {
//     return {
//       '/': (context) {
//         return [
//           HomeScreen(
//             isFromFirstTime: widget.isFromFirstTime,
//           ),
//           AvailabilityScreen(),
//           const ScheduleScreen(),
//           HoursScreen(),
//           const NewsScreen(),
//           Container(),
//         ].elementAt(widget.index);
//       },
//     };
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final routeBuilders = _routeBuilders(context);
//     return Navigator(
//       key: widget.navigatorKey,
//       initialRoute: TabNavigatorRoutes.root,
//       onGenerateRoute: (routeSettings) {
//         return MaterialPageRoute(builder: (context) {
//           return routeBuilders[routeSettings.name]!(context);
//         });
//       },
//     );
//   }
// }
