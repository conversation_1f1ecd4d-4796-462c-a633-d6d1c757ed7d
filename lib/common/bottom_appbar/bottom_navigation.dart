// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:staff_medewerker/common/bottom_appbar/tab_item.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
// import 'package:staff_medewerker/main.dart';
// import 'package:staff_medewerker/screens/notification_module/bloc/open_service_cubit.dart';
// import 'package:staff_medewerker/utils/colors/app_colors.dart';
//
// import '../../utils/appsize.dart';
//
// class BottomNavigation extends StatefulWidget {
//   final int currentTab;
//   final ValueChanged<int> onSelectTab;
//   const BottomNavigation({Key? key, required this.currentTab, required this.onSelectTab}) : super(key: key);
//
//   @override
//   State<BottomNavigation> createState() => _BottomNavigationState();
// }
//
// class _BottomNavigationState extends State<BottomNavigation> {
//   final notificationBloc = BlocProvider.of<NotificationCubit>(navigatorKey.currentContext!);
//
//   @override
//   Widget build(BuildContext context) {
//     return BottomNavigationBar(
//       type: BottomNavigationBarType.fixed,
//       showSelectedLabels: false,
//       showUnselectedLabels: false,
//       selectedFontSize: 0,
//       unselectedFontSize: 10,
//       backgroundColor: context.themeColors.bottomBarBackgroundColor,
//       items: [
//         _buildItem(
//           tabItem: 0,
//         ),
//         _buildItem(
//           tabItem: 1,
//         ),
//         _buildItem(
//           tabItem: 2,
//         ),
//         _buildItem(
//           tabItem: 3,
//         ),
//         _buildItem(
//           tabItem: 4,
//         ),
//         _buildItem(
//           tabItem: 5,
//         ),
//       ],
//       onTap: widget.onSelectTab,
//       currentIndex: widget.currentTab,
//     );
//   }
//
//   BottomNavigationBarItem _buildItem({required int tabItem}) {
//     return BottomNavigationBarItem(
//       icon: Column(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Stack(
//             clipBehavior: Clip.none,
//             children: [
//               Icon(
//                 _tabSelected(tabItem),
//                 color:
//                     widget.currentTab == tabItem ? context.themeColors.primaryColor : context.themeColors.darkGreyColor,
//               ),
//               BlocBuilder<NotificationCubit, NotificationState>(
//                 builder: (ctx, state) {
//                   final notificationBloc = ctx.read<NotificationCubit>();
//
//                   if (tabItem == 5 && notificationBloc.notificationList.isNotEmpty) {
//                     return Positioned(
//                       right: -8,
//                       top: -8,
//                       child: Container(
//                         height: AppSize.w12,
//                         width: AppSize.w12,
//                         decoration: BoxDecoration(shape: BoxShape.circle, color: AppColors.lightModeRedColor),
//                         child: Center(
//                             child: Text(
//                           notificationBloc.notificationList.length.toString(),
//                           style: context.textTheme.bodySmall?.copyWith(color: AppColors.white, fontSize: AppSize.sp8),
//                         )),
//                       ),
//                     );
//                   } else {
//                     return Container(
//                       width: 0,
//                     );
//                   }
//                 },
//               ),
//             ],
//           )
//         ],
//       ),
//       label: "",
//     );
//   }
//
//   IconData _tabSelected(item) {
//     return tabIcon[item]!;
//   }
// }
