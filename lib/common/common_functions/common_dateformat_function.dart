import 'package:intl/intl.dart';

class DateFormatFunctions {
  static String formatDay(int day) {
    return day.toString().padLeft(2, '0');
  }

  static String formatDate(DateTime date) {
    return DateFormat('yyyyMMdd').format(date);
  }

  static IosYearWeekData(DateTime selectedDate) {
    int weekNumber = (DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays / 7).ceil();
    String formattedWeek = weekNumber < 10 ? '0$weekNumber' : weekNumber.toString();

    final format = DateFormat('yyyy');

    // Format the selected date using the custom format
    return format.format(selectedDate) + '$formattedWeek';
  }

  static IosYearMonthData(DateTime selectedDate, {required int month}) {
    final format = DateFormat('yyyy');

    // Format the selected date using the custom format
    return format.format(selectedDate) + month.toString();
  }

  static formatDateTimeToIsoYearMonth(DateTime dateTime) {
    return DateFormat('yyyyMM').format(dateTime);
  }
}
