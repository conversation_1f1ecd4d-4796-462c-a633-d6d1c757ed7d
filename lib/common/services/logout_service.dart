import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/app/db/app_db.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/availability_module/bloc/availability_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/bloc/clocking_screen_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/bloc/time_sheet_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/bloc/request_leave_screen_cubit.dart';
import 'package:staff_medewerker/screens/open_service_module/bloc/open_service_cubit.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/bloc/hours_cubit.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class LogoutService {
  static Future<void> performCompleteLogout(BuildContext context) async {
    try {
      // Clear all user-specific data from cubits/blocs
      await _clearAllCubitsData(context);

      // Clear shared preferences
      Prefs.preferences.clear();

      // Clear database
      AppDB appDB = await AppDB.getInstance();
      await appDB.clearData();

      // Preserve base URL
      await prefs.setString('base_url', ServerConstant.base_url);

      print("Complete logout performed successfully");
    } catch (e) {
      print("Error during logout: $e");
    }
  }

  static Future<void> _clearAllCubitsData(BuildContext context) async {
    try {
      // Clear Availability Module data
      final availabilityCubit = context.read<AvailabilityCubit>();
      availabilityCubit.clearAllUserData();

      // Clear Schedule Module data
      final scheduleCubit = context.read<ScheduleCubit>();
      scheduleCubit.clearAllUserData();

      // Clear Absence Module data
      final absenceCubit = context.read<AbsenceCubit>();
      _clearAbsenceData(absenceCubit);

      // Clear Clocking Module data
      final clockingCubit = context.read<ClockingCubit>();
      _clearClockingData(clockingCubit);

      // Clear Time Sheet data
      final timeSheetCubit = context.read<TimeSheetCubit>();
      timeSheetCubit.clearAllData(false);

      // Clear Declaration Module data
      final declarationCubit = context.read<DeclrationCubit>();
      declarationCubit.clearData();

      // Clear Request Leave data
      final requestLeaveCubit = context.read<RequestLeaveCubit>();
      requestLeaveCubit.clearFormData(context: context);

      // Clear Open Service Module data
      final openServiceCubit = context.read<OpenServiceCubit>();
      _clearOpenServiceData(openServiceCubit);

      // Clear Profile Module data
      final profileCubit = context.read<ProfileCubit>();
      _clearProfileData(profileCubit);

      // Clear Hours Module data
      final hoursCubit = context.read<HoursCubit>();
      _clearHoursData(hoursCubit);
    } catch (e) {
      print("Error clearing cubit data: $e");
      // Continue with logout even if some cubits fail to clear
    }
  }

  static void _clearAbsenceData(AbsenceCubit cubit) {
    cubit.isLoading.value = false;
    cubit.myBalancesOverviewList.clear();
    cubit.leaveTypeList.clear();
    cubit.filteredList.value = [];
    cubit.totalList.value = [];
    cubit.selectedDate = "";
    cubit.selectedDateString.value = '${formatDate(DateTime.now())}';
    cubit.timesheetLeaveInYearList.clear();
  }

  static void _clearClockingData(ClockingCubit cubit) {
    cubit.selectedActivityName.value = '';
    cubit.selectedActivityId.value = '';
    cubit.isLoading.value = false;
    cubit.isButtonLoading.value = false;
    cubit.remarkTextController.clear();
    cubit.totalTrackedHours.value = 0;
    cubit.totalTrackedMinutes.value = 0;
    cubit.timer?.cancel();
    cubit.costCentersNonTimeBoundPersonList.clear();
    cubit.firstTimeApiCall = false;
    cubit.clockInResponse = null;
    cubit.clockOutResponse = null;
  }

  static void _clearOpenServiceData(OpenServiceCubit cubit) {
    cubit.isOpenServiceMonthLoading.value = false;
    cubit.isSetSwapShiftLoading.value = false;
    cubit.toggleMyOpenServiceSubscriptionLoading.value = false;
    cubit.isCancelSwapShiftLoading.value = false;
    cubit.swapNoteController.clear();
    cubit.selectDate = DateTime.now();
    cubit.myServicePerMonthList.clear();
  }

  static void _clearProfileData(ProfileCubit cubit) {
    cubit.imagePath.value = '';
    cubit.inProgress.value = false;
    cubit.isApiProfileCalled = false;
    cubit.getImage = null;
    cubit.isImageLoading.value = true;
    cubit.imageString = '';
    cubit.image = null;
  }

  static void _clearHoursData(HoursCubit cubit) {
    cubit.selectedValue = 'Locatie S';
    cubit.selectedId = 'c17238ad-5164-46e0-b443-6c921dd7746d';
    cubit.filteredActivity.clear();
    cubit.selectedActivityList.clear();
    cubit.selectedActivity = 'Activity';
    cubit.filterActivityValue.clear();
    cubit.hoursMonthList.clear();
    cubit.hoursWorkMonthList.clear();
    cubit.isHourLoading.value = false;
    cubit.workMonthHour = '';
  }
}

// Helper function for date formatting (if not already available)
String formatDate(DateTime date) {
  return "${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}";
}
