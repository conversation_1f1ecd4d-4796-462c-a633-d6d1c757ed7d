class ChangePasswordResponseModel {
  final List<String> result;
  final bool done;
  final String newGuid;
  final String? newTitle;
  final String? redirectUrl;
  final int statusCode;

  ChangePasswordResponseModel({
    required this.result,
    required this.done,
    required this.newGuid,
    this.newTitle,
    this.redirectUrl,
    required this.statusCode,
  });

  factory ChangePasswordResponseModel.fromJson(Map<String, dynamic> json, int statusCode) {
    return ChangePasswordResponseModel(
      result: List<String>.from(json['Result']),
      done: json['Done'],
      newGuid: json['NewGuid'],
      newTitle: json['NewTitle'],
      redirectUrl: json['RedirectURL'],
      statusCode: statusCode,
    );
  }
}

