import 'package:flutter/cupertino.dart';

import '../model/change_password_model.dart';
import 'change_password_api_provider.dart';


class ChangePasswordApiRepository {
  final changePasswordProvider = ChangePasswordApiProvider();

  Future<ChangePasswordResponseModel?> changePasswordApi({required BuildContext context, required String newPassword ,required String currentPassword}) async {
    final response = await changePasswordProvider.changePasswordApiCall(newPassword, currentPassword, context);

    if (response != null) {
      return ChangePasswordResponseModel.fromJson(response.data,response.statusCode!);
    } else {
      return null;
    }
  }
}
