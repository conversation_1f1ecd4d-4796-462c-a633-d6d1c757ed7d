import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';

import '../../../../../service/api_service/api_function.dart';
import '../../../../../service/api_service/server_constants.dart';

class ProfileImageApiProvider {
  Future<Response?> profileImageApiCall(String imagePath, BuildContext context) async {
    try {
      File file = File(imagePath);
      print('File is = ' + file.toString());
      List<int> fileInByte = file.readAsBytesSync();
      String fileInBase64 = base64Encode(fileInByte);

      print("object: $fileInBase64");
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "Photo": fileInBase64
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.profile_photo,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      print("Query======>$query");

      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> getProfilePicApiCall(BuildContext context, String guid) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "guid": guid
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.publicDocument,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
