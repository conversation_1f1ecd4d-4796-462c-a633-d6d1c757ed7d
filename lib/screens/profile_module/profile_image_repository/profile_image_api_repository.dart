import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/profile_module/model/model.dart';
import 'package:staff_medewerker/screens/profile_module/profile_image_repository/profile_image_api_provider.dart';

import '../../../app/db/model/download_file_model.dart';

class ProfileImageApiRepository {
  final profileImageProvider = ProfileImageApiProvider();
  Future<ProfilePhotoUpdateResponseModel?> profileImageApi(
      {required BuildContext context, required String imagePath}) async {
    final response = await profileImageProvider.profileImageApiCall(imagePath, context);

    if (response != null) {
      return ProfilePhotoUpdateResponseModel.from<PERSON>son(response.data, response.statusCode!);
    } else {
      return null;
    }
  }

  Future<DownloadFileModel?> getProfilePicApi({required BuildContext context, required String guid}) async {
    final response = await profileImageProvider.getProfilePicApiCall(context, guid);

    if (response != null) {
      return DownloadFileModel.from<PERSON>son(response.data);
    } else {
      return null;
    }
  }
}
