import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';

import '../widget/common/common_info_row.dart';

class PersonalInfoScreen extends StatefulWidget {
  const PersonalInfoScreen({super.key});

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await context.read<ProfileCubit>().profileDataApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
            title: AppLocalizations.of(context)!.personalInformationText),
        body: Padding(
          padding: EdgeInsets.only(
              top: AppSize.sp20, left: AppSize.sp20, right: AppSize.sp20),
          child: BlocBuilder<ProfileCubit, bool>(
            builder: (context, state) {
              if (state == true &&
                  !(appDB.user?.profileData?.isApiCallDone ?? false)) {
                return ListView.builder(
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return ShimmerWidget(
                      margin:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                      height: AppSize.h20,
                    );
                  },
                );
              } else {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SpaceV(AppSize.h10),
                    CommonInfoRow(
                        title: AppLocalizations.of(context)!.firstNameText,
                        value: '${(appDB.user?.profileData?.callName) ?? "-"}'),
                    CommonInfoRow(
                        title: AppLocalizations.of(context)!.fullNameText,
                        value: '${(appDB.user?.profileData?.fullName) ?? "-"}'),
                    CommonInfoRow(
                        title: AppLocalizations.of(context)!.dateOfBirthText,
                        value:
                            '${(appDB.user?.profileData?.birthDate) ?? "-"}'),
                    CommonInfoRow(
                        title: AppLocalizations.of(context)!.bankAccountText,
                        value:
                            '${(appDB.user?.profileData?.bankAccount) ?? "-"}'),
                  ],
                );
              }
            },
          ),
        ));
  }
}
