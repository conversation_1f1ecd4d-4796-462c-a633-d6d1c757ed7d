import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/profile_data.dart';
import 'package:staff_medewerker/screens/profile_module/profile_data_repository/profile_data_api_provider.dart';

class ProfileDataApiRepository {
  final profileDataProvider = ProfileDataApiProvider();

  Future<ProfileData?> profileDataApi({required BuildContext context}) async {
    final response = await profileDataProvider.profileDataApiCall(context);

    if (response != null) {
      return ProfileData.fromJson(response.data,response.statusCode!);
    } else {
      return null;
    }
  }
}