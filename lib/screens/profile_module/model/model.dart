class ProfilePhotoUpdateResponseModel {
  List<String> result;
  bool done;
  String? newGuid;
  dynamic newTitle;
  dynamic redirectUrl;
  final int? statusCode;

  ProfilePhotoUpdateResponseModel({
    required this.result,
    required this.done,
    required this.newGuid,
    this.newTitle,
    this.redirectUrl,
    required this.statusCode,
  });

  factory ProfilePhotoUpdateResponseModel.fromJson(Map<String, dynamic> json, int statusCode) {
    return ProfilePhotoUpdateResponseModel(
      result: List<String>.from(json['Result']),
      done: json['Done'],
      newGuid: json['NewGuid'],
      newTitle: json['NewTitle'],
      redirectUrl: json['RedirectURL'],
      statusCode: statusCode,
    );
  }
}

