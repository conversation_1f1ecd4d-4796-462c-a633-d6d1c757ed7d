import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CustomPasswordTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;

  CustomPasswordTextField({
    required this.controller,
    required this.hintText
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      // validator: (value) {
      //   if (value == "" || value!.isEmpty){
      //     return AppLocalizations.of(context)!.passwordCantEmptyText;
      //   }
      //   return null;
      // },
      decoration: InputDecoration(
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(width: 0),
          ),
          focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(width: 2, color: AppColors.limeGreenColor)
          ),
          hintText: hintText,
          hintStyle: context.textTheme.bodyMedium?.copyWith(fontSize: AppSize.sp14,color: context.themeColors.greyColor),
        contentPadding: EdgeInsets.symmetric(horizontal: AppSize.w10),
      ),
      obscureText: true,
      cursorColor: context.themeColors.greyColor,
    );
  }
}
