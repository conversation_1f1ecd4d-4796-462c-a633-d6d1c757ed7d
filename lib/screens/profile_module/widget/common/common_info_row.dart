import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class CommonInfoRow extends StatelessWidget {
  final String title;
  final double? titlePadding;
  final TextStyle? titleValue;
  final TextStyle? valueStyle;
  final String value;
  final bool needDivider;

  CommonInfoRow({
    required this.title,
    required this.value,
    this.needDivider = true,
    this.titleValue,
    this.titlePadding,
    this.valueStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SpaceV(AppSize.h4),
        Padding(
          padding: EdgeInsets.only(left: titlePadding ?? AppSize.w10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: titleValue ??
                    context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor.withOpacity(0.9),
                    ),
              ),
              SpaceV(AppSize.h2),
              // value sometimes not used
              value != ''
                  ? Text(
                      value,
                      style: valueStyle ??
                          context.textTheme.bodyMedium?.copyWith(
                            fontSize: AppSize.sp14,
                            color: context.themeColors.textColor,
                          ),
                    )
                  : Container(),
            ],
          ),
        ),
        SpaceV(AppSize.h4),
        !needDivider ? Container() : Divider(thickness: 1),
      ],
    );
  }
}
