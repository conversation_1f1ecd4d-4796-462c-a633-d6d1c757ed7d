import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class HoursApiProvider {
  // Future<Response?> hoursMonthApiCall(BuildContext context) async {
  //   try {
  //
  //
  //     final query = {
  //       "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
  //     };
  //
  //     Response response = await APIFunction.postAPICall(
  //       apiName: ServerConstant.base_url + ServerConstant.hoursMonthList,
  //       header: {
  //         ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
  //         ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
  //       },
  //       query,
  //       context: context,
  //     );
  //     return response;
  //   } catch (error, stacktrace) {
  //     print("Exception occurred: $error stackTrace: $stacktrace");
  //   }
  //   return null;
  // }

  Future<Response?> hoursWeekApiCall(BuildContext context, String isoMonth) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonth": isoMonth
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.hoursWeekList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> hoursMonthApiCall(BuildContext context, String iSOYearMonthStart, String iSOYearMonthEnd) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonthStart": iSOYearMonthStart,
        "ISOYearMonthEnd": iSOYearMonthEnd
      };
      print("hoursMonthApiCall: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.hoursMonthList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> hoursMonthWorkHourApiCall(BuildContext context, String iSOYearMonth) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonth": iSOYearMonth
      };
      print("hoursMonthApiCall11: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.hoursWeekList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> hoursWeekWorkHourApiCall(BuildContext context, String iSOYearWeek) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearWeek": iSOYearWeek
      };
      print("hoursMonthApiCall: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.hoursWorkWeek,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
