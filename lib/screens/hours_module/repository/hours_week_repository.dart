import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/repository/hours_api_provider.dart';

class HoursWeekApiRepository {
  final hoursApiProvider = HoursApiProvider();

  Future<List<HoursWeekResponseModel>?> hoursWeekApi({required BuildContext context, required String isoMonth}) async {
    final response = await hoursApiProvider.hoursWeekApiCall(context,isoMonth);

    if (response != null && response.data is List<dynamic>) {
        List<dynamic> dataList = response.data;
        List<HoursWeekResponseModel> hoursWeek = [];

        for (var item in dataList) {
          hoursWeek.add(HoursWeekResponseModel.fromJson(item));
        }

      return hoursWeek;
    } else {
      return null;
    }
  }
}

