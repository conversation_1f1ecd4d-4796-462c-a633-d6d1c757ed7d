import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursmonth_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/repository/hours_api_provider.dart';

import '../models/hoursweek_response_model.dart';

class HoursMonthApiRepository {
  final hoursApiProvider = HoursApiProvider();

  Future<List<HoursMonthResponseModel>?> hoursMonthApi(
      {required BuildContext context, required String iSOYearMonthStart, required String iSOYearMonthEnd}) async {
    final response = await hoursApiProvider.hoursMonthApiCall(context, iSOYearMonthStart, iSOYearMonthEnd);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<HoursMonthResponseModel> hoursMonth = [];

      for (var item in dataList) {
        hoursMonth.add(HoursMonthResponseModel.fromJson(item));
      }

      return hoursMonth;
    } else {
      return null;
    }
  }

  Future<List<HoursWeekResponseModel>?> hoursMonthWorkHourApi({
    required BuildContext context,
    required String iSOYearMonth,
  }) async {
    final response = await hoursApiProvider.hoursMonthWorkHourApiCall(context, iSOYearMonth);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<HoursWeekResponseModel> hoursMonth = [];

      for (var item in dataList) {
        hoursMonth.add(HoursWeekResponseModel.fromJson(item));
      }

      return hoursMonth;
    } else {
      return null;
    }
  }

  Future<List<HoursWeekResponseModel>?> hoursWeekWorkHourApi({
    required BuildContext context,
    required String iSOYearWeek,
  }) async {
    final response = await hoursApiProvider.hoursWeekWorkHourApiCall(context, iSOYearWeek);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<HoursWeekResponseModel> hoursMonth = [];

      for (var item in dataList) {
        hoursMonth.add(HoursWeekResponseModel.fromJson(item));
      }

      return hoursMonth;
    } else {
      return null;
    }
  }
}
