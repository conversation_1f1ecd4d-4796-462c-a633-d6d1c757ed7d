import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class AddTimeContainerWidget extends StatelessWidget {
  final double? height;
  final double? width;
  final Color? color;
  final double? borderRadius;
  final double? verticalPadding;
  final String? titleText;
  final String? titleValue;
  final TextStyle? titleTextStyle;
  final TextStyle? titleValueStyle;
  final Function()? onTap;
  const AddTimeContainerWidget(
      {Key? key,
      this.height,
      this.width,
      this.color,
      this.borderRadius,
      this.verticalPadding,
      this.titleText,
      this.titleValue,
      this.titleTextStyle,
      this.titleValueStyle,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height ?? AppSize.h70,
        width: width ?? AppSize.w80,
        decoration: BoxDecoration(
            color: color ?? AppColors.primaryColor, borderRadius: BorderRadius.circular(borderRadius ?? AppSize.r6)),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: verticalPadding ?? AppSize.h10),
          child: Column(
            children: [
              Text(titleText ?? '',
                  style: titleTextStyle ??
                      context.textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: AppSize.sp14,
                          letterSpacing: 1)),
              Spacer(),
              Text(titleValue ?? '',
                  style: titleValueStyle ??
                      context.textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: AppSize.sp14,
                          letterSpacing: 1)),
            ],
          ),
        ),
      ),
    );
  }
}
