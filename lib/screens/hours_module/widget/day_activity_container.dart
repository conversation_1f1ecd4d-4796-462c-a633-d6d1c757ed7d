import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../common/custom_widgets/spacebox.dart';
import '../../../utils/appsize.dart';

class DayActivityContainer extends StatelessWidget {
  final String? titleText;
  final String? valueText;
  const DayActivityContainer({Key? key, this.titleText, this.valueText})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(titleText ?? '',
            softWrap: true,
            style: context.textTheme.titleMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp11,
            )),
        SpaceV(AppSize.h2),
        Text(valueText ?? '-',
            softWrap: true,
            style: context.textTheme.titleMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp13,
            )),
      ],
    );
  }
}
