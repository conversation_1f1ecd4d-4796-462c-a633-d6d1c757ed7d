import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_activity_screen/ui/activity_list_screen.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class ActivitySelectWidget extends StatelessWidget {
  final String title;
  final String? time;
  const ActivitySelectWidget({Key? key, required this.title, this.time}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final result1 = await Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ActivitySearchListScreen(
                    selectedActivityValue: title,
                  )),
        ); // Handle the returned result (selected value)
        if (result1 != null) {
          //  hoursBloc.updateActivityValue(result1);
        }
      },
      child: Padding(
        padding: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10, left: AppSize.w14, right: AppSize.w14),
        child: Container(
          child: Row(
            children: [
              Text(
                time ?? '00:00',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.themeColors.textColor,
                  fontSize: AppSize.sp15,
                ),
              ),
              Spacer(),
              Row(
                children: [
                  Text(
                    title,
                    style: context.textTheme.headlineLarge?.copyWith(
                      fontSize: AppSize.sp14,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  SpaceH(AppSize.w4),
                  Icon(
                    Ionicons.caret_down,
                    color: context.themeColors.iconColor,
                    size: AppSize.sp12,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
