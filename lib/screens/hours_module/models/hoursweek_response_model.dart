class HoursWeekResponseModel {
  DateTime date;
  String isoDate;
  String? department;
  String? timeFrom;
  String? timeUntil;
  String? breakTime;
  String? costCenters;
  String? remark;

  HoursWeekResponseModel(
      {required this.date,
      required this.isoDate,
      this.department,
      this.timeFrom,
      this.timeUntil,
      this.breakTime,
      this.costCenters,
      this.remark});

  factory HoursWeekResponseModel.fromJson(Map<String, dynamic> json) => HoursWeekResponseModel(
        date: DateTime.parse(json["Date"]),
        isoDate: json["ISODate"],
        department: json['Department'] ?? "",
        timeFrom: json['TimeFrom'] ?? "",
        timeUntil: json['TimeUntil'] ?? "",
        breakTime: json['BreakTime'] ?? "",
        costCenters: json['CostCenters'] ?? "",
        remark: json['Remark'] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "Date": date.toIso8601String(),
        "ISODate": isoDate,
        "Department": department,
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "BreakTime": breakTime,
        "CostCenters": costCenters,
        "Remark": remark
      };
}
