class HoursResponseErrorModel {
  bool done;
  String? result;
  String? newGuid;
  String? newTitle;

  HoursResponseErrorModel({
  required   this.done,
     this.result,
     this.newGuid,
    this.newTitle,
  });

  factory HoursResponseErrorModel.fromJson(Map<String, dynamic> json) {
    return HoursResponseErrorModel(
      done: json['Done'] ?? false,
      result: json['Result'] ?? '',
      newGuid: json['NewGuid'] ?? '00000000-0000-0000-0000-000000000000',
      newTitle: json['NewTitle'],
    );
  }
}
