import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursmonth_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/repository/hours_month_repository.dart';

import '../../../common/common_functions/common_dateformat_function.dart';

part 'hours_state.dart';

class HoursCubit extends Cubit<HoursState> {
  HoursCubit() : super(HoursInitial());

  String selectedValue = 'Locatie S';
  String selectedId = 'c17238ad-5164-46e0-b443-6c921dd7746d';

  //activity search variable
  List<String> filteredActivity = [];
  List<String> selectedActivityList = [];
  String selectedActivity = 'Activity';
  List<String> filterActivityValue = [];

  //hoursmonth list
  List<HoursMonthResponseModel> hoursMonthList = [];
  List<HoursWeekResponseModel> hoursWorkMonthList = [];
  List<HoursWeekResponseModel> hoursWorkWeekList = [];
  ScrollController scrollController = ScrollController();
  ValueNotifier<int> initialFetchYear = ValueNotifier(2);
  ValueNotifier<bool> isLoading = ValueNotifier(true);
  ValueNotifier<bool> isHourLoading = ValueNotifier(true);
  ValueNotifier<bool> isWeekLoading = ValueNotifier(true);
  String workMonthHour = '';
  String workWeekHour = '';

  // String calculateTotal(String time1, String time2, String time3) {
  //   // Parse individual times and calculate the total here
  //   final List<String> parts1 = time1.split(':');
  //   final List<String> parts2 = time2.split(':');
  //   final List<String> parts3 = time3.split(':');
  //
  //   final int totalHours = int.parse(parts1[0]) + int.parse(parts2[0]) + int.parse(parts3[0]);
  //   final int totalMinutes = int.parse(parts1[1]) + int.parse(parts2[1]) + int.parse(parts3[1]);
  //
  //   final int carryHours = totalMinutes ~/ 60;
  //   final int remainingMinutes = totalMinutes % 60;
  //
  //   final String totalHoursStr = (totalHours + carryHours).toString().padLeft(2, '0');
  //   final String totalMinutesStr = remainingMinutes.toString().padLeft(2, '0');
  //   print("total ======>$totalHoursStr:$totalMinutesStr");
  //   return '$totalHoursStr:$totalMinutesStr';
  // }

  String getHoursFromMonth(int year, String monthName) {
    DateFormat monthFormat = DateFormat.MMMM();
    String month = monthFormat.parse(monthName).month.toString();

    String YearMonth = year.toString() + (month.length == 2 ? month : "0$month");

    String hours = "";

    hoursMonthList.forEach((element) {
      if (element.yearMonth.toString() == YearMonth) {
        hours = element.hours ?? '0';
      }
    });

    return hours;
  }

  void scrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.maxScrollExtent == scrollController.position.pixels) {
        print("object");
        initialFetchYear.value = initialFetchYear.value + 1;
      }
    });
    emit(HoursInitial());
  }

  Future<void> hoursMonthApiCall(
      {required BuildContext context,
      required String iSOYearMonthStart,
      required String iSOYearMonthEnd,
      bool isFirstTime = false}) async {
    print("api started =====>");
    if (isFirstTime) {
      isLoading.value = true;
    }

    final HoursMonthApiRepository hoursMonthApiRepository = HoursMonthApiRepository();
    final response = await hoursMonthApiRepository.hoursMonthApi(
        context: context, iSOYearMonthStart: iSOYearMonthStart, iSOYearMonthEnd: iSOYearMonthEnd);

    if (response!.isNotEmpty) {
      hoursMonthList.clear();
      hoursMonthList.addAll(response);
      print("aaa api done =====>${hoursMonthList.toList()}");
      DateTime today = DateTime.now();

      log("Current month number: ${today.year}${DateFormatFunctions.formatDay(today.month)}}");
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    if (isFirstTime) {
      isLoading.value = false;
    }
    emit(HoursInitial());
  }

  Future<void> hoursMonthWorkHourData(
      {required BuildContext context, required String iSOYearMonth, bool isFirstTime = false}) async {
    isHourLoading.value = true;

    final HoursMonthApiRepository hoursMonthApiRepository = HoursMonthApiRepository();
    final response = await hoursMonthApiRepository.hoursMonthWorkHourApi(context: context, iSOYearMonth: iSOYearMonth);
    print("api done =====>${response}");
    print("hoursMonthWorkHourData started =====>${response}");
    isHourLoading.value = false;

    if (response != null) {
      hoursWorkMonthList.clear();
      hoursWorkMonthList.addAll(response);

      workMonthHour = calculateTotalWorkedHours(hoursWorkMonthList);

      print("Total work hour: $workMonthHour");
    } else {}

    emit(HoursInitial());
  }

  Future<void> hoursWeekWorkHourData(
      {required BuildContext context, required String iSOWeek, bool isFirstTime = false}) async {
    isWeekLoading.value = true;

    final HoursMonthApiRepository hoursMonthApiRepository = HoursMonthApiRepository();
    final response = await hoursMonthApiRepository.hoursWeekWorkHourApi(context: context, iSOYearWeek: iSOWeek);
    print("api done =====>${response}");
    print("hoursMonthWorkHourData started =====>${response}");

    if (response != null) {
      hoursWorkWeekList.clear();
      hoursWorkWeekList.addAll(response);

      workWeekHour = calculateTotalWorkedHours(hoursWorkWeekList);

      print("Total week work hour: $workWeekHour");
    } else {}

    isWeekLoading.value = false;

    emit(HoursInitial());
  }

  updateActivityValue(String value) {
    selectedValue = value;
    emit(HoursInitial());
  }

  void filterActivity(String query, List<String> searchList) {
    filterActivityValue = searchList.where((element) => element.toLowerCase().contains(query.toLowerCase())).toList();
    emit(HoursInitial());

    print("filterValue=====>$filterActivityValue");
  }

  // String calculateTotalHours(
  //     {required DateTime date, required String timeFrom, required String timeUntil, required String breakTime}) {
  //   print("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 timeFrom ${timeFrom}");
  //   print("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 timeUntil ${timeUntil}");
  //   print("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 breakTime${breakTime}");

  //   if (timeFrom == "00:00" && timeUntil == "00:00") {
  //     // breakTime = "00:00";
  //     return "00:00";
  //   }

  //   if (timeUntil.isEmpty || timeUntil == "" || timeUntil == "00:00") {
  //     timeUntil = "00:00";
  //   }

  //   if (timeFrom.isEmpty || timeFrom == "" || timeFrom == "00:00") {
  //     timeFrom = "00:00";
  //   }

  //   if (breakTime.isEmpty || breakTime == "" || breakTime == "00:00") {
  //     breakTime = "00:00";
  //   }
  //   final timeFromParts = timeFrom.split(":");
  //   final timeUntilParts = timeUntil.split(":");
  //   final breakTimeParts = breakTime.split(":");

  //   DateTime dt1 = DateTime.parse(
  //       "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeUntil:00");

  //   DateTime dt2 = dt1.subtract(Duration(hours: int.parse(timeFromParts[0]), minutes: int.parse(timeFromParts[1])));

  //   DateTime dt3 = dt2.subtract(Duration(hours: int.parse(breakTimeParts[0]), minutes: int.parse(breakTimeParts[1])));

  //   // Format the result as "HH:mm"
  //   var hours = dt3.hour.toString();
  //   var minutes = dt3.minute.toString();

  //   final formattedHours = hours.toString().padLeft(2, '0');
  //   final formattedMinutes = minutes.toString().padLeft(2, '0');

  //   var formattedDifference = '$formattedHours:$formattedMinutes';
  //   print("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 breakTime${formattedDifference}");

  //   return formattedDifference;
  // }

  // String calculateTotalHours({
  //   required DateTime date,
  //   required String timeFrom,
  //   required String timeUntil,
  //   required String breakTime,
  // }) {
  //   log("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 timeFrom $timeFrom");
  //   log("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 timeUntil $timeUntil");
  //   log("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 breakTime $breakTime");
  //   log("timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 date $date");

  //   // If both timeFrom and timeUntil are "00:00", total hours should be "00:00"
  //   if (timeFrom == "00:00" && timeUntil == "00:00") {
  //     // breakTime = "00:00";
  //     return "00:00";
  //   }

  //   if (breakTime.isEmpty || breakTime == "" || breakTime == "00:00") {
  //     breakTime = "00:00";
  //   }

  //   final timeFromParts = timeFrom.split(":");
  //   final timeUntilParts = timeUntil.split(":");
  //   final breakTimeParts = breakTime.split(":");

  //   DateTime dt1 = DateTime.parse(
  //       "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeUntil:00");

  //   DateTime dt2 = dt1.subtract(Duration(hours: int.parse(timeFromParts[0]), minutes: int.parse(timeFromParts[1])));

  //   DateTime dt3 = dt2.subtract(Duration(hours: int.parse(breakTimeParts[0]), minutes: int.parse(breakTimeParts[1])));

  //   // Format the result as "HH:mm"
  //   var hours = dt3.hour.toString();
  //   var minutes = dt3.minute.toString();

  //   final formattedHours = hours.padLeft(2, '0');
  //   final formattedMinutes = minutes.padLeft(2, '0');

  //   var formattedDifference = '$formattedHours:$formattedMinutes';

  //   return formattedDifference;
  // }

  String calculateTotalHours({
    required DateTime date,
    required String timeFrom,
    required String timeUntil,
    required String breakTime,
  }) {
    print("timeFrom: $timeFrom");
    print("timeUntil: $timeUntil");
    print("breakTime: $breakTime");

    // Check if both timeFrom and timeUntil are valid
    if (timeFrom == "00:00" || timeUntil == "00:00") {
      return "00:00";
    }

    if (timeUntil.isEmpty || timeUntil == "" || timeUntil == "00:00") {
      timeUntil = "00:00";
    }

    if (timeFrom.isEmpty || timeFrom == "" || timeFrom == "00:00") {
      timeFrom = "00:00";
    }

    if (breakTime.isEmpty || breakTime == "" || breakTime == "00:00") {
      breakTime = "00:00";
    }

    // Parse time values
    final timeFromParts = timeFrom.split(":");
    final timeUntilParts = timeUntil.split(":");
    final breakTimeParts = breakTime.split(":");

    // Create DateTime object for timeUntil
    DateTime endDateTime = DateTime.parse(
        "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeUntil:00");

    // Calculate the start time by subtracting the timeFrom duration
    DateTime startDateTime =
        endDateTime.subtract(Duration(hours: int.parse(timeFromParts[0]), minutes: int.parse(timeFromParts[1])));

    // Subtract breakTime to get the final duration
    DateTime finalDateTime =
        startDateTime.subtract(Duration(hours: int.parse(breakTimeParts[0]), minutes: int.parse(breakTimeParts[1])));

    // Format the result as "HH:mm"
    String hours = finalDateTime.hour.toString().padLeft(2, '0');
    String minutes = finalDateTime.minute.toString().padLeft(2, '0');

    String formattedDifference = '$hours:$minutes';
    print("Total hours after breakTime: $formattedDifference");

    return formattedDifference;
  }

  String addTimes(String weekHours, String totalDayTime) {
    if (weekHours.isEmpty || weekHours == "00:00") {
      weekHours = "00:00";
    }

    if (totalDayTime.isEmpty || totalDayTime == "00:00") {
      totalDayTime = "00:00";
    }

    final weekHoursParts = weekHours.split(":");
    final totalDayTimeParts = totalDayTime.split(":");

    int hours = int.parse(weekHoursParts[0]) + int.parse(totalDayTimeParts[0]);
    int minutes = int.parse(weekHoursParts[1]) + int.parse(totalDayTimeParts[1]);

    if (minutes >= 60) {
      hours++;
      minutes = minutes - 60;
    }

    String totalWeeklyHours = '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';

    return totalWeeklyHours;
  }

  // String calculateTotalWorkedHours(List<HoursWeekResponseModel> hoursWorkMonthList) {
  //   var hours = '00'; // Initialize hours to '00'
  //   var minutes = '00'; // Initialize minutes to '00'
  //
  //   hoursWorkMonthList.forEach((element) {
  //     final timeFrom = element.timeFrom ?? "00:00";
  //     final timeUntil = element.timeUntil ?? "00:00";
  //     final breakTime = element.breakTime ?? "00:00";
  //     final workedTime = calculateTotalHours(
  //       date: element.date,
  //       timeFrom: timeFrom,
  //       timeUntil: timeUntil,
  //       breakTime: breakTime,
  //     );
  //
  //     final List<String> workedTimeParts = workedTime.split(':');
  //     int hoursPart = int.parse(workedTimeParts[0]);
  //     int minutesPart = int.parse(workedTimeParts[1]);
  //
  //     // Accumulate the hours and minutes
  //     hours = (int.parse(hours) + hoursPart).toString().padLeft(2, '0');
  //     minutes = (int.parse(minutes) + minutesPart).toString().padLeft(2, '0');
  //   });
  //
  //
  //   int hoursPart = int.parse(hours);
  //   int minutesPart = int.parse(minutes);
  //
  //   if(minutesPart >= 60){
  //     hoursPart++;
  //     minutesPart = minutesPart - 60;
  //   }
  //
  //
  //   return '$hoursPart:$minutesPart';
  // }

  String calculateTotalWorkedHours(List<HoursWeekResponseModel> hoursWorkMonthList) {
    var hours = 0;
    var minutes = 0;

    hoursWorkMonthList.forEach((element) {
      final timeFrom = element.timeFrom ?? "00:00";
      final timeUntil = element.timeUntil ?? "00:00";
      final breakTime = element.breakTime ?? "00:00";
      final workedTime = calculateTotalHours(
        date: element.date,
        timeFrom: timeFrom,
        timeUntil: timeUntil,
        breakTime: breakTime,
      );

      final List<String> workedTimeParts = workedTime.split(':');
      int hoursPart = int.parse(workedTimeParts[0]);
      int minutesPart = int.parse(workedTimeParts[1]);

      hours += hoursPart;
      minutes += minutesPart;
    });

    if (minutes >= 60) {
      hours += minutes ~/ 60;
      minutes %= 60;
    }

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }
}
