import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/bloc/hours_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/edit_time_sheet_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../utils/appsize.dart';
import 'hours_week/ui/hour_week_info_screen.dart';

class HoursScreen extends StatefulWidget {
  HoursScreen({super.key});

  @override
  State<HoursScreen> createState() => _HoursScreenState();
}

class _HoursScreenState extends State<HoursScreen> {
  List<String> monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];
  String getLocalizedMonthName(BuildContext context, String monthName) {
    switch (monthName.toLowerCase()) {
      case 'january':
        return AppLocalizations.of(context)!.january;
      case 'february':
        return AppLocalizations.of(context)!.february;
      case 'march':
        return AppLocalizations.of(context)!.march;
      case 'april':
        return AppLocalizations.of(context)!.april;
      case 'may':
        return AppLocalizations.of(context)!.may;
      case 'june':
        return AppLocalizations.of(context)!.june;
      case 'july':
        return AppLocalizations.of(context)!.july;
      case 'august':
        return AppLocalizations.of(context)!.august;
      case 'september':
        return AppLocalizations.of(context)!.september;
      case 'october':
        return AppLocalizations.of(context)!.october;
      case 'november':
        return AppLocalizations.of(context)!.november;
      case 'december':
        return AppLocalizations.of(context)!.december;
      default:
        return '';
    }
  }

  int currentYear = DateTime.now().year;
  int currentMonth = DateTime.now().month;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    final hourBloc = context.read<HoursCubit>();
    final date = DateTime.now();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await context.read<HoursCubit>().hoursMonthApiCall(
          context: context,
          iSOYearMonthStart:
              "${date.year - 100}${DateFormatFunctions.formatDay(date.month)}",
          iSOYearMonthEnd:
              "${date.year}${DateFormatFunctions.formatDay(date.month)}",
          isFirstTime: true);
    });

    hourBloc.scrollListener();
  }

  @override
  Widget build(BuildContext context) {
    final date = DateTime.now();
    return Scaffold(
      appBar:
          CustomAppBar(title: AppLocalizations.of(context)!.hoursAppbarText),
      body: RefreshIndicator(
        onRefresh: () {
          return context.read<HoursCubit>().hoursMonthApiCall(
              context: context,
              iSOYearMonthStart:
                  "${date.year - 100}${DateFormatFunctions.formatDay(date.month)}",
              iSOYearMonthEnd:
                  "${date.year}${DateFormatFunctions.formatDay(date.month)}",
              isFirstTime: true);
        },
        child: BlocBuilder<HoursCubit, HoursState>(
          builder: (context, state) {
            final hourBloc = context.read<HoursCubit>();
            return ValueListenableBuilder(
              valueListenable: hourBloc.initialFetchYear,
              builder:
                  (BuildContext context, int initialFetchYear, Widget? child) {
                return CustomScrollView(
                  controller: hourBloc.scrollController,
                  slivers: <Widget>[
                    for (int group = 0; group < initialFetchYear; group++)
                      SliverStickyHeader(
                        header: Container(
                          height: AppSize.h40,
                          color: context.themeColors.listGridColor1,
                          padding:
                              EdgeInsets.symmetric(horizontal: AppSize.w12),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            (currentYear - group).toString(),
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp15,
                            ),
                          ),
                        ),
                        sliver: SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (BuildContext context, int index) {
                              String monthName1 = monthNames[
                                  ((currentYear - group) == currentYear
                                      ? (currentMonth - 1) - index
                                      : ((monthNames.length - 1) - index))];

                              String monthName =
                                  getLocalizedMonthName(context, monthName1);

                              log("monthName $monthName");
                              log("monthName $monthName1");
                              // String monthName = getLocalizedMonthName(context, index)[
                              //     ((currentYear - group) == currentYear
                              //         ? (currentMonth - 1) - index
                              //         : ((monthNames.length - 1) - index))];

                              return ValueListenableBuilder(
                                valueListenable: hourBloc.isLoading,
                                builder: (BuildContext context, bool isLoading,
                                    Widget? child) {
                                  if (isLoading) {
                                    return ShimmerWidget(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                      height: AppSize.h10,
                                    );
                                  } else {
                                    return Column(
                                      children: [
                                        Material(
                                          color: Colors.transparent,
                                          child: Container(
                                            child: InkWell(
                                              onTap: () {
                                                print("ontap =====>");

                                                navigateNextScreen(
                                                    monthName1, group, context);
                                              },
                                              child: Container(
                                                color: context.themeColors
                                                    .homeContainerColor,
                                                child: Column(
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                        left: AppSize.w12,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            monthName,
                                                            style: context
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.copyWith(
                                                              color: context
                                                                  .themeColors
                                                                  .textColor,
                                                              fontSize:
                                                                  AppSize.sp15,
                                                            ),
                                                          ),
                                                          Spacer(),
                                                          // print here total month hours according year and month
                                                          Text(
                                                            hourBloc
                                                                .getHoursFromMonth(
                                                                    currentYear -
                                                                        group,
                                                                    monthName1),
                                                            style: context
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.copyWith(
                                                              color: context
                                                                  .themeColors
                                                                  .textColor,
                                                              fontSize:
                                                                  AppSize.sp15,
                                                            ),
                                                          ),
                                                          IconButton(
                                                            onPressed: () {
                                                              navigateNextScreen(
                                                                  monthName1,
                                                                  group,
                                                                  context);
                                                            },
                                                            icon: Icon(
                                                              Ionicons
                                                                  .chevron_forward_outline,
                                                              color: context
                                                                  .themeColors
                                                                  .greyColor,
                                                              size:
                                                                  AppSize.sp18,
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (group == initialFetchYear - 1 &&
                                            index == 11)
                                          Container(
                                            height: AppSize.h40,
                                            color: context
                                                .themeColors.listGridColor1,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: AppSize.w12),
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              (currentYear - group - 1)
                                                  .toString(),
                                              style: context
                                                  .textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: context
                                                    .themeColors.textColor,
                                                fontSize: AppSize.sp15,
                                              ),
                                            ),
                                          ),
                                      ],
                                    );
                                    // return Material(
                                    //   color: context.themeColors.mediumBlackColor,
                                    //   child: ListTile(
                                    //     title: Text(
                                    //       monthName,
                                    //       style: context.textTheme.headlineLarge
                                    //           ?.copyWith(fontSize: AppSize.sp15, fontWeight: FontWeight.normal),
                                    //     ),
                                    //     trailing: Row(
                                    //       mainAxisSize: MainAxisSize.min,
                                    //       children: [
                                    //         Text(
                                    //           hourBloc.getHoursFromMonth(currentYear - group, monthName),
                                    //           style: context.textTheme.bodyMedium?.copyWith(
                                    //             color: context.themeColors.textColor,
                                    //             fontSize: AppSize.sp15,
                                    //           ),
                                    //         ),
                                    //         SizedBox(
                                    //           width: AppSize.w10,
                                    //         ),
                                    //         Icon(
                                    //           Ionicons.chevron_forward_outline,
                                    //           color: context.themeColors.greyColor,
                                    //           size: AppSize.sp18,
                                    //         ),
                                    //       ],
                                    //     ),
                                    //     onTap: () {
                                    //       navigateNextScreen(monthName, group, context);
                                    //     },
                                    //     dense: true,
                                    //   ),
                                    // );
                                  }
                                },
                              );
                            },
                            childCount: currentYear - group == currentYear
                                ? currentMonth
                                : 12,
                          ),
                        ),
                      ),
                  ],
                );
              },
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
          key: UniqueKey(),
          onPressed: appDB.user?.AppFunctionRightsList
                      ?.contains(AppConstants.myHoursEditRights) ??
                  false
              ? () {
                  DateTime dateTime = DateTime.now();
                  String formattedDate =
                      DateFormat('EEEE dd MMMM', appDB.language)
                          .format(dateTime);
                  AppNavigation.nextScreen(
                      context,
                      EditTimeSheetScreen(
                          isFromFloatingButton: true,
                          dayTitle: formattedDate,
                          selectedYear: DateTime.now().year.toString()));
                }
              : () {
                  Future.delayed(
                    Duration(),
                    () {
                      customSnackBar(
                          context: context,
                          message: "U heeft geen toegang tot deze pagina");
                    },
                  )..then((value) {
                      persistentTabController.jumpToTab(newIndex = 0);
                    });
                },
          backgroundColor: AppColors.primaryColor,
          child: Icon(
            Icons.add,
            size: AppSize.sp24,
            color: AppColors.white,
          )),
    );
  }

  void navigateNextScreen(String monthName, int group, BuildContext context) {
    DateFormat monthFormat = DateFormat.MMMM();
    String month = monthFormat.parse(monthName).month.toString();

    String YearMonth = (currentYear - group).toString() +
        (month.length == 2 ? month : "0$month");

    AppNavigation.nextScreen(
        context,
        HoursWeekInfoScreen(
            monthYearName: "$monthName ${currentYear - group}",
            isoMonth: YearMonth));
  }
}
