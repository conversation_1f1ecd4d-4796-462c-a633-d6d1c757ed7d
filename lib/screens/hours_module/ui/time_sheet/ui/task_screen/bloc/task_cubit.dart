import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/task_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/task_list_repository.dart';

part 'task_state.dart';

class TaskCubit extends Cubit<TaskState> {
  TaskCubit() : super(TaskInitial());
  List<TaskResponseModel> taskList = [];
  String selectedTaskName = '';
  String selectedTaskId = '';
  List<TaskResponseModel> filterTaskList = [];
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  Future<void> setTaskData({required String selectedValue, required BuildContext context, required selectedId}) async {
    print("====> ${taskList}");

    filterTaskList = List.from(taskList);
    selectedTaskName = selectedValue;
    selectedTaskId = selectedId;
    emit(TaskInitial());
  }

  void filterTask(String query) {
    filterTaskList.clear();

    print('taskList: $taskList');
    filterTaskList =
        taskList.where((element) => (element.relation?.toLowerCase() ?? "").contains(query.toLowerCase())).toList();

    emit(TaskInitial());
  }

  Future<void> taskApiCall(
      {required BuildContext context, required String startIsoDate, required String endIsoDate}) async {
    print("api started =====>");
    final TaskListApiRepository taskListApiRepository = TaskListApiRepository();
    final response =
        await taskListApiRepository.taskListApi(context: context, startIsoDate: startIsoDate, endIsoDate: endIsoDate);
    print("api done2 =====>${response}");

    if (response!.isNotEmpty) {
      taskList.clear();
      filterTaskList.clear();
      taskList.addAll(response);
      filterTaskList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(TaskInitial());
  }
}
