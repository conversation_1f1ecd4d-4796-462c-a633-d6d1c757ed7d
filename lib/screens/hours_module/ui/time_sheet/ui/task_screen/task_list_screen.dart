import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/task_screen/bloc/task_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/widget/search_text_field.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class TaskScreen extends StatelessWidget {
  final String selectedTaskValue;
  final String selectedTaskId;

  TaskScreen(
      {Key? key, required this.selectedTaskValue, required this.selectedTaskId})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    context.read<TaskCubit>().setTaskData(
        selectedValue: selectedTaskValue,
        selectedId: selectedTaskId,
        context: context);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
        actions: true,
        isLeading: true,
        leading: GestureDetector(
            onTap: () {
              AppNavigation.previousScreen(context);
            },
            child: Icon(Ionicons.close_outline)),
        title: AppLocalizations.of(context)!.task,
      ),
      body: BlocBuilder<TaskCubit, TaskState>(
        builder: (ctx, state) {
          final taskBloc = ctx.read<TaskCubit>();

          return Column(
            children: [
              HourSearchTextField(
                onChanged: (value) {
                  print("value ======>$value");
                  taskBloc.filterTask(value);
                  print("filterTaskList: ${taskBloc.filterTaskList}");
                },
              ),
              ValueListenableBuilder(
                valueListenable: taskBloc.isLoading,
                builder: (BuildContext context, isLoading, Widget? child) {
                  return isLoading
                      ? Expanded(
                          child: ListView.builder(
                            itemCount: 15,
                            itemBuilder: (context, index) {
                              return ShimmerWidget(
                                margin: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 15),
                                height: AppSize.h20,
                              );
                            },
                          ),
                        )
                      : Expanded(
                          child: ListView.builder(
                            itemCount: taskBloc.filterTaskList.length,
                            itemBuilder: (context, index) {
                              final taskName = taskBloc.filterTaskList[index];
                              print("taskName: $taskName");
                              return GestureDetector(
                                onTap: () async {
                                  taskBloc.selectedTaskName =
                                      taskName.relation ?? "";
                                  taskBloc.selectedTaskId =
                                      taskName.calendarEntryId ?? "";
                                  print(
                                      "value ======>${taskBloc.selectedTaskId}");
                                  print(
                                      "value ======>${taskBloc.selectedTaskName}");

                                  var result = {
                                    "selectedTaskName":
                                        taskBloc.selectedTaskName,
                                    "selectedTaskId": taskBloc.selectedTaskId
                                  };

                                  Navigator.pop(context, result);
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      left: AppSize.w16, top: AppSize.h14),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: AppSize.w16,
                                        height: AppSize.w16,
                                        decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color:
                                                  taskBloc.selectedTaskName ==
                                                          taskName.relation
                                                      ? AppColors.primaryColor
                                                      : Colors.grey,
                                            ),
                                            color: taskBloc.selectedTaskName ==
                                                    taskName.relation
                                                ? AppColors.primaryColor
                                                : null),
                                        child: Center(
                                          child: taskBloc.selectedTaskName ==
                                                  taskName.relation
                                              ? Icon(
                                                  Icons.check,
                                                  size: AppSize.sp14,
                                                  color: Colors.white,
                                                )
                                              : null,
                                        ),
                                      ),
                                      SpaceH(AppSize.w20),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              taskName.relation.toString(),
                                              style: context
                                                  .textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: context
                                                    .themeColors.textColor,
                                                fontSize: AppSize.sp15,
                                              ),
                                            ),
                                            SpaceV(AppSize.h10),
                                            Container(
                                              color: context.themeColors
                                                  .dividerAvailbilityColor,
                                              width: double.infinity,
                                              height: AppSize.h1,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
