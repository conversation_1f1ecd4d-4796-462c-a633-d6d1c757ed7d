import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class TimeSheetHourList extends StatelessWidget {
  final int value;
  final ValueChanged<int> onValueChanged;

  TimeSheetHourList({required this.value, required this.onValueChanged, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final int minHour = 0; // Minimum hour value
    final List<int> hoursList = List.generate(24, (index) => minHour + index);

    return ListWheelScrollView(
      itemExtent: 50,
      perspective: 0.0000000001,
      useMagnifier: false,
      physics: FixedExtentScrollPhysics(),
      onSelectedItemChanged: (index) {
        final newValue = hoursList[index];
        onValueChanged(newValue);
      },
      children: List<Widget>.generate(hoursList.length, (index) {
        final number = hoursList[index].toString().padLeft(2, '0');
        final isSelected = value == hoursList[index];

        return Text(number,
            style: context.textTheme.bodyMedium?.copyWith(
              fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
              fontWeight: FontWeight.bold,
              color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
            ));
      }),
      controller: FixedExtentScrollController(initialItem: hoursList.indexOf(value)),
    );
  }
}
