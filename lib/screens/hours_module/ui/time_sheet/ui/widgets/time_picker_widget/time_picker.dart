import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/widgets/time_picker_widget/minute_list.dart';

import 'hour_list.dart';

class TimeSheetTimePicker extends StatefulWidget {
  final void Function(String) onOkPressed;
  final String selectedTime; // Selected time value as "hh:mm"
  final String initialTime; // Initial time value as "hh:mm"

  const TimeSheetTimePicker(
      {Key? key,
      required this.onOkPressed,
      required this.selectedTime,
      required this.initialTime})
      : super(key: key);

  @override
  _TimePickerState createState() => _TimePickerState();
}

class _TimePickerState extends State<TimeSheetTimePicker> {
  late ValueNotifier<int> selectedHour;
  late ValueNotifier<int> selectedMinute;
  int initialHour = 00;
  int initialMinute = 00;

  @override
  void initState() {
    super.initState();
    final initialTimeParts = widget.selectedTime.split(':');
    initialHour = int.parse(initialTimeParts[0]);
    initialMinute = int.parse(initialTimeParts[1]);

    selectedHour = ValueNotifier(initialHour);
    selectedMinute = ValueNotifier(initialMinute);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.334,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(AppLocalizations.of(context)!
                    .cancelText
                    .toUpperCase()
                    .toUpperCase()),
              ),
              TextButton(
                onPressed: () {
                  final hour = selectedHour.value.toString().padLeft(2, '0');
                  final minute =
                      selectedMinute.value.toString().padLeft(2, '0');
                  final selectedTime = "$hour:$minute";
                  widget.onOkPressed(selectedTime);
                  print("selectedTime =====>$selectedTime");
                  Navigator.pop(context, selectedTime);
                },
                child: Text(AppLocalizations.of(context)!.oK.toUpperCase()),
              ),
            ],
          ),
          Expanded(
            child: Row(
              children: [
                Spacer(flex: 3),
                Expanded(
                  flex: 1,
                  child: ValueListenableBuilder(
                    valueListenable: selectedHour,
                    builder: (context, value, child) {
                      return TimeSheetHourList(
                        value: selectedHour.value,
                        maxHours: initialHour,
                        onValueChanged: (newValue) {
                          if (initialMinute < selectedMinute.value &&
                              initialHour == newValue) {
                            selectedMinute.value = initialMinute;
                          }
                          selectedHour.value = newValue;
                        },
                      );
                    },
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: ValueListenableBuilder(
                      valueListenable: selectedHour,
                      builder: (context, value1, child) {
                        return ValueListenableBuilder(
                          valueListenable: selectedMinute,
                          builder: (context, value, child) {
                            return TimeSheetMinuteList(
                              value: selectedMinute.value,
                              onValueChanged: (newValue) {
                                selectedMinute.value = newValue;
                              },
                              selectedMinutes: initialMinute,
                            );
                          },
                        );
                      }),
                ),
                Spacer(flex: 3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
