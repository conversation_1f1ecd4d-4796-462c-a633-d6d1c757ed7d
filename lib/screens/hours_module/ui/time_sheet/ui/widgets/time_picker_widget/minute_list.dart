import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class TimeSheetMinuteList extends StatelessWidget {
  final int value; // The currently selected minute
  final ValueChanged<int> onValueChanged; // Callback to update the selected minute
  final int selectedMinutes;

  TimeSheetMinuteList({
    required this.value,
    required this.onValueChanged,
    Key? key,
    required this.selectedMinutes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int minutesToDisplay = 60;
    // (selectedHour == maxHours) ? minutesToDisplay = maxMinute+1 : 60;/

    return ListWheelScrollView(
      itemExtent: 50,
      perspective: 0.0000000001,
      useMagnifier: false,
      physics: FixedExtentScrollPhysics(),
      onSelectedItemChanged: (index) {
        onValueChanged(index);
        print(index);
      },
      children: List<Widget>.generate(minutesToDisplay, (index) {
        final minuteValue = index.toString().padLeft(2, '0');
        final isSelected = value == index;

        return Text(
          minuteValue,
          style: context.textTheme.bodyMedium?.copyWith(
            fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
            fontWeight: FontWeight.bold,
            color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
          ),
        );
      }),
      controller: FixedExtentScrollController(initialItem: selectedMinutes),
    );
  }
}
