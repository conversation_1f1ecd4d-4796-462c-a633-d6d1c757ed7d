
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/departmentlist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/department_list_repository.dart';

part 'department_state.dart';

class DepartmentCubit extends Cubit<DepartmentState> {
  DepartmentCubit() : super(DepartmentInitial());
  List<DepartmentListResponseModel> departmentList = [];
  String selectedDepartmentName = '';
  String selectedDepartmentId = '';
  List<DepartmentListResponseModel> filterDepartmentList = [];
  bool isDepartmentApiCalled = false;
  ValueNotifier<bool> isLoading = ValueNotifier(false);



  Future<void> setDepartmentData({required String selectedValue, required BuildContext context, required selectedId}) async {

    print("====> ${departmentList}");

    filterDepartmentList = List.from(departmentList);
    selectedDepartmentName =selectedValue;
    selectedDepartmentId =selectedId;
    emit(DepartmentInitial());
  }

  void filterDepartment(String query) {
    filterDepartmentList.clear();

    print('departmentList: $departmentList');
    filterDepartmentList = departmentList
        .where((element) => element.title.toLowerCase().contains(query.toLowerCase()))
        .toList();

    emit(DepartmentInitial());
  }

  Future<void> departmentApiCall({required BuildContext context}) async {
    print("api started =====>");
    final DepartmentListApiRepository departmentListApiRepository = DepartmentListApiRepository();
    final response = await departmentListApiRepository.departmentListApi(context: context);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      departmentList.clear();
      departmentList.addAll(response);
      filterDepartmentList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(DepartmentInitial());
  }


}


