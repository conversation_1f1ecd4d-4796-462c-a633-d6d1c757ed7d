import 'package:bloc/bloc.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_exclusions_list.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_list_repository.dart';

part 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  ActivityCubit() : super(ActivityInitial());

  List<ActivityListResponseModel> activityList = [];
  String selectedActivityName = '';
  String selectedActivityId = '';
  List<ActivityListResponseModel> filterActivityList = [];
  List<ActivityListResponseModel> timeBondFalseList = [];
  List<ActivityExclusionsListResponseModel> activityExclusionsList = [];
  List<Map<String, dynamic>> hierarchyList = []; // Simplified hierarchy data

  void fetchData({required String selectedValue, required String selectedId}) {
    filterActivityList = List.from(activityList);

    selectedActivityName = selectedValue;
    selectedActivityId = selectedId;
  }

  void filterActivity(String query) {
    filterActivityList.clear();
    filterActivityList = activityList
        .where((element) =>
            element.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
    emit(ActivityInitial());

    print("filterValue=====>$filterActivityList");
  }

  Future<void> costCentersActivityApiCall(
      {required BuildContext context,
      required String selectedDate,
      String? departmentId}) async {
    print("api started =====>");
    final ActivityListApiRepository activityListApiRepository =
        ActivityListApiRepository();
    final response = await activityListApiRepository.activityListApi(
        context: context, selectedDate: selectedDate);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      timeBondFalseList.clear();
      activityList.clear();

      print("Activity API response received: ${response.length} activities");
      print(
          "Activity Exclusions available: ${activityExclusionsList.length} exclusions");
      print("Department ID for filtering: $departmentId");

      // Parse selected date for date range validation
      DateTime selectedDate2 = DateTime.parse(
          "${selectedDate.substring(0, 4)}-${selectedDate.substring(4, 6)}-${selectedDate.substring(6)}");

      // Apply filtering logic: Valid=true AND not excluded AND within date range
      List<ActivityListResponseModel> filteredActivities = [];

      for (var element in response) {
        // Step 1: Check if Valid = true
        if (element.valid != true) {
          print("FILTERED OUT (Invalid): ${element.title}");
          continue;
        }

        // Step 1.5: Hide non-productive activities (requirement: non-productive hours should not be visible)
        if (element.productive != true) {
          print("FILTERED OUT (Non-productive): ${element.title}");
          continue;
        }

        // Step 1.6: Hide non-timebound activities (requirement: if timeBound:false we exclude it)
        if (element.timeBound != true) {
          print("FILTERED OUT (Non-timebound): ${element.title}");
          continue;
        }

        // Step 2: Check if excluded for this department or its parent organizational units
        bool isExcluded = false;
        if (departmentId != null && activityExclusionsList.isNotEmpty) {
          // Debug logging for the specific cost center you're investigating
          if (element.costCenterId == "d775f05e-c28f-4ddc-8e8c-bf75879908a9") {
            print(
                "=== DEBUG: Checking exclusion for ${element.title} (${element.costCenterId}) ===");
            print("Department ID: $departmentId");

            // Check if this is the "Locatie S" department the client mentioned
            if (departmentId == "c17238ad-5164-46e0-b443-6c921dd7746d") {
              print("*** THIS IS LOCATIE S - should be excluded! ***");
            } else {
              print(
                  "*** This is NOT Locatie S - checking different department ***");
            }
            print("Total exclusions loaded: ${activityExclusionsList.length}");
            print("Hierarchy relationships loaded: ${hierarchyList.length}");

            // Show all exclusions for this cost center
            var exclusionsForThisCostCenter = activityExclusionsList
                .where((e) => e.costCenterId == element.costCenterId)
                .toList();
            print(
                "Exclusions for this cost center: ${exclusionsForThisCostCenter.length}");
            exclusionsForThisCostCenter.forEach((exclusion) {
              print(
                  "  - Excluded for OrganisationalUnitId: ${exclusion.organisationalUnitId}");
            });

            // Show some sample hierarchy data to verify it's loaded correctly
            print("Sample hierarchy data (first 5 entries):");
            hierarchyList.take(5).forEach((item) {
              print(
                  "  ${item['OrganisationalUnitId']} -> ${item['ParentOrganisationalUnitId']}");
            });

            // Show department hierarchy
            Set<String> hierarchy = _getDepartmentHierarchy(departmentId);
            print("Department hierarchy: $hierarchy");

            // Check if the exclusion target is in our hierarchy data at all
            String targetOrgUnit = "2f089038-bd9e-4b81-88f6-bbb41a6022e4";
            bool foundInHierarchy = hierarchyList.any((item) =>
                item['OrganisationalUnitId'] == targetOrgUnit ||
                item['ParentOrganisationalUnitId'] == targetOrgUnit);
            print(
                "Target org unit $targetOrgUnit found in hierarchy data: $foundInHierarchy");
          }

          // Check if this cost center is excluded for the department or any of its parent organizational units
          isExcluded = _isActivityExcludedForDepartment(
              element.costCenterId, departmentId);

          if (isExcluded) {
            print(
                "FILTERED OUT (Hierarchical Exclusion): ${element.title} for department $departmentId");
            continue;
          } else if (element.costCenterId ==
              "d775f05e-c28f-4ddc-8e8c-bf75879908a9") {
            print(
                "=== DEBUG: ${element.title} was NOT excluded - allowing through ===");
          }
        }

        // Step 3: Check date range (StartDate <= selectedDate <= EndDate)
        bool isDateValid = (element.startDate.isBefore(selectedDate2) ||
                element.startDate.isAtSameMomentAs(selectedDate2)) &&
            (element.endDate.isAfter(selectedDate2) ||
                element.endDate.isAtSameMomentAs(selectedDate2));

        if (!isDateValid) {
          print(
              "FILTERED OUT (Date): ${element.title} - not active on ${selectedDate2.toIso8601String().split('T')[0]}");
          continue;
        }

        // If we get here, the activity passed all filters
        filteredActivities.add(element);
      }

      // Categorize filtered activities
      for (var element in filteredActivities) {
        if (element.timeBound == false) {
          // Non-timebound activities (both productive and non-productive)
          timeBondFalseList.add(element);
        } else if (element.timeBound == true) {
          // Timebound activities (both productive and non-productive)
          activityList.add(element);
        }
      }

      print("Activity filtering completed:");
      print("- Total received: ${response.length}");
      print("- After filtering: ${filteredActivities.length}");
      print("- Non-timebound: ${timeBondFalseList.length}");
      print("- Timebound: ${activityList.length}");
    } else {
      print("No activities returned from API");
    }
    emit(ActivityInitial());
  }

  Future<void> activityExclusionsApiCall(
      {required BuildContext context}) async {
    print("api started =====>");
    final ActivityExclusionsApiRepository activityExclusionsApiRepository =
        ActivityExclusionsApiRepository();
    final response = await activityExclusionsApiRepository
        .activityExclusionsListApi(context: context);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      activityExclusionsList.clear();
      activityExclusionsList.addAll(response);
      print(
          "=== EXCLUSIONS LOADED: ${activityExclusionsList.length} exclusions ===");

      // // Debug: Show exclusions for the specific cost center
      // var targetExclusions = activityExclusionsList
      //     .where(
      //         (e) => e.costCenterId == "d775f05e-c28f-4ddc-8e8c-bf75879908a9")
      //     .toList();
      // if (targetExclusions.isNotEmpty) {
      //   print(
      //       "Found ${targetExclusions.length} exclusions for cost center d775f05e-c28f-4ddc-8e8c-bf75879908a9:");
      //   targetExclusions.forEach((exclusion) {
      //     print("  - OrganisationalUnitId: ${exclusion.organisationalUnitId}");
      //   });
      // } else {
      //   print(
      //       "No exclusions found for cost center d775f05e-c28f-4ddc-8e8c-bf75879908a9");
      // }
    } else {
      print("=== NO EXCLUSIONS LOADED ===");
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(ActivityInitial());
  }

  /// Check if an activity is excluded for a department or any of its parent organizational units
  /// This implements hierarchical exclusion checking as described in the requirements
  bool _isActivityExcludedForDepartment(
      String costCenterId, String departmentId) {
    // Get all organizational units that this department belongs to (including itself)
    Set<String> departmentHierarchy = _getDepartmentHierarchy(departmentId);

    // Debug logging for the specific cost center
    if (costCenterId == "d775f05e-c28f-4ddc-8e8c-bf75879908a9") {
      print("=== EXCLUSION CHECK DETAILS ===");
      print("Cost Center ID: $costCenterId");
      print("Department ID: $departmentId");
      print("Department Hierarchy: $departmentHierarchy");

      // Check each exclusion individually
      for (var exclusion in activityExclusionsList) {
        if (exclusion.costCenterId == costCenterId) {
          print("Found exclusion for this cost center:");
          print(
              "  - Exclusion OrganisationalUnitId: ${exclusion.organisationalUnitId}");
          print(
              "  - Is in department hierarchy? ${departmentHierarchy.contains(exclusion.organisationalUnitId)}");

          if (departmentHierarchy.contains(exclusion.organisationalUnitId)) {
            print("  ✅ MATCH FOUND - Should be excluded!");
            return true;
          } else {
            print("  ❌ NO MATCH - Not excluded for this department");
          }
        }
      }
      print("=== END EXCLUSION CHECK ===");
    }

    // Check if the cost center is excluded for any organizational unit in the hierarchy
    return activityExclusionsList.any((exclusion) =>
        exclusion.costCenterId == costCenterId &&
        departmentHierarchy.contains(exclusion.organisationalUnitId));
  }

  /// Get all organizational unit IDs that a department belongs to (including itself)
  /// This traverses up the organizational hierarchy to find all parent units
  Set<String> _getDepartmentHierarchy(String departmentId) {
    Set<String> hierarchy = {
      departmentId
    }; // Always include the department itself

    if (hierarchyList.isEmpty) {
      print(
          "Warning: Hierarchy list is empty, only checking direct department exclusions");
      return hierarchy;
    }

    // Find all parent organizational units by traversing up the hierarchy
    String? currentId = departmentId;
    Set<String> visited = {departmentId}; // Prevent infinite loops

    int maxDepth = 10; // Prevent infinite loops with max depth
    int currentDepth = 0;

    while (currentId != null && currentDepth < maxDepth) {
      // Find the parent organizational unit for currentId
      Map<String, dynamic>? parentUnit = hierarchyList
          .where((unit) => unit['OrganisationalUnitId'] == currentId)
          .firstOrNull;

      if (parentUnit != null &&
          parentUnit['ParentOrganisationalUnitId'] != null) {
        String parentId = parentUnit['ParentOrganisationalUnitId'];
        if (!visited.contains(parentId)) {
          hierarchy.add(parentId);
          visited.add(parentId);
          print(
              "Found parent organizational unit: $parentId for $departmentId (depth: $currentDepth)");

          // Continue traversing up from this parent
          currentId = parentId;
          currentDepth++;
        } else {
          print("Circular reference detected or already visited: $parentId");
          break; // Prevent infinite loops
        }
      } else {
        print("No more parents found for $currentId");
        break; // No more parents found
      }
    }

    if (currentDepth >= maxDepth) {
      print(
          "Warning: Max hierarchy depth reached for department $departmentId");
    }

    print("Department $departmentId complete hierarchy: $hierarchy");
    return hierarchy;
  }

  /// Load organizational unit hierarchy data from API
  Future<void> loadOrganizationalUnitHierarchy(BuildContext context) async {
    try {
      final ActivityListApiRepository activityListApiRepository =
          ActivityListApiRepository();
      final response = await activityListApiRepository
          .organisationalUnitHierarchyApi(context: context);

      if (response != null && response.data != null) {
        hierarchyList.clear();

        // The API returns data in format: "DepartmentId;OrganisationalUnitId": 1
        // We need to convert this to our internal format
        Map<String, dynamic> hierarchyData = response.data;

        hierarchyData.forEach((key, value) {
          if (key.contains(';')) {
            List<String> parts = key.split(';');
            if (parts.length == 2) {
              String departmentId = parts[0]; // First part is DepartmentId
              String organisationalUnitId =
                  parts[1]; // Second part is OrganisationalUnitId (parent)

              hierarchyList.add({
                'OrganisationalUnitId': departmentId, // The department itself
                'ParentOrganisationalUnitId':
                    organisationalUnitId, // Its parent organizational unit
              });
            }
          }
        });

        print(
            "Loaded ${hierarchyList.length} hierarchy relationships from API");
        hierarchyList.forEach((item) {
          print(
              "Department ${item['OrganisationalUnitId']} -> Parent ${item['ParentOrganisationalUnitId']}");
        });
      } else {
        print("No hierarchy data received from API");
      }
    } catch (e) {
      print("Error loading organizational unit hierarchy: $e");
      // Fallback to empty hierarchy - will only check direct exclusions
      hierarchyList = [];
    }
  }
}
