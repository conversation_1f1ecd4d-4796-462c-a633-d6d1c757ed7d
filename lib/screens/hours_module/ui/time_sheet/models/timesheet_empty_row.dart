// class TimeSheetEmptyRowModel {
//   String? guid;
//   String personId;
//   String fullName;
//   String birthDate;
//   int age;
//   String dailyHours;
//   String dailyHoursSource;
//   dynamic vacationHours;
//   int? scheduledHours;
//   String personRosterStartTime;
//   String caoId;
//   int sequence;
//   String? personalBreakTime;
//   String date;
//   String isoDate;
//   String? holiday;
//   String departmentId;
//   String? hoursSource;
//   String? remark;
//   String? timeFrom;
//   String? timeUntil;
//   String? breakTime;
//   String? calendarEntryId;
//   String? accountEntryId1;
//   String? accountEntryId2;
//   String? accountEntryId3;
//   String accountEntryOrElseCostCenterId1;
//   String accountEntryOrElseCostCenterId2;
//   String accountEntryOrElseCostCenterId3;
//   String accountEntryOrElseCostCenterId4;
//   String tb1CostCenterId;
//   String? tb2CostCenterId;
//   String? tb3CostCenterId;
//   String? tb1Hours;
//   String? tb2Hours;
//   String? tb3Hours;
//   String? tb1Remark;
//   String? tb2Remark;
//   String? tb3Remark;
//   bool ntb1Checked;
//   bool ntb2Checked;
//   bool ntb3Checked;
//   bool ntb4Checked;
//   int? isSick;
//   int? isLeave;
//   int? temporary;
//   int? approved;
//   int? editRight;
//   String? command;
//
//   TimeSheetEmptyRowModel({
//     this.guid,
//     required this.personId,
//     required this.fullName,
//     required this.birthDate,
//     required this.age,
//     required this.dailyHours,
//     required this.dailyHoursSource,
//     required this.vacationHours,
//     this.scheduledHours,
//     required this.personRosterStartTime,
//     required this.caoId,
//     required this.sequence,
//     this.personalBreakTime,
//     required this.date,
//     required this.isoDate,
//     this.holiday,
//     required this.departmentId,
//     this.hoursSource,
//     this.remark,
//     this.timeFrom,
//     this.timeUntil,
//     this.breakTime,
//     this.calendarEntryId,
//     this.accountEntryId1,
//     this.accountEntryId2,
//     this.accountEntryId3,
//     required this.accountEntryOrElseCostCenterId1,
//     required this.accountEntryOrElseCostCenterId2,
//     required this.accountEntryOrElseCostCenterId3,
//     required this.accountEntryOrElseCostCenterId4,
//     required this.tb1CostCenterId,
//     this.tb2CostCenterId,
//     this.tb3CostCenterId,
//     this.tb1Hours,
//     this.tb2Hours,
//     this.tb3Hours,
//     this.tb1Remark,
//     this.tb2Remark,
//     this.tb3Remark,
//     required this.ntb1Checked,
//     required this.ntb2Checked,
//     required this.ntb3Checked,
//     required this.ntb4Checked,
//     this.isSick,
//     this.isLeave,
//     this.temporary,
//     this.approved,
//     this.editRight,
//     this.command,
//   });
//
//   factory TimeSheetEmptyRowModel.fromJson(Map<String, dynamic> json) => TimeSheetEmptyRowModel(
//         guid: json["guid"],
//         personId: json["PersonId"],
//         fullName: json["FullName"],
//         birthDate: json["BirthDate"],
//         age: json["Age"],
//         dailyHours: json["DailyHours"],
//         dailyHoursSource: json["DailyHoursSource"],
//         vacationHours: json["VacationHours"],
//         scheduledHours: json["ScheduledHours"],
//         personRosterStartTime: json["PersonRosterStartTime"],
//         caoId: json["CAOId"],
//         sequence: json["Sequence"],
//         personalBreakTime: json["PersonalBreakTime"],
//         date: json["Date"],
//         isoDate: json["ISODate"],
//         holiday: json["Holiday"],
//         departmentId: json["DepartmentId"],
//         hoursSource: json["HoursSource"],
//         remark: json["Remark"],
//         timeFrom: json["TimeFrom"],
//         timeUntil: json["TimeUntil"],
//         breakTime: json["BreakTime"],
//         calendarEntryId: json["CalendarEntryId"],
//         accountEntryId1: json["AccountEntryId1"],
//         accountEntryId2: json["AccountEntryId2"],
//         accountEntryId3: json["AccountEntryId3"],
//         accountEntryOrElseCostCenterId1: json["AccountEntryOrElseCostCenterId1"],
//         accountEntryOrElseCostCenterId2: json["AccountEntryOrElseCostCenterId2"],
//         accountEntryOrElseCostCenterId3: json["AccountEntryOrElseCostCenterId3"],
//         accountEntryOrElseCostCenterId4: json["AccountEntryOrElseCostCenterId4"],
//         tb1CostCenterId: json["tb1CostCenterId"],
//         tb2CostCenterId: json["tb2CostCenterId"],
//         tb3CostCenterId: json["tb3CostCenterId"],
//         tb1Hours: json["tb1Hours"],
//         tb2Hours: json["tb2Hours"],
//         tb3Hours: json["tb3Hours"],
//         tb1Remark: json["tb1Remark"],
//         tb2Remark: json["tb2Remark"],
//         tb3Remark: json["tb3Remark"],
//         ntb1Checked: json["ntb1Checked"],
//         ntb2Checked: json["ntb2Checked"],
//         ntb3Checked: json["ntb3Checked"],
//         ntb4Checked: json["ntb4Checked"],
//         isSick: json["IsSick"],
//         isLeave: json["IsLeave"],
//         temporary: json["Temporary"],
//         approved: json["Approved"],
//         editRight: json["EditRight"],
//         command: json["Command"],
//       );
//
//   Map<String, dynamic> toJson() => {
//         "guid": guid,
//         "PersonId": personId,
//         "FullName": fullName,
//         "BirthDate": birthDate,
//         "Age": age,
//         "DailyHours": dailyHours,
//         "DailyHoursSource": dailyHoursSource,
//         "VacationHours": vacationHours,
//         "ScheduledHours": scheduledHours,
//         "PersonRosterStartTime": personRosterStartTime,
//         "CAOId": caoId,
//         "Sequence": sequence,
//         "PersonalBreakTime": personalBreakTime,
//         "Date": date,
//         "ISODate": isoDate,
//         "Holiday": holiday,
//         "DepartmentId": departmentId,
//         "HoursSource": hoursSource,
//         "Remark": remark,
//         "TimeFrom": timeFrom,
//         "TimeUntil": timeUntil,
//         "BreakTime": breakTime,
//         "CalendarEntryId": calendarEntryId,
//         "AccountEntryId1": accountEntryId1,
//         "AccountEntryId2": accountEntryId2,
//         "AccountEntryId3": accountEntryId3,
//         "AccountEntryOrElseCostCenterId1": accountEntryOrElseCostCenterId1,
//         "AccountEntryOrElseCostCenterId2": accountEntryOrElseCostCenterId2,
//         "AccountEntryOrElseCostCenterId3": accountEntryOrElseCostCenterId3,
//         "AccountEntryOrElseCostCenterId4": accountEntryOrElseCostCenterId4,
//         "tb1CostCenterId": tb1CostCenterId,
//         "tb2CostCenterId": tb2CostCenterId,
//         "tb3CostCenterId": tb3CostCenterId,
//         "tb1Hours": tb1Hours,
//         "tb2Hours": tb2Hours,
//         "tb3Hours": tb3Hours,
//         "tb1Remark": tb1Remark,
//         "tb2Remark": tb2Remark,
//         "tb3Remark": tb3Remark,
//         "ntb1Checked": ntb1Checked,
//         "ntb2Checked": ntb2Checked,
//         "ntb3Checked": ntb3Checked,
//         "ntb4Checked": ntb4Checked,
//         "IsSick": isSick,
//         "IsLeave": isLeave,
//         "Temporary": temporary,
//         "Approved": approved,
//         "EditRight": editRight,
//         "Command": command,
//       };
// }
