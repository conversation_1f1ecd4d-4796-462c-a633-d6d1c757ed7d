class CostCentersNonTimeBoundPersonModel {
  String? ntbCcId1;
  String? ntbCcId2;
  String? ntbCcId3;
  String? ntbCcId4;
  String? ntbName1;
  String? ntbName2;
  String? ntbName3;
  String? ntbName4;
  String? ntbAbbreviation1;
  String? ntbAbbreviation2;
  String? ntbAbbreviation3;
  String? ntbAbbreviation4;

  CostCentersNonTimeBoundPersonModel({
    this.ntbCcId1,
    this.ntbCcId2,
    this.ntbCcId3,
    this.ntbCcId4,
    this.ntbName1,
    this.ntbName2,
    this.ntbName3,
    this.ntbName4,
    this.ntbAbbreviation1,
    this.ntbAbbreviation2,
    this.ntbAbbreviation3,
    this.ntbAbbreviation4,
  });

  factory CostCentersNonTimeBoundPersonModel.fromJson(Map<String, dynamic> json) => CostCentersNonTimeBoundPersonModel(
        ntbCcId1: json["ntbCCId1"],
        ntbCcId2: json["ntbCCId2"],
        ntbCcId3: json["ntbCCId3"],
        ntbCcId4: json["ntbCCId4"],
        ntbName1: json["ntbName1"],
        ntbName2: json["ntbName2"],
        ntbName3: json["ntbName3"],
        ntbName4: json["ntbName4"],
        ntbAbbreviation1: json["ntbAbbreviation1"],
        ntbAbbreviation2: json["ntbAbbreviation2"],
        ntbAbbreviation3: json["ntbAbbreviation3"],
        ntbAbbreviation4: json["ntbAbbreviation4"],
      );

  Map<String, dynamic> toJson() => {
        "ntbCCId1": ntbCcId1,
        "ntbCCId2": ntbCcId2,
        "ntbCCId3": ntbCcId3,
        "ntbCCId4": ntbCcId4,
        "ntbName1": ntbName1,
        "ntbName2": ntbName2,
        "ntbName3": ntbName3,
        "ntbName4": ntbName4,
        "ntbAbbreviation1": ntbAbbreviation1,
        "ntbAbbreviation2": ntbAbbreviation2,
        "ntbAbbreviation3": ntbAbbreviation3,
        "ntbAbbreviation4": ntbAbbreviation4,
      };
}
