class MyTimeSheetDataSaveModel {
  List<String> result;
  bool done;
  String newGuid;
  dynamic newTitle;
  dynamic redirectUrl;
  int statusCode;

  MyTimeSheetDataSaveModel(
      {required this.result,
      required this.done,
      required this.newGuid,
      required this.newTitle,
      required this.redirectUrl,
      required this.statusCode});

  factory MyTimeSheetDataSaveModel.fromJson(Map<String, dynamic> json, int statusCode) => MyTimeSheetDataSaveModel(
      result: List<String>.from(json["Result"].map((x) => x)),
      done: json["Done"],
      newGuid: json["NewGuid"],
      newTitle: json["NewTitle"],
      redirectUrl: json["RedirectURL"],
      statusCode: statusCode);

  Map<String, dynamic> toJson() => {
        "Result": List<dynamic>.from(result.map((x) => x)),
        "Done": done,
        "NewGuid": newGuid,
        "NewTitle": newTitle,
        "RedirectURL": redirectUrl,
      };
}
