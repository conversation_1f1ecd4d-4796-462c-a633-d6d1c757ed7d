class TaskResponseModel {
  String? calendarEntryId;
  DateTime? date;
  String? organisationalUnitId;
  String? id;
  String? relation;
  String? description;
  String? contactMedia;
  String? remark;
  String? timeFrom;
  String? timeUntil;
  int? amount;

  TaskResponseModel({
    this.calendarEntryId,
    this.date,
    this.organisationalUnitId,
    this.id,
    this.relation,
    this.description,
    this.contactMedia,
    this.remark,
    this.timeFrom,
    this.timeUntil,
    this.amount,
  });

  factory TaskResponseModel.fromJson(Map<String, dynamic> json) => TaskResponseModel(
        calendarEntryId: json["CalendarEntryId"],
        date: DateTime.parse(json["Date"]),
        organisationalUnitId: json["OrganisationalUnitId"],
        id: json["ID"],
        relation: json["Relation"],
        description: json["Description"],
        contactMedia: json["ContactMedia"],
        remark: json["Remark"],
        timeFrom: json["TimeFrom"],
        timeUntil: json["TimeUntil"],
        amount: json["Amount"],
      );

  Map<String, dynamic> toJson() => {
        "CalendarEntryId": calendarEntryId,
        "Date": date?.toIso8601String(),
        "OrganisationalUnitId": organisationalUnitId,
        "ID": id,
        "Relation": relation,
        "Description": description,
        "ContactMedia": contactMedia,
        "Remark": remark,
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "Amount": amount,
      };
}
