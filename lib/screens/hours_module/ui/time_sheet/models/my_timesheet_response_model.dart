class MyTimeSheetResponseModel {
  String? guid;
  String? personId;
  String? fullName;
  String? birthDate;
  int? age;
  String? dailyHours;
  String? dailyHoursSource;
  dynamic vacationHours;
  dynamic? scheduledHours;
  String? personRosterStartTime;
  String? caoId;
  int? sequence;
  String? personalBreakTime;
  String? date;
  String? isoDate;
  String? holiday;
  String? departmentId;
  String? hoursSource;
  String? remark;
  String? timeFrom;
  String? timeUntil;
  String? breakTime;
  String? calendarEntryId;
  String? accountEntryId1;
  String? accountEntryId2;
  String? accountEntryId3;
  String? accountEntryOrElseCostCenterId1;
  String? accountEntryOrElseCostCenterId2;
  String? accountEntryOrElseCostCenterId3;
  String? accountEntryOrElseCostCenterId4;
  String? tb1CostCenterId;
  String? tb2CostCenterId;
  String? tb3CostCenterId;
  String? tb1Hours;
  String? tb2Hours;
  String? tb3Hours;
  String? totalActivityHours;
  String? totalHours;
  String? tb1Remark;
  String? tb2Remark;
  String? tb3Remark;
  bool? tb1RemarkRequired;
  bool? tb2RemarkValidate;
  bool? tb3RemarkValidate;
  bool? ntb1Checked;
  bool? ntb2Checked;
  bool? ntb3Checked;
  bool? ntb4Checked;
  dynamic isSick;
  dynamic isLeave;
  dynamic temporary;
  dynamic approved;
  bool? editRight;
  String? command;
  bool? isError;

  MyTimeSheetResponseModel({
    this.guid,
    this.personId,
    this.fullName,
    this.birthDate,
    this.age,
    this.dailyHours,
    this.dailyHoursSource,
    this.vacationHours,
    this.scheduledHours,
    this.personRosterStartTime,
    this.caoId,
    this.sequence,
    this.personalBreakTime,
    this.date,
    this.isoDate,
    this.holiday,
    this.departmentId,
    this.hoursSource,
    this.remark,
    this.timeFrom,
    this.timeUntil,
    this.breakTime,
    this.calendarEntryId,
    this.accountEntryId1,
    this.accountEntryId2,
    this.accountEntryId3,
    this.accountEntryOrElseCostCenterId1,
    this.accountEntryOrElseCostCenterId2,
    this.accountEntryOrElseCostCenterId3,
    this.accountEntryOrElseCostCenterId4,
    this.tb1CostCenterId,
    this.tb2CostCenterId,
    this.tb3CostCenterId,
    this.tb1Hours,
    this.tb2Hours,
    this.tb3Hours,
    this.totalActivityHours,
    this.totalHours,
    this.tb1Remark,
    this.tb2Remark,
    this.tb3Remark,
    this.tb1RemarkRequired,
    this.tb2RemarkValidate,
    this.tb3RemarkValidate,
    this.ntb1Checked,
    this.ntb2Checked,
    this.ntb3Checked,
    this.ntb4Checked,
    this.isSick,
    this.isLeave,
    this.temporary,
    this.approved,
    this.editRight,
    this.command,
    this.isError,
  });

  factory MyTimeSheetResponseModel.fromJson(Map<String, dynamic> json) {
    dynamic editRightValue = json["EditRight"];
    bool? convertedEditRight = false;

    if (editRightValue is int) {
      convertedEditRight = (editRightValue == 1);
    } else if (editRightValue is bool) {
      convertedEditRight = editRightValue;
    }

    return MyTimeSheetResponseModel(
      guid: json["guid"],
      personId: json["PersonId"],
      fullName: json["FullName"],
      birthDate: json["BirthDate"],
      age: json["Age"],
      dailyHours: json["DailyHours"],
      dailyHoursSource: json["DailyHoursSource"],
      vacationHours: json["VacationHours"],
      scheduledHours: json["ScheduledHours"],
      personRosterStartTime: json["PersonRosterStartTime"],
      caoId: json["CAOId"],
      sequence: json["Sequence"],
      personalBreakTime: json["PersonalBreakTime"],
      date: json["Date"],
      isoDate: json["ISODate"],
      holiday: json["Holiday"],
      departmentId: json["DepartmentId"],
      hoursSource: json["HoursSource"],
      remark: json["Remark"],
      timeFrom: json["TimeFrom"],
      timeUntil: json["TimeUntil"],
      breakTime: json["BreakTime"],
      calendarEntryId: json["CalendarEntryId"],
      accountEntryId1: json["AccountEntryId1"],
      accountEntryId2: json["AccountEntryId2"],
      accountEntryId3: json["AccountEntryId3"],
      accountEntryOrElseCostCenterId1: json["AccountEntryOrElseCostCenterId1"],
      accountEntryOrElseCostCenterId2: json["AccountEntryOrElseCostCenterId2"],
      accountEntryOrElseCostCenterId3: json["AccountEntryOrElseCostCenterId3"],
      accountEntryOrElseCostCenterId4: json["AccountEntryOrElseCostCenterId4"],
      tb1CostCenterId: json["tb1CostCenterId"],
      tb2CostCenterId: json["tb2CostCenterId"],
      tb3CostCenterId: json["tb3CostCenterId"],
      tb1Hours: json["tb1Hours"],
      tb2Hours: json["tb2Hours"],
      tb3Hours: json["tb3Hours"],
      totalActivityHours: json["totalActivityHours"],
      totalHours: json["totalHours"],
      tb1Remark: json["tb1Remark"],
      tb2Remark: json["tb2Remark"],
      tb3Remark: json["tb3Remark"],
      ntb1Checked: json["ntb1Checked"],
      ntb2Checked: json["ntb2Checked"],
      ntb3Checked: json["ntb3Checked"],
      ntb4Checked: json["ntb4Checked"],
      isSick: json["IsSick"],
      isLeave: json["IsLeave"],
      temporary: json["Temporary"],
      approved: json["Approved"],
      editRight: convertedEditRight,
      command: json["Command"],
      isError: json["isError"],
    );
  }

  Map<String, dynamic> toJson() => {
        "guid": guid,
        "PersonId": personId,
        "FullName": fullName,
        "BirthDate": birthDate,
        "Age": age,
        "DailyHours": dailyHours,
        "DailyHoursSource": dailyHoursSource,
        "VacationHours": vacationHours,
        "ScheduledHours": scheduledHours,
        "PersonRosterStartTime": personRosterStartTime,
        "CAOId": caoId,
        "Sequence": sequence,
        "PersonalBreakTime": personalBreakTime,
        "Date": date,
        "ISODate": isoDate,
        "Holiday": holiday,
        "DepartmentId": departmentId,
        "HoursSource": hoursSource,
        "Remark": remark,
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "BreakTime": breakTime,
        "CalendarEntryId": calendarEntryId,
        "AccountEntryId1": accountEntryId1,
        "AccountEntryId2": accountEntryId2,
        "AccountEntryId3": accountEntryId3,
        "AccountEntryOrElseCostCenterId1": accountEntryOrElseCostCenterId1,
        "AccountEntryOrElseCostCenterId2": accountEntryOrElseCostCenterId2,
        "AccountEntryOrElseCostCenterId3": accountEntryOrElseCostCenterId3,
        "AccountEntryOrElseCostCenterId4": accountEntryOrElseCostCenterId4,
        "tb1CostCenterId": tb1CostCenterId,
        "tb2CostCenterId": tb2CostCenterId,
        "tb3CostCenterId": tb3CostCenterId,
        "tb1Hours": tb1Hours,
        "tb2Hours": tb2Hours,
        "tb3Hours": tb3Hours,
        "totalActivityHours": totalActivityHours,
        "totalHours": totalHours,
        "tb1Remark": tb1Remark,
        "tb2Remark": tb2Remark,
        "tb3Remark": tb3Remark,
        "ntb1Checked": ntb1Checked,
        "ntb2Checked": ntb2Checked,
        "ntb3Checked": ntb3Checked,
        "ntb4Checked": ntb4Checked,
        "IsSick": isSick,
        "IsLeave": isLeave,
        "Temporary": temporary,
        "Approved": approved,
        "EditRight": editRight,
        "Command": command,
        "isError": isError,
      };
}
