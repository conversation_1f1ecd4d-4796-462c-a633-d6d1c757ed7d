class ActivityExclusionsListResponseModel {
  String costCenterId;
  String organisationalUnitId;

  ActivityExclusionsListResponseModel({
    required this.costCenterId,
    required this.organisationalUnitId,
  });

  factory ActivityExclusionsListResponseModel.fromJson(Map<String, dynamic> json) =>
      ActivityExclusionsListResponseModel(
        costCenterId: json["CostCenterId"],
        organisationalUnitId: json["OrganisationalUnitId"],
      );

  Map<String, dynamic> toJson() => {
        "CostCenterId": costCenterId,
        "OrganisationalUnitId": organisationalUnitId,
      };
}
