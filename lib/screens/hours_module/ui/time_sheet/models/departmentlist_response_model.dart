class DepartmentListResponseModel {
  String guid;
  String title;
  bool timesheetRegistration;

  DepartmentListResponseModel({
    required this.guid,
    required this.title,
    required this.timesheetRegistration,
  });

  factory DepartmentListResponseModel.fromJson(Map<String, dynamic> json) => DepartmentListResponseModel(
    guid: json["guid"],
    title: json["Title"],
    timesheetRegistration: json["TimesheetRegistration"],
  );

  Map<String, dynamic> toJson() => {
    "guid": guid,
    "Title": title,
    "TimesheetRegistration": timesheetRegistration,
  };
}
