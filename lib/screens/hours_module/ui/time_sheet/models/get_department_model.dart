class GetDepartmentSettingModel {
  String? guid;
  String? title;
  bool? timesheetRegistration;
  List<BreakSetting>? breakSettings;
  bool? timesheetApproval;
  bool? timesheetProhibitApprovalPersonLevel;
  bool? userHasApprovalRight;
  bool? userHasResetApprovalRight;
  bool? timesheetBreakSettingTimeAdjustable;
  bool? timesheetProhibitOverlap;

  GetDepartmentSettingModel({
    this.guid,
    this.title,
    this.timesheetRegistration,
    this.breakSettings,
    this.timesheetApproval,
    this.timesheetProhibitApprovalPersonLevel,
    this.userHasApprovalRight,
    this.userHasResetApprovalRight,
    this.timesheetBreakSettingTimeAdjustable,
    this.timesheetProhibitOverlap,
  });

  factory GetDepartmentSettingModel.fromJson(Map<String, dynamic> json) => GetDepartmentSettingModel(
        guid: json["guid"],
        title: json["Title"],
        timesheetRegistration: json["TimesheetRegistration"],
        breakSettings: json["BreakSettings"] == null
            ? null
            : List<BreakSetting>.from(json["BreakSettings"].map((x) => BreakSetting.fromJson(x))),
        timesheetApproval: json["TimesheetApproval"],
        timesheetProhibitApprovalPersonLevel: json["TimesheetProhibitApprovalPersonLevel"],
        userHasApprovalRight: json["UserHasApprovalRight"],
        userHasResetApprovalRight: json["UserHasResetApprovalRight"],
        timesheetBreakSettingTimeAdjustable: json["TimesheetBreakSettingTimeAdjustable"],
        timesheetProhibitOverlap: json["TimesheetProhibitOverlap"],
      );

  Map<String, dynamic> toJson() => {
        "guid": guid,
        "Title": title,
        "TimesheetRegistration": timesheetRegistration,
        "BreakSettings": breakSettings == null ? null : List<dynamic>.from(breakSettings!.map((x) => x.toJson())),
        "TimesheetApproval": timesheetApproval,
        "TimesheetProhibitApprovalPersonLevel": timesheetProhibitApprovalPersonLevel,
        "UserHasApprovalRight": userHasApprovalRight,
        "UserHasResetApprovalRight": userHasResetApprovalRight,
        "TimesheetBreakSettingTimeAdjustable": timesheetBreakSettingTimeAdjustable,
        "TimesheetProhibitOverlap": timesheetProhibitOverlap,
      };
}

class BreakSetting {
  String? startTime;
  String? endTime;
  String? breakTime;

  BreakSetting({
    this.startTime,
    this.endTime,
    this.breakTime,
  });

  factory BreakSetting.fromJson(Map<String, dynamic> json) => BreakSetting(
        startTime: json["StartTime"],
        endTime: json["EndTime"],
        breakTime: json["BreakTime"],
      );

  Map<String, dynamic> toJson() => {
        "StartTime": startTime,
        "EndTime": endTime,
        "BreakTime": breakTime,
      };
}
