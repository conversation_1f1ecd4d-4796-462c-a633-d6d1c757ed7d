class ActivityListResponseModel {
  String costCenterId;
  String title;
  String abbreviation;
  bool productive;
  bool timeBound;
  DateTime startDate;
  DateTime endDate;
  CssClass cssClass;
  bool valid;
  List<NonStandard> nonStandard;
  int? sequence;
  bool isSelected;

  ActivityListResponseModel(
      {required this.costCenterId,
      required this.title,
      required this.abbreviation,
      required this.productive,
      required this.timeBound,
      required this.startDate,
      required this.endDate,
      required this.cssClass,
      required this.valid,
      required this.nonStandard,
      this.sequence,
      this.isSelected = false});

  factory ActivityListResponseModel.fromJson(Map<String, dynamic> json) => ActivityListResponseModel(
        costCenterId: json["CostCenterId"],
        title: json["Title"],
        abbreviation: json["Abbreviation"],
        productive: json["Productive"],
        timeBound: json["TimeBound"],
        startDate: DateTime.parse(json["StartDate"]),
        endDate: DateTime.parse(json["EndDate"]),
        cssClass: cssClassValues.map[json["CssClass"]]!,
        valid: json["Valid"],
        nonStandard: List<NonStandard>.from(json["NonStandard"].map((x) => NonStandard.fromJson(x))),
        sequence: json["Sequence"],
      );

  Map<String, dynamic> toJson() => {
        "CostCenterId": costCenterId,
        "Title": title,
        "Abbreviation": abbreviation,
        "Productive": productive,
        "TimeBound": timeBound,
        "StartDate": startDate.toIso8601String(),
        "EndDate": endDate.toIso8601String(),
        "CssClass": cssClassValues.reverse[cssClass],
        "Valid": valid,
        "NonStandard": List<dynamic>.from(nonStandard.map((x) => x.toJson())),
        "Sequence": sequence,
      };
}

enum CssClass { A_N_PR, A_PR }

final cssClassValues = EnumValues({"aNPr": CssClass.A_N_PR, "aPr": CssClass.A_PR});

class NonStandard {
  String departmentId;
  String startDate;
  String endDate;
  bool remarkRequired;

  NonStandard({
    required this.departmentId,
    required this.startDate,
    required this.endDate,
    required this.remarkRequired,
  });

  factory NonStandard.fromJson(Map<String, dynamic> json) => NonStandard(
        departmentId: json["DepartmentId"],
        startDate: json["StartDate"],
        endDate: json["EndDate"],
        remarkRequired: json["RemarkRequired"],
      );

  Map<String, dynamic> toJson() => {
        "DepartmentId": departmentId,
        "StartDate": startDate,
        "EndDate": endDate,
        "RemarkRequired": remarkRequired,
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
