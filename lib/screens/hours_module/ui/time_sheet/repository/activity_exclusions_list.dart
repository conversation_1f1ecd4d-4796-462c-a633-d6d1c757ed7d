import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class ActivityExclusionsApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<ActivityExclusionsListResponseModel>?> activityExclusionsListApi({
    required BuildContext context,
  }) async {
    final response = await timeSheetApiProvider.activityExclusionsListApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<ActivityExclusionsListResponseModel> activityExclusionsList = [];

      for (var item in dataList) {
        activityExclusionsList.add(ActivityExclusionsListResponseModel.fromJson(item));
      }

      return activityExclusionsList;
    } else {
      return null;
    }
  }
}
