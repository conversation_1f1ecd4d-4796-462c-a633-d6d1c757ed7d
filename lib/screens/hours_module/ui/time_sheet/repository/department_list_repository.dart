import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/departmentlist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class DepartmentListApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<DepartmentListResponseModel>?> departmentListApi({required BuildContext context}) async {
    final response = await timeSheetApiProvider.departmentListApiCall(context);

    if (response != null && response.data is List<dynamic>) {
        List<dynamic> dataList = response.data;
        List<DepartmentListResponseModel> activityList = [];

        for (var item in dataList) {
          activityList.add(DepartmentListResponseModel.fromJson(item));
        }

      return activityList;
    } else {
      return null;
    }
  }
}

