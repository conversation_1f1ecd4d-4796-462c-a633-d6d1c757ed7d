import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/task_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class TaskListApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<TaskResponseModel>?> taskListApi(
      {required BuildContext context, required String startIsoDate, required String endIsoDate}) async {
    final response = await timeSheetApiProvider.taskListApiCall(context, startIsoDate, endIsoDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<TaskResponseModel> taskList = [];

      for (var item in dataList) {
        taskList.add(TaskResponseModel.fromJson(item));
      }

      return taskList;
    } else {
      return null;
    }
  }
}
