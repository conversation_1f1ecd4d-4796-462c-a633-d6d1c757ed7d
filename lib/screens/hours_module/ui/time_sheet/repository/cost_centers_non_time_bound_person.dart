import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/cost_centers_non_time_bound_person.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class CostCentersNonTimeBoundPersonApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<CostCentersNonTimeBoundPersonModel>?> costCentersNonTimeBoundPersonApi(
      {required BuildContext context, required String startIsoDate, required String endIsoDate}) async {
    final response =
        await timeSheetApiProvider.costCentersNonTimeBoundPersonModelApiCall(context, startIsoDate, endIsoDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<CostCentersNonTimeBoundPersonModel> costCentersNonTimeBoundPersonList = [];

      for (var item in dataList) {
        costCentersNonTimeBoundPersonList.add(CostCentersNonTimeBoundPersonModel.fromJson(item));
      }

      return costCentersNonTimeBoundPersonList;
    } else {
      return null;
    }
  }
}
