import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class TimeSheetEmptyRowApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<MyTimeSheetResponseModel>?> timeSheetEmptyRowApi(
      {required BuildContext context, required String iSODate}) async {
    final response = await timeSheetApiProvider.timeSheetEmptyRowApiCall(context, iSODate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<MyTimeSheetResponseModel> myTimeSheet = [];

      for (var item in dataList) {
        myTimeSheet.add(MyTimeSheetResponseModel.fromJson(item));
      }

      return myTimeSheet;
    } else {
      return null;
    }
  }
}
