import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class MyTimeSheetApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<MyTimeSheetResponseModel>?> myTimeSheetApi({required BuildContext context, required isoStartDate}) async {
    final response = await timeSheetApiProvider.myTimeSheetApiCall(context, isoStartDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<MyTimeSheetResponseModel> myTimeSheet = [];

      for (var item in dataList) {
        myTimeSheet.add(MyTimeSheetResponseModel.fromJson(item));
      }

      List<MyTimeSheetResponseModel> myTimeSheet2 = myTimeSheet.where((element) {
        return element.timeFrom != null && element.timeFrom != "";
      }).toList();

      return myTimeSheet2;
    } else {
      return null;
    }
  }
}
