import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/get_department_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class GetDepartmentSettingApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<GetDepartmentSettingModel>?> getDepartmentSettingApi(
      {required BuildContext context, required String guid}) async {
    final response = await timeSheetApiProvider.getDepartmentSettingApiCall(context, guid: guid);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<GetDepartmentSettingModel> getDepartmentList = [];

      for (var item in dataList) {
        getDepartmentList.add(GetDepartmentSettingModel.fromJson(item));
      }

      return getDepartmentList;
    } else {
      return null;
    }
  }
}
