import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet-data_save.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class MyTimeSheetDataSaveApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<MyTimeSheetDataSaveModel?> timeSheetDataSaveApi(
      {required BuildContext context, required, required String myTimeSheetListJson}) async {
    final response = await timeSheetApiProvider.timeSheetDataSaveApiCall(context, myTimeSheetListJson);

    if (response != null) {
      return MyTimeSheetDataSaveModel.fromJson(response.data, response.statusCode!);
    } else {
      return null;
    }
  }
}
