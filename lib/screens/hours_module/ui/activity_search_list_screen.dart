// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:ionicons/ionicons.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
// import 'package:staff_medewerker/l10n/app_localizations.dart';
// import 'package:staff_medewerker/main.dart';
// import 'package:staff_medewerker/screens/hours_module/bloc/hours_cubit.dart';
//
// import '../../../common/custom_widgets/appbar_custom.dart';
// import '../../../common/custom_widgets/spacebox.dart';
// import '../../../utils/app_navigation/appnavigation.dart';
// import '../../../utils/appsize.dart';
// import '../../../utils/colors/app_colors.dart';
// import '../widget/search_text_field.dart';
//
// class ActivitySearchListScreen extends StatefulWidget {
//   final String? selectedActivityValue;
//   final int? index;
//
//   const ActivitySearchListScreen( {Key? key,this.index, this.selectedActivityValue}) : super(key: key);
//
//   @override
//   State<ActivitySearchListScreen> createState() => _ActivitySearchListScreenState();
// }
//
// class _ActivitySearchListScreenState extends State<ActivitySearchListScreen> {
//
//   final List<String> activity = [
//     'Bedrijfsrestaurant',
//     'Banqueting',
//     'Koffie bar',
//     'Extra Activiteiten',
//     'Automaten en vendingmachines',
//     'Ziek',
//     'Arbeidstherapeutisch werken',
//     'Cursus',
//     'Tijd voor tijd',
//     'Geboorteverlof',
//     'NOW',
//     'Aanvullend geboorteverlof',
//     'Ondernemingsraad',
//     'Wet Betaald Ouderschapsverlof',
//     'Vakantie'
//   ];
//
//
//   @override
//   void initState() {
//     final hourBloc = BlocProvider.of<HoursCubit>(context,listen: false);
// print("hourBloc.selectedActivity =======>${hourBloc.selectActivity}");
// print("hourBloc.selectedActivity =======>${widget.selectedActivityValue}");
// print("hourBloc.selectedActivity =======>${widget.index}");
// hourBloc.selectActivity = '';
//     hourBloc.filterActivityValue = activity;
//    // hourBloc.selectActivity = widget.selectedActivityValue ?? '';
//     super.initState();
//   }
//
//
//
//   Future<void> _navigateBackWithSelection(BuildContext context) async {
//     final hourBloc = BlocProvider.of<HoursCubit>(context,listen: false);
//
//     if (hourBloc.selectActivity != null) {
//       final result = {
//         'selectedValue': hourBloc.selectActivity,
//         'selectedIndex': widget.index??0,
//       };
//       Navigator.pop(context, result); // Return the selected value to the previous screen
//     }
//     print("previous value =====>${hourBloc.selectActivity}");
//   }
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: context.themeColors.homeContainerColor,
//       appBar: CustomAppBar(
//         actions: true,
//         isLeading: true,
//         leading: GestureDetector(
//             onTap: () {
//               AppNavigation.previousScreen(context);
//             },
//             child: Icon(Ionicons.close_outline)),
//         title: '${AppLocalizations.of(context)!.departmentText} ${widget.index??0 + 1}',
//       ),
//       body: BlocBuilder<HoursCubit, HoursState>(
//   builder: (ctx, state) {
//     final hoursBloc = ctx.read<HoursCubit>();
//
//     return Column(
//         children: [
//           HourSearchTextField(onChanged: (value) {
//             print("======>$value");
//              hoursBloc.filterActivity(value,activity);
//           },),
//           Expanded(
//             child: ListView.builder(
//               itemCount: hoursBloc.filterActivityValue.length,
//               itemBuilder: (context, index) {
//                 final activityName = hoursBloc.filterActivityValue[index];
//                 return GestureDetector(
//                   onTap: () {
//                     print("value ======>${ hoursBloc.selectActivity}");
//
//                     hoursBloc.selectActivity = activityName;
//
//                     // Navigate back to the previous screen with the selected value
//                     _navigateBackWithSelection(context);
//                   },
//                   child:Padding(
//                     padding: EdgeInsets.only(left: AppSize.w16, top: AppSize.h14),
//                     child: Row(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Container(
//                           width: AppSize.w16,
//                           height: AppSize.w16,
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             border: Border.all(
//                               color:hoursBloc.selectActivity ==
//                                   activityName ? AppColors.primaryColor : Colors.grey,
//                             ),
//                             color: hoursBloc.selectActivity ==
//                                 activityName ? AppColors.primaryColor : null,
//                           ),
//                           child: Center(
//                             child: hoursBloc.selectActivity ==
//                                 activityName
//                                 ? Icon(
//                               Icons.check,
//                               size: AppSize.sp14,
//                               color: Colors.white,
//                             )
//                                 : null,
//                           ),
//                         ),
//                         SpaceH(AppSize.w20),
//                         Expanded(
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Text(
//                                 activityName,
//                                 style: context.textTheme.bodyMedium?.copyWith(
//                                   color: context.themeColors.textColor,
//                                   fontSize: AppSize.sp15,
//                                 ),
//                               ),
//                               SpaceV(AppSize.h10),
//                               Container(
//                                 color: context.themeColors.dividerAvailbilityColor,
//                                 width: double.infinity,
//                                 height: AppSize.h1,
//                               ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 );
//               },
//             ),
//           ),
//         ],
//       );
//   },
// ),
//     );
//   }
// }
//
