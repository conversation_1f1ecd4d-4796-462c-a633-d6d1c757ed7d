import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/repository/hours_week_repository.dart';

part 'hours_week_state.dart';

class HoursWeekCubit extends Cubit<HoursWeekState> {
  HoursWeekCubit() : super(HoursWeekInitial());

  //hoursweek list
  List<HoursWeekResponseModel> hoursWeekList = [];
  Map<String, List<HoursWeekResponseModel>> allHoursWeekList = {};
  ValueNotifier<bool> isLoading = ValueNotifier(true);

  Future<void> hoursWeekApiCall(
      {required BuildContext context, required String isoMonth, required String monthYearName,bool refresh = false,}) async {
    print("monthYearName: $monthYearName");
    print(!allHoursWeekList.containsKey(monthYearName));
    print(allHoursWeekList);

    if (refresh || !allHoursWeekList.containsKey(monthYearName)) {
      isLoading.value = true;
      print("api started =====>");
      final HoursWeekApiRepository hoursWeekApiRepository = HoursWeekApiRepository();
      final response = await hoursWeekApiRepository.hoursWeekApi(context: context, isoMonth: isoMonth);
      print("api done =====>${response}");

      if (response!.isNotEmpty) {
        hoursWeekList.clear();
        hoursWeekList.addAll(response);
 allHoursWeekList[monthYearName] = response;
        // if (!allHoursWeekList.containsKey(monthYearName)) {
        //   allHoursWeekList.addAll({"$monthYearName": response});
        // }
        print(allHoursWeekList);
      } else {
        // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      }
      isLoading.value = false;
      emit(HoursWeekInitial());
    }
  }

  List<String> dayList = [];

  List<String> getDayNameFromWeekData(List<String> weekNameList, int group, Map<String, List<String>> weekData) {
    print("datatatatta" + weekNameList[group]);
    dayList = weekData[weekNameList[group]]!;

    List<String> reversedDayList = dayList.reversed.toList();
    print("dayList $reversedDayList");
    return reversedDayList;
  }

  String calculateTotalHours(
      {required DateTime date, required String timeFrom, required String timeUntil, required String breakTime}) {
    if (breakTime.isEmpty || breakTime == "" || breakTime == "00:00") {
      breakTime = "00:00";
    }

    if (timeUntil.isEmpty || timeUntil == "" || timeUntil == "00:00") {
      timeUntil = "00:00";
    }

    if (timeFrom.isEmpty || timeFrom == "" || timeFrom == "00:00") {
      timeFrom = "00:00";
    }

    List<String> timeFromParts = timeFrom.split(":");
    List<String> breakTimeParts = breakTime.split(":");

    DateTime dt1 = DateTime.parse(
        "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeUntil:00");

    DateTime dt2 = dt1.subtract(Duration(hours: int.parse(timeFromParts[0]), minutes: int.parse(timeFromParts[1])));

    DateTime dt3 = dt2.subtract(Duration(hours: int.parse(breakTimeParts[0]), minutes: int.parse(breakTimeParts[1])));

    // Format the result as "HH:mm"
    String hours = dt3.hour.toString();
    String minutes = dt3.minute.toString();

    String formattedHours = hours.toString().padLeft(2, '0');
    String formattedMinutes = minutes.toString().padLeft(2, '0');

    var formattedDifference = '$formattedHours:$formattedMinutes';

    return formattedDifference;
  }

  String addTimes(String weekHours, String totalDayTime) {
    if (weekHours.isEmpty || weekHours == "00:00") {
      weekHours = "00:00";
    }

    if (totalDayTime.isEmpty || totalDayTime == "00:00") {
      totalDayTime = "00:00";
    }

    List<String> weekHoursParts = weekHours.split(":");
    List<String> totalDayTimeParts = totalDayTime.split(":");

    int hours = int.parse(weekHoursParts[0]) + int.parse(totalDayTimeParts[0]);
    int minutes = int.parse(weekHoursParts[1]) + int.parse(totalDayTimeParts[1]);

    if (minutes >= 60) {
      hours++;
      minutes = minutes - 60;
    }

    String totalWeeklyHours = '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';

    return totalWeeklyHours;
  }
}
