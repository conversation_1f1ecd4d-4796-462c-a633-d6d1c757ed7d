import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:isoweek/isoweek.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_week/bloc/hours_week_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/time_data_display_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class HoursWeekInfoScreen extends StatefulWidget {
  final String monthYearName;
  final String isoMonth;

  const HoursWeekInfoScreen(
      {super.key, required this.monthYearName, required this.isoMonth});

  @override
  State<HoursWeekInfoScreen> createState() => _HoursWeekInfoScreenState();
}

class _HoursWeekInfoScreenState extends State<HoursWeekInfoScreen> {
  String getWeekday(int weekday) {
    switch (weekday) {
      case 1:
        return AppLocalizations.of(context)!.monday;
      case 2:
        return AppLocalizations.of(context)!.tuesday;
      case 3:
        return AppLocalizations.of(context)!.wednesday;
      case 4:
        return AppLocalizations.of(context)!.thursday;
      case 5:
        return AppLocalizations.of(context)!.friday;
      case 6:
        return AppLocalizations.of(context)!.saturday;
      case 7:
        return AppLocalizations.of(context)!.sunday;
      default:
        return '';
    }
  }

  String getMonth(int month) {
    switch (month) {
      case 1:
        return AppLocalizations.of(context)!.january;
      case 2:
        return AppLocalizations.of(context)!.february;
      case 3:
        return AppLocalizations.of(context)!.march;
      case 4:
        return AppLocalizations.of(context)!.april;
      case 5:
        return AppLocalizations.of(context)!.may;
      case 6:
        return AppLocalizations.of(context)!.june;
      case 7:
        return AppLocalizations.of(context)!.july;
      case 8:
        return AppLocalizations.of(context)!.august;
      case 9:
        return AppLocalizations.of(context)!.september;
      case 10:
        return AppLocalizations.of(context)!.october;
      case 11:
        return AppLocalizations.of(context)!.november;
      case 12:
        return AppLocalizations.of(context)!.december;
      default:
        return '';
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print("isoMonth: ${widget.isoMonth}");

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await context.read<HoursWeekCubit>().hoursWeekApiCall(
          context: context,
          isoMonth: widget.isoMonth,
          monthYearName: widget.monthYearName);
    });
  }

  DateTime currentDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "${widget.monthYearName}",
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: () {
          return context.read<HoursWeekCubit>().hoursWeekApiCall(
              refresh: true,
              context: context,
              isoMonth: widget.isoMonth,
              monthYearName: widget.monthYearName);
        },
        child: BlocBuilder<HoursWeekCubit, HoursWeekState>(
          builder: (context, state) {
            final hourBloc = context.read<HoursWeekCubit>();

            Map<String, List<String>> weekData = {};
            List<String> weekHoursList1 = [];

            List<HoursWeekResponseModel>? weekList =
                hourBloc.allHoursWeekList[widget.monthYearName];
            if (weekList != null) {
              for (int i = 0; i < weekList.length; i++) {
                final element = weekList[i];
                if (element.date.isBefore(currentDate)) {
                  Week currentWeek = Week.fromDate(element.date);
                  final String weekNumber = currentWeek.weekNumber.toString();

                  // Calculate totalTime by subtracting timeFrom, timeUntil, and breakTime
                  final String timeFrom = element.timeFrom ?? "00:00";
                  final String timeUntil = element.timeUntil ?? "00:00";
                  final String breakTime = element.breakTime ?? "00:00";

                  String totalDayTime = hourBloc.calculateTotalHours(
                    date: element.date,
                    timeFrom: timeFrom,
                    timeUntil: timeUntil,
                    breakTime: breakTime,
                  );

                  if (!weekData.containsKey("Week $weekNumber")) {
                    weekData["Week $weekNumber"] = [];
                  }

                  // final String formatDate = DateFormat("EEEE dd MMMM").format(element.date);
                  // print("formatDate: $formatDate");

                  // Fetch the localized weekday and month
                  String localizedWeekday = getWeekday(element.date.weekday);
                  String localizedMonth = getMonth(element.date.month);
                  final String localizedDate =
                      "$localizedWeekday ${element.date.day} $localizedMonth";

                  print("localizedDate: $localizedDate");

                  weekData["Week $weekNumber"]
                      ?.add("$localizedDate - $totalDayTime");

                  // weekData["Week $weekNumber"]?.add("$formatDate - $totalDayTime");
                }
              }
            }

            // Combine entries for the same day and calculate the total hours
            Map<String, String> weekTotals = {};
            weekHoursList1.clear();

            weekData.forEach((week, entries) {
              Map<String, String> dayTotals = {};

              entries.forEach((entry) {
                // Split the entry into date and time
                List<String> parts = entry.split(" - ");
                if (parts.length == 2) {
                  String date = parts[0];
                  String time = parts[1];
                  // Add the time to the total for the day
                  dayTotals[date] =
                      hourBloc.addTimes(dayTotals[date] ?? "00:00", time);
                }
              });

              // Combine day totals for the week
              List<String> combinedEntries = dayTotals.entries
                  .map((entry) => "${entry.key} - ${entry.value}")
                  .toList();

              weekData[week] = combinedEntries;

              String totalHours = "00:00";
              combinedEntries.forEach((entry) {
                List<String> parts = entry.split(" - ");
                if (parts.length == 2) {
                  String time = parts[1];
                  totalHours = hourBloc.addTimes(totalHours, time);
                }
              });

              weekTotals[week] = totalHours;
              weekHoursList1.add(totalHours);
              print("$week has total hour: $totalHours");
            });

            List<String> weekHoursList = weekHoursList1.toList();
            weekHoursList = weekHoursList.reversed.toList();

            print("weekHoursList: $weekHoursList");

            List<String> weekNameList = weekData.keys.toList();
            weekNameList = weekNameList.reversed.toList();

            print("weekData: $weekData");

            return ValueListenableBuilder(
              valueListenable: hourBloc.isLoading,
              builder: (BuildContext context, bool isLoading, Widget? child) {
                if (isLoading) {
                  return ListView.builder(
                    itemBuilder: (context, index) {
                      return ShimmerWidget(
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                        height: AppSize.h10,
                      );
                    },
                  );
                } else {
                  return CustomScrollView(
                    slivers: <Widget>[
                      for (int group = 0; group < weekData.length; group++)
                        SliverStickyHeader(
                          header: Builder(
                            builder: (context) {
                              return Container(
                                height: AppSize.h38,
                                color: context.themeColors.listGridColor1,
                                // color: AppColors.lightBlackColor,
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w12),
                                alignment: Alignment.centerLeft,
                                child: Row(
                                  children: [
                                    Text(
                                      '${weekNameList[group]}',
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                              color:
                                                  context.themeColors.textColor,
                                              fontSize: AppSize.sp15),
                                    ),
                                    Spacer(),
                                    Padding(
                                      padding:
                                          EdgeInsets.only(right: AppSize.sp40),
                                      child: Text(
                                        weekHoursList[group],
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                                color: context
                                                    .themeColors.textColor,
                                                fontSize: AppSize.sp15),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                              // return Material(
                              //   color: context.themeColors.listGridColor1,
                              //   child: ListTile(
                              //     title: Text(
                              //       '${weekNameList[group]}',
                              //       style: context.textTheme.bodyMedium
                              //           ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15),
                              //     ),
                              //     trailing: Text(
                              //       weekHoursList[group],
                              //       style: context.textTheme.bodyMedium
                              //           ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15),
                              //     ),
                              //     dense: true,
                              //   ),
                              // );
                            },
                          ),
                          sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (BuildContext context, int i) {
                                List<String> reversedDayList =
                                    hourBloc.getDayNameFromWeekData(
                                        weekNameList, group, weekData);
                                return InkWell(
                                  onTap: () {
                                    navigateNextScreen(i, weekList, context,
                                        reversedDayList, hourBloc);
                                  },
                                  child: Container(
                                    color:
                                        context.themeColors.homeContainerColor,
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(
                                            left: AppSize.w12,
                                          ),
                                          child: Row(
                                            children: [
                                              Text(
                                                  reversedDayList[i]
                                                      .split("-")[0],
                                                  style: context
                                                      .textTheme.bodyMedium
                                                      ?.copyWith(
                                                          color: context
                                                              .themeColors
                                                              .textColor,
                                                          fontSize:
                                                              AppSize.sp15)),
                                              Spacer(),
                                              reversedDayList[
                                                              i]
                                                          .split("-")[1]
                                                          .trim() !=
                                                      "00:00"
                                                  ? Text(
                                                      reversedDayList[i].split(
                                                          "-")[1],
                                                      style: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                              color: context
                                                                  .themeColors
                                                                  .textColor,
                                                              fontSize:
                                                                  AppSize.sp15))
                                                  : Container(),
                                              IconButton(
                                                  onPressed: () {
                                                    navigateNextScreen(
                                                        i,
                                                        weekList,
                                                        context,
                                                        reversedDayList,
                                                        hourBloc);
                                                  },
                                                  icon: Icon(
                                                    Ionicons
                                                        .chevron_forward_outline,
                                                    color: context
                                                        .themeColors.greyColor,
                                                    size: AppSize.sp18,
                                                  ))
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                                // return Material(
                                //   color: context.themeColors.homeContainerColor,
                                //   child: ListTile(
                                //     title: Text(reversedDayList[i].split("-")[0],
                                //         style: context.textTheme.bodyMedium
                                //             ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15)),
                                //     trailing: Row(
                                //       mainAxisSize: MainAxisSize.min,
                                //       children: [
                                //         reversedDayList[i].split("-")[1].trim() != "00:00"
                                //             ? Text(reversedDayList[i].split("-")[1],
                                //                 style: context.textTheme.bodyMedium?.copyWith(
                                //                     color: context.themeColors.textColor, fontSize: AppSize.sp15))
                                //             : Container(),
                                //         SizedBox(
                                //           width: AppSize.w10,
                                //         ),
                                //         Icon(
                                //           Ionicons.chevron_forward_outline,
                                //           color: context.themeColors.greyColor,
                                //           size: AppSize.sp18,
                                //         ),
                                //       ],
                                //     ),
                                //     onTap: () {
                                //       navigateNextScreen(i, weekList, context, reversedDayList, hourBloc);
                                //     },
                                //     dense: true,
                                //   ),
                                // );
                              },
                              childCount: weekData[weekNameList[group]]?.length,
                            ),
                          ),
                        ),
                    ],
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }

  void navigateNextScreen(
      int i,
      List<HoursWeekResponseModel>? weekList,
      BuildContext context,
      List<String> reversedDayList,
      HoursWeekCubit hourBloc) {
    print(i);
    print(weekList?[i].isoDate);
    // final formatYear = DateFormat("MMMM yyyy");
    // DateTime year = DateTime.parse(formatYear);

    DateTime date = DateFormat("MMMM yyyy").parse(widget.monthYearName);
    //datedate:trimRight Monday 23 September
    print(
        "datedate:trimRight ${hourBloc.allHoursWeekList[widget.monthYearName]!}");
    hourBloc.allHoursWeekList[widget.monthYearName]?.forEach((element) {
      log("element ${element.timeFrom}");
      log("element ${element.timeUntil}");
    });

    AppNavigation.nextScreen(
        context,
        TimeDataDisplayScreen(
            dayNameTitle: reversedDayList[i].split("-")[0].trimRight(),
            monthlyData: hourBloc.allHoursWeekList[widget.monthYearName]!,
            selectedYear: date.year.toString()));
  }
}
