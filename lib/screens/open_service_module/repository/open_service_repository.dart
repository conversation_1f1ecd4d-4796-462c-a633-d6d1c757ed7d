import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/open_service_module/model/my_service_per_month.dart';
import 'package:staff_medewerker/screens/open_service_module/model/toggle_service_model.dart';

import 'open_service_provider.dart';

class OpenServiceApiRepository {
  final openServiceProvider = OpenServiceApiProvider();
  Future<List<MyServicesPerMonthResponseModel>?> myServicesPerMonthApi(
      {required BuildContext context, required String iosYearMonth}) async {
    final response = await openServiceProvider.myServicesPerMonthApiCall(context, iosYearMonth);

    if (response != null && response.data is List<dynamic>) {
      List<MyServicesPerMonthResponseModel> myServicesPerWeekDetail = [];

      for (var item in response.data) {
        myServicesPerWeekDetail.add(MyServicesPerMonthResponseModel.fromJson(item, response.statusCode ?? 0));
      }

      return myServicesPerWeekDetail;
    } else {
      return null;
    }
  }

  Future<Response?> setSwapAndServiceApi(
      {required BuildContext context,
      required String personId,
      required String dateEntryId,
      required String stateId,
      required String nextStateId,
      required String remark}) async {
    final response = await openServiceProvider.setSwapAndServiceApiCall(
      context,
      personId,
      dateEntryId,
      stateId,
      nextStateId,
      remark,
    );

    return response;
  }

  Future<ToggleMyOpenServiceSubscriptionResponseModel?> toggleMyOpenServiceSubscriptionApi(
      {required BuildContext context,
      required String personId,
      required String baseServiceId,
      required String departmentId,
      required String date}) async {
    final response = await openServiceProvider.toggleMyOpenServiceSubscriptionApiCall(
        context, personId, baseServiceId, departmentId, date);

    if (response != null) {
      return ToggleMyOpenServiceSubscriptionResponseModel.fromJson(response.data, response.statusCode!);
    } else {
      return null;
    }
  }

  Future<Response?> cancelSwapAndServiceApi({
    required BuildContext context,
    required String personId,
    required String dateEntryId,
    required String stateId,
    required String nextStateId,
  }) async {
    final response = await openServiceProvider.cancelSwapAndServiceApiCall(
      context,
      personId,
      dateEntryId,
      stateId,
      nextStateId,
    );

    return response;
  }
}
