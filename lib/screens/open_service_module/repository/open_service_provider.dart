import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../main.dart';
import '../../../service/api_service/api_function.dart';
import '../../../service/api_service/server_constants.dart';

class OpenServiceApiProvider {
  Future<Response?> myServicesPerMonthApiCall(BuildContext context, String selectDate) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonth": selectDate
      };
      log("response myServicesPerMonthApiCall ${query}");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.scheduleSwapAndOpenMonth,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      log("response myServicesPerMonthApiCall ${json.encode(response.data)}");
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> setSwapAndServiceApiCall(BuildContext context, String personId, String dateEntryId, String stateId,
      String nextStateId, String? remark) async {
    try {
      final query = {
        "ChangeServiceSwapState": {
          "PersonId": personId,
          "DateEntryId": dateEntryId,
          "StateId": stateId,
          "NextStateId": nextStateId,
          "Remark": remark
        },
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };
      print("response =======>$personId");
      print("response =======>$dateEntryId");
      print("response =======>$stateId");
      print("response =======>$nextStateId");
      print("response =======>$remark");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.scheduleSetSwapService,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> toggleMyOpenServiceSubscriptionApiCall(
      BuildContext context, String personId, String baseServiceId, String departmentId, String date) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "OpenServiceInfo": {
          "PersonId": personId,
          "BaseServiceId": baseServiceId,
          "DepartmentId": departmentId,
          "Date": date
        }
      };

      print("query toggleMyOpenServiceSubscriptionApiCall =======>$query");
      print("response =======>$personId");
      print("response =======>$baseServiceId");
      print("response =======>$departmentId");
      print("response =======>$date");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.toggleMyOpenServiceSubscription,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> cancelSwapAndServiceApiCall(
    BuildContext context,
    String personId,
    String dateEntryId,
    String stateId,
    String nextStateId,
  ) async {
    try {
      final query = {
        "ChangeServiceSwapState": {
          "PersonId": personId,
          "DateEntryId": dateEntryId,
          "StateId": stateId,
          "NextStateId": nextStateId,
        },
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };
      print("response =======>$personId");
      print("response =======>$dateEntryId");
      print("response =======>$stateId");
      print("response =======>$nextStateId");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.scheduleSetSwapService,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
