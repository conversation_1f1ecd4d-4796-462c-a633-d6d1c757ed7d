import 'package:flutter/cupertino.dart';

import '../../../../common/custom_widgets/shimmer_effect.dart';
import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';

class OpenServiceMonthShimmerWidget extends StatelessWidget {
  const OpenServiceMonthShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Column(
        children: [
          Expanded(
              child: ListView.builder(
            itemBuilder: (context, index) {
              return Column(
                children: [
                  Container(
                    height: AppSize.h50,
                    color: AppColors.white,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: AppSize.h4, bottom: AppSize.h10),
                    child: Container(
                      margin: EdgeInsets.only(
                        top: AppSize.h6,
                      ),
                      height: AppSize.h30,
                      color: AppColors.white,
                    ),
                  ),
                ],
              );
            },
            itemCount: 10,
          ))
        ],
      ),
    );
  }
}
