class ToggleMyOpenServiceSubscriptionResponseModel {
  bool done;
  List<dynamic> result;
  String newGuid;
  int statusCode;

  ToggleMyOpenServiceSubscriptionResponseModel(
      {required this.done, required this.result, required this.newGuid, required this.statusCode});

  factory ToggleMyOpenServiceSubscriptionResponseModel.fromJson(Map<String, dynamic> json, int statusCode) =>
      ToggleMyOpenServiceSubscriptionResponseModel(
          done: json["Done"], result: json["Result"], newGuid: json["NewGuid"], statusCode: statusCode);

  Map<String, dynamic> toJson() => {
        "Done": done,
        "Result": result,
        "NewGuid": newGuid,
      };
}
