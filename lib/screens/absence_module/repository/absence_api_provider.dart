import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class AbsenceApiProvider {
  Future<Response?> myBalancesOverviewApiCall(BuildContext context, String selectedDate) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISODate": selectedDate
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.myBalancesOverview,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> timesheetLeaveInYearApiCall(BuildContext context, String selectedYear) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "Year": selectedYear
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.timesheetLeaveInYear,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> leaveTypeApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.leaveType,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> myHoursPeriodApiCall(BuildContext context, String startDate, String endDate) async {
    print(startDate);
    print(endDate);

    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOStartDate": startDate,
        "ISOEndDate": endDate
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.myHoursPeriods,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> submitLeaveApiCall(BuildContext context, String startDate, String endDate,
      String selectedLeaveTypeGuid, String workedHours, String leaveReason) async {
    print(startDate);
    print(endDate);

    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOStartDate": startDate,
        "ISOEndDate": endDate,
        "Hours": workedHours,
        "Reason": leaveReason,
        "LeaveTypeId": selectedLeaveTypeGuid,
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.timesheetInsertLeaveRequest,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
