import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/absence_module/repository/absence_api_provider.dart';
import 'package:staff_medewerker/screens/absence_module/repository/timesheet_leave_InYear/timesheet_leave_model.dart';

class TimesheetLeaveInYearApiRepository {
  final absenceProvider = AbsenceApiProvider();

  Future<List<TimeSheetLeaveInYearModel>?> timesheetLeaveInYearApi({required BuildContext context, required String selectedYear}) async {
    final response = await absenceProvider.timesheetLeaveInYearApiCall(context,selectedYear);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<TimeSheetLeaveInYearModel> timesheetLeaveInYear = [];

      for (var item in dataList) {
        timesheetLeaveInYear.add(TimeSheetLeaveInYearModel.fromJson(item));
      }

      return timesheetLeaveInYear;
    } else {
      return null;
    }
  }
}