import 'dart:convert';

List<TimeSheetLeaveInYearModel> timeSheetLeaveInYearModelFromJson(String str) => List<TimeSheetLeaveInYearModel>.from(json.decode(str).map((x) => TimeSheetLeaveInYearModel.fromJson(x)));

String timeSheetLeaveInYearModelToJson(List<TimeSheetLeaveInYearModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TimeSheetLeaveInYearModel {
  String? guid;
  String? soortVerlof;
  DateTime? begin;
  DateTime? einde;
  double? urenAangevraagd;
  String? toestand;
  String? reden;
  DateTime? invoerdatum;
  double? urenGeregistreerd;
  bool? isApiCallDone;

  TimeSheetLeaveInYearModel({
     this.guid,
     this.soortVerlof,
     this.begin,
      this.einde,
     this.urenAangevraagd,
     this.toestand,
     this.reden,
     this.invoerdatum,
    this.urenGeregistreerd,
    this.isApiCallDone = false
  });

  factory TimeSheetLeaveInYearModel.fromJson(Map<String, dynamic> json) => TimeSheetLeaveInYearModel(
    guid: json["guid"],
    soortVerlof: json["Soort verlof"],
    begin: DateTime.parse(json["Begin"]),
    einde: DateTime.parse(json["Einde"]),
    urenAangevraagd: json["Uren aangevraagd"]?.toDouble(),
    toestand: json["Toestand"],
    reden: json["Reden"],
    invoerdatum: DateTime.parse(json["Invoerdatum"]),
    urenGeregistreerd: json["Uren geregistreerd"],
  );

  Map<String, dynamic> toJson() => {
    "guid": guid,
    "Soort verlof": soortVerlof,
    "Begin": begin?.toIso8601String(),
    "Einde": einde?.toIso8601String(),
    "Uren aangevraagd": urenAangevraagd,
    "Toestand": toestand,
    "Reden": reden,
    "Invoerdatum": invoerdatum?.toIso8601String(),
    "Uren geregistreerd": urenGeregistreerd,
  };
}
