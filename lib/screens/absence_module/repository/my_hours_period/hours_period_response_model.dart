class MyHoursInPeriodResponseModel {
  int done;
  String result;
  int statusCode;

  MyHoursInPeriodResponseModel({
    required this.done,
    required this.result,
    required this.statusCode
  });

  factory MyHoursInPeriodResponseModel.fromJson(Map<String, dynamic> json, int statusCode) => MyHoursInPeriodResponseModel(
    done: json["Done"],
    result: json["Result"],
    statusCode: statusCode
  );

  Map<String, dynamic> toJson() => {
    "Done": done,
    "Result": result,
  };
}
