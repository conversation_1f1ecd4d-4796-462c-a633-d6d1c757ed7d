import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/absence_module/repository/absence_api_provider.dart';
import 'package:staff_medewerker/screens/absence_module/repository/my_hours_period/hours_period_response_model.dart';


class MyHoursPeriodApiRepository {
  final absenceProvider = AbsenceApiProvider();

  Future<MyHoursInPeriodResponseModel?> myHoursPeriodApi({required BuildContext context, required String startDate, required String endDate}) async {
    final response = await absenceProvider.myHoursPeriodApiCall(context,startDate,endDate);

    if (response != null) {
      return MyHoursInPeriodResponseModel.fromJson(response.data,response.statusCode!);
    } else {
      return null;
    }
  }
}
 