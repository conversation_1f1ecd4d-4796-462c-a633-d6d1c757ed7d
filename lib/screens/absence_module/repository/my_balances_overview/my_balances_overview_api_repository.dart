import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/absence_module/repository/absence_api_provider.dart';
import 'package:staff_medewerker/screens/absence_module/repository/my_balances_overview/balance_overview_model.dart';

class MyBalancesOverviewRepository {
  final absenceProvider = AbsenceApiProvider();

  Future<List<MyBalancesOverviewModel>?> myBalancesOverviewApi({required BuildContext context, required String selectedDate}) async {
    final response = await absenceProvider.myBalancesOverviewApiCall(context,selectedDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<MyBalancesOverviewModel> myBalancesOverview = [];

      for (var item in dataList) {
        myBalancesOverview.add(MyBalancesOverviewModel.fromJson(item));
      }

      return myBalancesOverview;
    } else {
      return null;
    }
  }
}