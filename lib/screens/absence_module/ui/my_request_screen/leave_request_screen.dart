import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/screens/profile_module/widget/common/common_info_row.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class LeaveRequestScreen extends StatelessWidget {
  final String startDate;
  final String endDate;
  final String submittedOn;
  final String leaveType;
  final String state;
  final String requestedHours;
  final String registeredHours;
  final String reason;

  LeaveRequestScreen({
    required this.startDate,
    required this.endDate,
    required this.submittedOn,
    required this.leaveType,
    required this.state,
    required this.requestedHours,
    required this.registeredHours,
    required this.reason,
  });

  @override
  Widget build(BuildContext context) {
    String registeredHoursString = registeredHours == "null" ? "-" : registeredHours;

    print(registeredHoursString);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.leveRequest),
      body: Padding(
        padding: EdgeInsets.all(AppSize.sp20),
        child: Column(
          children: [
            SpaceV(AppSize.sp4),
            CommonInfoRow(title: AppLocalizations.of(context)!.start, value: startDate),
            CommonInfoRow(title: AppLocalizations.of(context)!.end, value: endDate),
            CommonInfoRow(title: AppLocalizations.of(context)!.submittedOn, value: submittedOn),
            CommonInfoRow(title: AppLocalizations.of(context)!.leaveType, value: leaveType),
            CommonInfoRow(title: AppLocalizations.of(context)!.state, value: state),
            CommonInfoRow(title: AppLocalizations.of(context)!.requestedHours, value: requestedHours),
            CommonInfoRow(title: AppLocalizations.of(context)!.registeredHours, value: registeredHoursString),
            CommonInfoRow(title: AppLocalizations.of(context)!.reason, value: reason, needDivider: false),
          ],
        ),
      ),
    );
  }
}
