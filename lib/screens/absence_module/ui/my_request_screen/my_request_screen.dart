import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/my_request_screen/leave_request_screen.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/request_leave_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class MyRequestScreen extends StatefulWidget {
  const MyRequestScreen({super.key});

  @override
  State<MyRequestScreen> createState() => _MyRequestScreenState();
}

class _MyRequestScreenState extends State<MyRequestScreen> {
  final absenceBloc = BlocProvider.of<AbsenceCubit>(navigatorKey.currentContext!);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      absenceBloc.timesheetLeaveInYearApiCall(context: context, selectedYear: '${DateTime.now().year}');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.myRequestText),
      body: RefreshIndicator(
        onRefresh: () {
          return absenceBloc.timesheetLeaveInYearApiCall(context: context, selectedYear: '${DateTime.now().year}');
        },
        child: ValueListenableBuilder(
          valueListenable: absenceBloc.isLoading,
          builder: (BuildContext context, isLoading, Widget? child) {
            if (!isLoading && absenceBloc.timesheetLeaveInYearList.isEmpty) {
              return Center(
                child: Text(AppLocalizations.of(context)!.noDataFound,
                    style: context.textTheme.bodyMedium
                        ?.copyWith(fontSize: AppSize.sp15, color: context.themeColors.textColor)),
              );
            } else if (!isLoading && absenceBloc.timesheetLeaveInYearList.isNotEmpty) {
              print('absenceBloc isloading ----->');
              return ListView.builder(
                itemCount: absenceBloc.timesheetLeaveInYearList.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(absenceBloc.timesheetLeaveInYearList[index].soortVerlof ?? '',
                        style: context.textTheme.bodyMedium
                            ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15)),
                    // subtitle: Text('Mon 11 Sep - Mon 11 Sep',
                    subtitle: Text(
                        "${formatDate(absenceBloc.timesheetLeaveInYearList[index].begin ?? DateTime.now())} - ${formatDate(absenceBloc.timesheetLeaveInYearList[index].einde ?? DateTime.now())}",
                        style: context.textTheme.titleMedium
                            ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor)),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          absenceBloc.timesheetLeaveInYearList[index].toestand ?? '',
                          style: context.textTheme.bodySmall?.copyWith(color: context.themeColors.darkGreyColor),
                        ),
                        SpaceH(AppSize.w4),
                        Icon(
                          Ionicons.chevron_forward_outline,
                          color: context.themeColors.greyColor,
                          size: AppSize.sp18,
                        )
                      ],
                    ),
                    onTap: () {
                      final formatter = DateFormat('MMM dd, yyy', appDB.language);
                      AppNavigation.nextScreen(
                          context,
                          LeaveRequestScreen(
                              startDate:
                                  formatter.format(absenceBloc.timesheetLeaveInYearList[index].begin ?? DateTime.now()),
                              endDate:
                                  formatter.format(absenceBloc.timesheetLeaveInYearList[index].einde ?? DateTime.now()),
                              submittedOn: formatter
                                  .format(absenceBloc.timesheetLeaveInYearList[index].invoerdatum ?? DateTime.now()),
                              leaveType: absenceBloc.timesheetLeaveInYearList[index].soortVerlof ?? '',
                              state: absenceBloc.timesheetLeaveInYearList[index].toestand ?? '',
                              requestedHours: absenceBloc.timesheetLeaveInYearList[index].urenAangevraagd.toString(),
                              registeredHours: absenceBloc.timesheetLeaveInYearList[index].urenGeregistreerd.toString(),
                              reason: absenceBloc.timesheetLeaveInYearList[index].reden ?? ''));
                    },
                  );
                },
              );
            } else {
              print('absenceBloc isloading2 ----->');

              return Center(
                child: Image.asset(
                  'assets/gif/loader_gif.gif',
                  height: AppSize.h60,
                  width: AppSize.w60,
                  color: context.themeColors.textColor,
                ),
              );
            }
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
          elevation: 10,
          onPressed: () {
            AppNavigation.nextScreen(context, RequestLeaveScreen());
          },
          backgroundColor: AppColors.primaryColor,
          child: Icon(
            Icons.add,
            color: AppColors.white,
          )),
    );
  }

  String formatDate(DateTime date) {
    final formatter = DateFormat('E dd MMM', appDB.language);
    return formatter.format(date);
  }
}

// TODO (H) : Use below code for update UI without loading

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:intl/intl.dart';
// import 'package:ionicons/ionicons.dart';
// import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
// import 'package:staff_medewerker/main.dart';
// import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
// import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
// import 'package:staff_medewerker/utils/appsize.dart';
// import 'package:staff_medewerker/utils/colors/app_colors.dart';
//
// import 'leave_request_screen.dart';
//
// class MyRequestScreen extends StatefulWidget {
//   const MyRequestScreen({super.key});
//
//   @override
//   State<MyRequestScreen> createState() => _MyRequestScreenState();
// }
//
// class _MyRequestScreenState extends State<MyRequestScreen> {
//   final absenceBloc = BlocProvider.of<AbsenceCubit>(navigatorKey.currentContext!);
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
//       await absenceBloc.timesheetLeaveInYearApiCall(
//           context: context, selectedYear: '${DateTime.now().year}', isFirstTime: true);
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: context.themeColors.homeContainerColor,
//       appBar: CustomAppBar(title: "My Request"),
//       body: RefreshIndicator(
//         onRefresh: () {
//           return absenceBloc.timesheetLeaveInYearApiCall(context: context, selectedYear: '${DateTime.now().year}');
//         },
//         child: ValueListenableBuilder(
//           valueListenable: absenceBloc.isLoading,
//           builder: (BuildContext context, isLoading, Widget? child) {
//             if (isLoading) {
//               return Center(
//                 child: Image.asset(
//                   'assets/gif/loader_gif.gif',
//                   height: AppSize.h60,
//                   width: AppSize.w60,
//                   color: AppColors.black,
//                 ),
//               );
//             } else {
//               return ValueListenableBuilder(
//                 valueListenable: absenceBloc.timesheetLeaveInYearList2,
//                 builder: (BuildContext context, timesheetLeaveInYearList, Widget? child) {
//                   return ListView.builder(
//                     itemCount: absenceBloc.timesheetLeaveInYearList2.value.length,
//                     itemBuilder: (context, index) {
//                       return ListTile(
//                         title: Text(timesheetLeaveInYearList[index].soortVerlof,
//                             style: context.textTheme.bodyMedium
//                                 ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15)),
//                         // subtitle: Text('Mon 11 Sep - Mon 11 Sep',
//                         subtitle: Text(
//                             "${formatDate(timesheetLeaveInYearList[index].begin)} - ${formatDate(timesheetLeaveInYearList[index].einde)}",
//                             style: context.textTheme.titleMedium
//                                 ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor)),
//                         trailing: Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Text(
//                               timesheetLeaveInYearList[index].toestand,
//                               style: context.textTheme.bodySmall?.copyWith(color: context.themeColors.darkGreyColor),
//                             ),
//                             IconButton(
//                                 onPressed: () {},
//                                 icon: Icon(
//                                   Ionicons.chevron_forward_outline,
//                                   color: context.themeColors.greyColor,
//                                   size: AppSize.sp18,
//                                 ))
//                           ],
//                         ),
//                         onTap: () {
//                           final formatter = DateFormat('MMM dd, yyy');
//                           AppNavigation.nextScreen(
//                               context,
//                               LeaveRequestScreen(
//                                   startDate: formatter.format(timesheetLeaveInYearList[index].begin),
//                                   endDate: formatter.format(timesheetLeaveInYearList[index].einde),
//                                   submittedOn: formatter.format(timesheetLeaveInYearList[index].invoerdatum),
//                                   leaveType: timesheetLeaveInYearList[index].soortVerlof,
//                                   state: timesheetLeaveInYearList[index].toestand,
//                                   requestedHours: timesheetLeaveInYearList[index].urenAangevraagd.toString(),
//                                   registeredHours: timesheetLeaveInYearList[index].urenGeregistreerd.toString(),
//                                   reason: timesheetLeaveInYearList[index].reden));
//                         },
//                       );
//                     },
//                   );
//                 },
//               );
//             }
//           },
//         ),
//       ),
//     );
//   }
//
//   String formatDate(DateTime date) {
//     final formatter = DateFormat('E dd MMM', 'en_US');
//     return formatter.format(date);
//   }
// }
