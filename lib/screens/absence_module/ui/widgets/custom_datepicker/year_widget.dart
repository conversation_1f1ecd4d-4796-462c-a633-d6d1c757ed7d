import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';


class AbsenceYearPicker extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    DateFormat format = DateFormat("MMM dd, yyyy");
    int currentYear = format.parse(BlocProvider.of<AbsenceCubit>(context).selectedDateString.value).year
    ;

    // int currentYear = DateTime.now().year;
    int startYear = DateTime.now().year - 100;

    List<int> yearsList = List<int>.generate(currentYear - startYear + 1, (index) {
      return DateTime.now().year - index;
    });

    return BlocBuilder<DateCubit, DateTime>(
      builder: (context, date) {
        return ListWheelScrollView(
          perspective: 0.000001,
          useMagnifier: false,
          itemExtent: 50,
          physics: FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            int selectedYear = yearsList[index];
            context.read<DateCubit>().setYear(selectedYear);
            print(selectedYear); // Print the selected year
          },
          children: yearsList.map((value) {
            final isSelected = value == date.year;
            return Text(
              value.toString(),
              style: context.textTheme.bodyMedium?.copyWith(
                fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
                color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
              ),
            );
          }).toList(),
          controller: FixedExtentScrollController(
              initialItem: yearsList.indexOf(date.year)), // Set the initial item to the current year
        );
      },
    );
  }
}












// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
// import 'package:staff_medewerker/screens/absence_module/ui/custom_datepicker/datepicker_cubit.dart';
// import 'package:staff_medewerker/utils/appsize.dart';
// import 'package:staff_medewerker/utils/colors/app_colors.dart';
//
// class YearPickerWidget extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     int currentYear = DateTime.now().year;
//     int startYear = DateTime.now().year - 100; // Define the start year
//
//     return BlocBuilder<DateCubit, DateTime>(
//       builder: (context, date) {
//         return ListWheelScrollView(
//           perspective: 0.000001,
//           useMagnifier: false,
//           itemExtent: 50,
//           physics: FixedExtentScrollPhysics(),
//           onSelectedItemChanged: (index) {
//             int selectedYear = currentYear - index;
//             context.read<DateCubit>().setYear(selectedYear);
//             print(selectedYear);
//             // print(currentYear);
//             // print(index);
//           },
//           children: List<Widget>.generate(currentYear+1 - startYear, (index) {
//             final value = startYear + index;
//             final isSelected = value == date.year;
//             return Text(
//               value.toString(),
//               style: context.textTheme.bodyMedium?.copyWith(
//                 fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
//                 color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
//               ),
//             );
//           }).re,
//           controller: FixedExtentScrollController(
//               initialItem:  startYear - currentYear), // Set the initial item to the current year
//         );
//       },
//     );
//   }
// }
//
