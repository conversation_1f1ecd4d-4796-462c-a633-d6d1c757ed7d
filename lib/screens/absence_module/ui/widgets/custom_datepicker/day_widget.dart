import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class AbsenceDayPicker extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    DateFormat format = DateFormat("MMM dd, yyyy");

    int currentDay = format.parse(BlocProvider.of<AbsenceCubit>(context).selectedDateString.value).day;

    return BlocBuilder<DateCubit, DateTime>(
      builder: (context, date) {
        final maxDay = DateTime(date.year, date.month + 1, 0).day;
        return ListWheelScrollView(
          perspective: 0.000001,
          itemExtent: 50,
          useMagnifier: false,
          physics: FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            context.read<DateCubit>().setDay(index + 1);
          },
          children: List<Widget>.generate(maxDay, (index) {
            final value = index + 1;
            final isSelected = value == date.day;
            return Text(value.toString(),
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
                  color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
                ));
          }),
          controller: FixedExtentScrollController(initialItem: currentDay - 1),
        );
      },
    );
  }
}
