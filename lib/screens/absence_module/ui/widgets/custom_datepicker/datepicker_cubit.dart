import 'package:flutter_bloc/flutter_bloc.dart';

class DateCubit extends Cubit<DateTime> {
  DateCubit() : super(DateTime.now());

  bool _isLeapYear(int year) {
    return year % 4 == 0 && (year % 100 != 0 || year % 400 == 0);
  }

  int _calculateMaxDay(int month, bool isLeap) {
    if (month == 2) {
      return isLeap ? 29 : 28;
    } else if ([4, 6, 9, 11].contains(month)) {
      return 30;
    } else {
      return 31;
    }
  }

  int get maxDay {
    final selectedDate = state;
    return _calculateMaxDay(selectedDate.month, _isLeapYear(selectedDate.year));
  }

  void setDay(int day) {
    final selectedDate = state;
    if (day <= maxDay) {
      emit(DateTime(selectedDate.year, selectedDate.month, day));
    }
  }
  void setDate(DateTime date) {
    setDay(date.day);
    setMonth(date.month);
    setYear(date.year);
  }

  void setMonth(int month) {
    final selectedDate = state;
    int day = selectedDate.day >_calculateMaxDay(month, _isLeapYear(selectedDate.year)) ? _calculateMaxDay(month, _isLeapYear(selectedDate.year)) : selectedDate.day;
      emit(DateTime(selectedDate.year, month, day));
  }

  void setYear(int year) {
    final selectedDate = state;
      emit(DateTime(year, selectedDate.month, selectedDate.day));
  }
}