// import 'package:flutter/material.dart';
// import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
// import 'package:staff_medewerker/utils/appsize.dart';
// import 'package:staff_medewerker/utils/colors/app_colors.dart';
//
// class AbsenceShimmer extends StatelessWidget {
//   const AbsenceShimmer({
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return ShimmerWidget(
//       child: Table(
//         children: [
//           TableRow(
//             children: [
//               TableCell(
//                 child: Container(
//                   margin: EdgeInsets.only(top: AppSize.sp10,right: AppSize.sp10),
//                   height: AppSize.h10,
//                   color: AppColors.white,
//                 ),
//               ),
//               TableCell(
//                 child: Container(
//                   margin: EdgeInsets.only(top: AppSize.sp10,right: AppSize.sp10),
//                   height: AppSize.h10,
//                   color: AppColors.white,
//                 ),
//               ),
//               TableCell(
//                 child: Container(
//                   margin: EdgeInsets.only(top: AppSize.sp10,right: AppSize.sp10),
//                   height: AppSize.h10,
//                   color: AppColors.white,
//                 ),
//               ),
//               TableCell(
//                 child: Container(
//                   margin: EdgeInsets.only(top: AppSize.sp10,right: AppSize.sp10),
//                   height: AppSize.h10,
//                   color: AppColors.white,
//                 ),
//               )
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
