import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/screens/absence_module/repository/my_hours_period/my_hours_period_repository.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/leave_types_repository/submit_leave_repository.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:syncfusion_flutter_datepicker/src/date_picker/date_picker_manager.dart';

class RequestLeaveCubit extends Cubit<bool> {
  RequestLeaveCubit() : super(false);
  ImagePicker picker = ImagePicker();
  XFile? image;
  ValueNotifier<String> imagePath = ValueNotifier('');
  ValueNotifier<bool> checkBox1 = ValueNotifier(false);
  ValueNotifier<bool> checkBox2 = ValueNotifier(false);
  ValueNotifier<bool> inProgress = ValueNotifier(false);
  ValueNotifier<PickerDateRange> selectedRanges = ValueNotifier(PickerDateRange(DateTime.now(), DateTime.now()));
  ValueNotifier<String> selectedDateRange = ValueNotifier('Selecteer een periode');
  ValueNotifier<TextEditingController> workedHoursController = ValueNotifier(TextEditingController(text: ''));
  ValueNotifier<TextEditingController> leaveReasonController = ValueNotifier(TextEditingController(text: ''));
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  Future<void> myHoursInPeriodApiCall({
    required BuildContext context,
    required String startDate,
    required String endDate,
  }) async {
    isLoading.value = true;
    print("api started =====>");
    final MyHoursPeriodApiRepository myHoursPeriodApiRepository = MyHoursPeriodApiRepository();
    final response =
        await myHoursPeriodApiRepository.myHoursPeriodApi(context: context, startDate: startDate, endDate: endDate);
    print("api done =====>${response}");

    if (response?.statusCode == 200 && response?.done == 1) {
      print("response?.result : ${response?.result}");
      workedHoursController.value.text = response?.result ?? "";
    }
    isLoading.value = false;
  }

  Future<void> submitLeaveApiCall(
      {required BuildContext context,
      required String startDate,
      required String endDate,
      required String selectedLeaveTypeGuid,
      required String workedHours,
      required String leaveReason}) async {
    isLoading.value = true;
    print("api started =====>");
    final SubmitLeaveApiRepository submitLeaveApiRepository = SubmitLeaveApiRepository();
    final response = await submitLeaveApiRepository.submitLeaveApi(
        context: context,
        startDate: startDate,
        endDate: endDate,
        selectedLeaveTypeGuid: selectedLeaveTypeGuid,
        workedHours: workedHours,
        leaveReason: leaveReason,);
    print("api done =====>${response}");

    if (response!.isNotEmpty && response[0].done == 1) {
      customSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.leaveReqSubmittedText,
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      AppNavigation.previousScreen(context);

    } else {
      customSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.errorText,
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    isLoading.value = false;
  }

  void selectedRange({required BuildContext context, PickerDateRange? selectedRange}) {
    selectedRanges.value = selectedRange!;
  }

  void setDateRange(String dateRange) {
    selectedDateRange.value = dateRange;
  }

  void clearFormData({required BuildContext context}) {
    selectedDateRange.value = AppLocalizations.of(context)!.selectPeriodText;
    selectedRanges.value = PickerDateRange(DateTime.now(), DateTime.now());

    workedHoursController.value.text = "";
    leaveReasonController.value.text = "";
  }
}
