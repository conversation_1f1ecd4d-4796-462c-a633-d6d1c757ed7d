import 'dart:convert';

List<SubmitLeaveModel> submitLeaveModelFromJson(String str) => List<SubmitLeaveModel>.from(json.decode(str).map((x) => SubmitLeaveModel.fromJson(x)));

String submitLeaveModelToJson(List<SubmitLeaveModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SubmitLeaveModel {
  int done;
  String newGuid;

  SubmitLeaveModel({
    required this.done,
    required this.newGuid,
  });

  factory SubmitLeaveModel.fromJson(Map<String, dynamic> json) => SubmitLeaveModel(
    done: json["Done"],
    newGuid: json["NewGuid"],
  );

  Map<String, dynamic> toJson() => {
    "Done": done,
    "NewGuid": newGuid,
  };
}
