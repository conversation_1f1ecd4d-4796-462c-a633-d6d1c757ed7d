import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/absence_module/repository/absence_api_provider.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/leave_types_repository/leave_type_model.dart';

class LeaveTypeApiRepository {
  final absenceProvider = AbsenceApiProvider();

  Future<List<LeaveTypeModel>?> leaveTypeApi({required BuildContext context}) async {
    final response = await absenceProvider.leaveTypeApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<LeaveTypeModel> leaveType = [];

      for (var item in dataList) {
        leaveType.add(LeaveTypeModel.fromJson(item));
      }

      return leaveType;
    } else {
      return null;
    }
  }
}