import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/absence_module/repository/absence_api_provider.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/leave_types_repository/submit_leave_response_model.dart';

class SubmitLeaveApiRepository {
  final absenceProvider = AbsenceApiProvider();

  Future<List<SubmitLeaveModel>?> submitLeaveApi(
      {required BuildContext context,
      required String startDate,
      required String endDate,
      required String selectedLeaveTypeGuid,
      required String workedHours,
      required String leaveReason}) async {
    final response = await absenceProvider.submitLeaveApiCall(
        context, startDate, endDate, selectedLeaveTypeGuid, workedHours, leaveReason);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<SubmitLeaveModel> submitLeave = [];

      for (var item in dataList) {
        submitLeave.add(SubmitLeaveModel.from<PERSON>son(item));
      }

      return submitLeave;
    } else {
      return null;
    }
  }
}
