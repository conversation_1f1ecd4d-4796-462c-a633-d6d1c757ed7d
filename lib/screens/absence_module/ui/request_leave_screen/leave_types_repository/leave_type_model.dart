import 'dart:convert';

List<LeaveTypeModel> leaveTypeModelFromJson(String str) => List<LeaveTypeModel>.from(json.decode(str).map((x) => LeaveTypeModel.fromJson(x)));

String leaveTypeModelToJson(List<LeaveTypeModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class LeaveTypeModel {
  String guid;
  String title;

  LeaveTypeModel({
    required this.guid,
    required this.title,
  });

  factory LeaveTypeModel.fromJson(Map<String, dynamic> json) => LeaveTypeModel(
    guid: json["guid"],
    title: json["Title"],
  );

  Map<String, dynamic> toJson() => {
    "guid": guid,
    "Title": title,
  };
}
