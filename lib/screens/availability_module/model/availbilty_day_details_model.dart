import 'package:equatable/equatable.dart';

class AvailabilityWeekDayListResponseModel extends Equatable {
  int? iSOYearWeek;
  int? dayNumber;
  String? remark;
  String? date;
  String? rosterStartTime;
  String? rosterEndTime;
  bool? prohibitUnavailable;
  String? FromTime;
  String? ToTime;
  int? availabilityState;
  bool? editRight;
  int statusCode;

  AvailabilityWeekDayListResponseModel({
    this.iSOYearWeek,
    this.FromTime,
    this.dayNumber,
    this.ToTime,
    this.availabilityState,
    this.remark,
    this.editRight,
    this.date,
    this.rosterStartTime,
    this.rosterEndTime,
    this.prohibitUnavailable,
    required this.statusCode,
  });

  AvailabilityWeekDayListResponseModel.clone(AvailabilityWeekDayListResponseModel source)
      : this.iSOYearWeek = source.iSOYearWeek,
        this.dayNumber = source.dayNumber,
        this.remark = source.remark,
        this.date = source.date,
        this.rosterStartTime = source.rosterStartTime,
        this.rosterEndTime = source.rosterEndTime,
        this.prohibitUnavailable = source.prohibitUnavailable,
        this.FromTime = source.FromTime,
        this.ToTime = source.ToTime,
        this.availabilityState = source.availabilityState,
        this.editRight = source.editRight,
        this.statusCode = source.statusCode;

  factory AvailabilityWeekDayListResponseModel.fromJson(
      Map<String, dynamic> json, int statusCode) {
    return AvailabilityWeekDayListResponseModel(
      statusCode: statusCode,
      iSOYearWeek: json['ISOYearWeek'],
      dayNumber: json['DayNumber'],
      remark: json['Remark'],
      date: json['Date'],
      rosterStartTime: json['RosterStartTime'],
      rosterEndTime: json['RosterEndTime'],
      prohibitUnavailable: json['ProhibitUnavailable'],
      FromTime: json['FromTime'],
      ToTime: json['ToTime'],
      availabilityState: json['AvailabilityState'],
      editRight: json['EditRight'],
    );
  }

  @override
  // TODO: implement props
  List<Object?> get props => [
        this.statusCode,
        this.iSOYearWeek,
        this.dayNumber,
        this.remark,
        this.date,
        this.rosterStartTime,
        this.rosterEndTime,
        this.prohibitUnavailable,
        this.FromTime,
        this.ToTime,
        this.availabilityState,
        this.editRight,
      ];
}
