// class SetWeekListResponse {
//   final String result;
//   final int done;
//   final String newGuid;
//   final int statusCode;
//
//   SetWeekListResponse({
//     required this.result,
//     required this.done,
//     required this.newGuid,
//     required this.statusCode,
//   });
//
//   factory SetWeekListResponse.fromJson(Map<String, dynamic> json, int statusCode) {
//     return SetWeekListResponse(
//       result: json['Result'],
//       done: json['Done'],
//       newGuid: json['NewGuid'],
//       statusCode: statusCode,
//     );
//   }
// }
//
