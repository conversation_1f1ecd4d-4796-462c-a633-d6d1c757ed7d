class DayWeekListModel {
  int? dayNumber;
  int? availabilityState;
  String? FromTime;
  String? remark;
  String? ToTime;

  DayWeekListModel({
     this.dayNumber,
     this.availabilityState,
     this.FromTime,
     this.remark,
     this.ToTime,
  });

  // Create a method to convert the object to a Map
  Map<String, dynamic> toJson() {
    return {
      'DayNumber': dayNumber,
      'AvailabilityState': availabilityState,
      'FromTime': FromTime,
      'Remark': remark,
      'ToTime': ToTime,
    };
  }
}
