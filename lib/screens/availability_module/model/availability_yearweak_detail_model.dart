class AvailabilityYearWeekResponseModel {
  final int? iSOYearWeek;
  int? numberOfServices;
  String? Remark;
  final String? Status;
  final bool? EditRight;
  final bool? ShowAvailable;
  final bool? ShowNotAvailable;
  final bool? WeekDaysEdited;
  final int statusCode;

  AvailabilityYearWeekResponseModel({
    this.iSOYearWeek,
    this.numberOfServices,
    this.Remark,
    this.Status,
    this.EditRight,
    this.ShowAvailable,
    this.ShowNotAvailable,
    this.WeekDaysEdited,
    required this.statusCode,
  });

  factory AvailabilityYearWeekResponseModel.fromJson(Map<String, dynamic> json, int statusCode) {
    return AvailabilityYearWeekResponseModel(
        statusCode: statusCode,
        iSOYearWeek: json['ISOYearWeek'],
        numberOfServices: json['NumberOfServices'],
        Remark: json['Remark'],
        Status: json['Status'],
        EditRight: json['EditRight'] ?? false,
        ShowAvailable: json['ShowAvailable'],
        ShowNotAvailable: json['ShowNotAvailable'],
        WeekDaysEdited: json['WeekDaysEdited']);
  }
}
