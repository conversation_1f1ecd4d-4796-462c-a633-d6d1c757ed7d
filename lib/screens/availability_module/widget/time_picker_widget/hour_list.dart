// import 'package:flutter/material.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
//
// import '../../../../utils/appsize.dart';
// import '../../../../utils/colors/app_colors.dart';
//
//
// class HourList extends StatelessWidget {
//   const HourList({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     int value = 7; // Replace with the initial value you want to highlight
//     int currentHour = DateTime.now().hour; // Replace with the current hour or another value
//
//     return ListWheelScrollView(
//       itemExtent: 50,
//       perspective: 0.0000000001,
//       useMagnifier: false,
//       physics: FixedExtentScrollPhysics(),
//       onSelectedItemChanged: (index) {
//         // Handle the selected item change if needed
//       },
//       children: List<Widget>.generate(17, (index) {
//         final number = (index + 7).toString().padLeft(2, '0'); // Convert to two-digit format
//         final isSelected = value == int.parse(number);
//
//         return Text(
//           number,
//           style: context.textTheme.bodyMedium?.copyWith(
//             fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
//             color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
//           ),
//         );
//       }),
//       controller: FixedExtentScrollController(initialItem: currentHour - 7),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';

class HourList extends StatelessWidget {
  final int value;
  final ValueChanged<int> onValueChanged;

  HourList({required this.value, required this.onValueChanged, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListWheelScrollView(
      itemExtent: 50,
      perspective: 0.0000000001,
      useMagnifier: false,
      physics: FixedExtentScrollPhysics(),
      onSelectedItemChanged: (index) {
        final newValue = index + 7; // Calculate the selected value
        onValueChanged(newValue); // Pass the selected value to the parent widget
      },
      children: List<Widget>.generate(17, (index) {
        final number = (index + 7).toString().padLeft(2, '0');
        final isSelected = value == int.parse(number);

        return Text(
          number,
          style:    context.textTheme.bodyMedium?.copyWith(
        fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
          color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
        )
        );
      }),
      controller: FixedExtentScrollController(initialItem: value - 7),
    );
  }
}
