import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';
import '../../bloc/availability_cubit.dart';

class TimeSlotPicker extends StatefulWidget {
  final String startTime;
  final String endTime;
  final int interval;
  final String initialTime; // Initial time value as "hh:mm"
  final void Function(String) onTimeSelected;
  final void Function(int)? onSelectedItemChanged;
  TimeSlotPicker({
    required this.startTime,
    required this.endTime,
    required this.interval,
    required this.initialTime,
    required this.onTimeSelected,
    this.onSelectedItemChanged,
  });

  @override
  _TimeSlotPickerState createState() => _TimeSlotPickerState();
}

class _TimeSlotPickerState extends State<TimeSlotPicker> {
  void initState() {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    super.initState();
    generateTimeSlots();
    if (widget.initialTime != null && widget.initialTime.isNotEmpty) {
      final initialTimeParts = widget.initialTime.split(':');
      final initialHour = int.tryParse(initialTimeParts[0]) ?? 0;
      final initialMinute = int.tryParse(initialTimeParts[1]) ?? 0;

      final initialTime = '${initialHour.toString().padLeft(2, '0')}:${initialMinute.toString().padLeft(2, '0')}';
      final initialIndex = availabilityBloc.timeSlots.indexOf(initialTime);

      if (initialIndex != -1) {
        availabilityBloc.selectedTimeSlotIndex.value = initialIndex;
      } else if (availabilityBloc.timeSlots.isNotEmpty) {
        availabilityBloc.selectedTimeSlotIndex.value = 0;
      }
      availabilityBloc.selectedTimeSlotIndex.value =
          availabilityBloc.selectedTimeSlotIndex.value.clamp(0, availabilityBloc.timeSlots.length - 1);
    } else if (availabilityBloc.timeSlots.isNotEmpty) {
      availabilityBloc.selectedTimeSlotIndex.value = 0;
      availabilityBloc.selectedTimeSlotIndex.value =
          availabilityBloc.selectedTimeSlotIndex.value.clamp(0, availabilityBloc.timeSlots.length - 1);
    }
  }

  void generateTimeSlots() {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    availabilityBloc.timeSlots.clear();
    final startParts = widget.startTime.split(':');
    final endParts = widget.endTime.split(':');

    final startHour = int.parse(startParts[0]);
    final startMinute = int.parse(startParts[1]);

    final endHour = int.parse(endParts[0]);
    final endMinute = int.parse(endParts[1]);

    int currentHour = startHour;
    int currentMinute = startMinute;

    while (!(currentHour == endHour && currentMinute > endMinute)) {
      final timeSlot = '${currentHour.toString().padLeft(2, '0')}:${currentMinute.toString().padLeft(2, '0')}';
      availabilityBloc.timeSlots.add(timeSlot);

      currentMinute += widget.interval;
      if (currentMinute >= 60) {
        currentMinute = 0;
        currentHour++;
      }
      if (currentHour >= 24) {
        currentHour = 0; // Reset to 0 for the next day
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    return ValueListenableBuilder(
      valueListenable: availabilityBloc.selectedTimeSlotIndex,
      builder: (context, value, child) {
        return ListWheelScrollView(
          itemExtent: 40, // Adjust the item extent to reduce vertical padding
          perspective: 0.**********,
          useMagnifier: false,
          physics: FixedExtentScrollPhysics(),

          onSelectedItemChanged: widget.onSelectedItemChanged ??
              (index) {
                // setState(() {
                availabilityBloc.selectedTimeSlotIndex.value = index;
                // });
              },
          children: availabilityBloc.timeSlots.map((timeSlot) {
            final isSelected = availabilityBloc.timeSlots[availabilityBloc.selectedTimeSlotIndex.value] == timeSlot;
            // availabilityBloc.fromTimeUpdate(timeSlot);
            return Padding(
              padding: EdgeInsets.only(left: AppSize.w20),
              child: Row(
                children: [
                  Text(
                    timeSlot,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontSize: isSelected ? AppSize.sp18 : AppSize.sp15, // Adjust font size
                      color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          controller: FixedExtentScrollController(initialItem: availabilityBloc.selectedTimeSlotIndex.value),
        );
      },
    );
  }
}

class TimeUntilSlotPicker extends StatefulWidget {
  final String startTime;
  final String endTime;
  final int interval;
  final String initialTime; // Initial time value as "hh:mm"
  final void Function(String) onTimeSelected;
  final void Function(int)? onSelectedItemChanged;
  TimeUntilSlotPicker({
    required this.startTime,
    required this.endTime,
    required this.interval,
    required this.initialTime,
    required this.onTimeSelected,
    this.onSelectedItemChanged,
  });

  @override
  _TimeUntilSlotPickerState createState() => _TimeUntilSlotPickerState();
}

class _TimeUntilSlotPickerState extends State<TimeUntilSlotPicker> {
  void initState() {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    super.initState();
    generateTimeSlots();
    if (widget.initialTime != null && widget.initialTime.isNotEmpty) {
      final initialTimeParts = widget.initialTime.split(':');
      final initialHour = int.tryParse(initialTimeParts[0]) ?? 0;
      final initialMinute = int.tryParse(initialTimeParts[1]) ?? 0;

      final initialTime = '${initialHour.toString().padLeft(2, '0')}:${initialMinute.toString().padLeft(2, '0')}';
      final initialIndex = availabilityBloc.timeUntilSlots.indexOf(initialTime);

      if (initialIndex != -1) {
        availabilityBloc.selectedTimeUntilSlotIndex.value = initialIndex;
      } else if (availabilityBloc.timeUntilSlots.isNotEmpty) {
        availabilityBloc.selectedTimeUntilSlotIndex.value = 0;
      }
      availabilityBloc.selectedTimeUntilSlotIndex.value =
          availabilityBloc.selectedTimeUntilSlotIndex.value.clamp(0, availabilityBloc.timeUntilSlots.length - 1);
    } else if (availabilityBloc.timeUntilSlots.isNotEmpty) {
      availabilityBloc.selectedTimeUntilSlotIndex.value = 0;
      availabilityBloc.selectedTimeUntilSlotIndex.value =
          availabilityBloc.selectedTimeUntilSlotIndex.value.clamp(0, availabilityBloc.timeUntilSlots.length - 1);
    }
  }

  void generateTimeSlots() {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    availabilityBloc.timeUntilSlots.clear();
    final startParts = widget.startTime.split(':');
    final endParts = widget.endTime.split(':');

    final startHour = int.parse(startParts[0]);
    final startMinute = int.parse(startParts[1]);

    final endHour = int.parse(endParts[0]);
    final endMinute = int.parse(endParts[1]);

    int currentHour = startHour;
    int currentMinute = startMinute;

    while (!(currentHour == endHour && currentMinute > endMinute)) {
      final timeSlot = '${currentHour.toString().padLeft(2, '0')}:${currentMinute.toString().padLeft(2, '0')}';
      availabilityBloc.timeUntilSlots.add(timeSlot);

      currentMinute += widget.interval;
      if (currentMinute >= 60) {
        currentMinute = 0;
        currentHour++;
      }
      if (currentHour >= 24) {
        currentHour = 0; // Reset to 0 for the next day
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    return ValueListenableBuilder(
      valueListenable: availabilityBloc.selectedTimeUntilSlotIndex,
      builder: (context, value, child) {
        return ListWheelScrollView(
          itemExtent: 40, // Adjust the item extent to reduce vertical padding
          perspective: 0.**********,
          useMagnifier: false,
          physics: FixedExtentScrollPhysics(),

          onSelectedItemChanged: widget.onSelectedItemChanged ??
              (index) {
                // setState(() {
                availabilityBloc.selectedTimeUntilSlotIndex.value = index;
                //});
              },
          children: availabilityBloc.timeUntilSlots.map((timeSlot) {
            final bool isSelected =
                availabilityBloc.timeUntilSlots[availabilityBloc.selectedTimeUntilSlotIndex.value] == timeSlot;
            return Padding(
              padding: EdgeInsets.only(left: AppSize.w20),
              child: Row(
                children: [
                  Text(
                    timeSlot,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontSize: isSelected ? AppSize.sp18 : AppSize.sp15, // Adjust font size
                      color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          controller: FixedExtentScrollController(initialItem: availabilityBloc.selectedTimeUntilSlotIndex.value),
        );
      },
    );
  }
}
