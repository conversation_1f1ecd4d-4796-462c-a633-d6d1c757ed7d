import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';

class MinuteList extends StatelessWidget {
  final int value; // The currently selected minute
  final ValueChanged<int> onValueChanged; // Callback to update the selected minute
  final int selectedHour; // The currently selected hour

  MinuteList({required this.value, required this.onValueChanged, required this.selectedHour, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isHour23 = selectedHour == 23;

    return ListWheelScrollView(
      itemExtent: 50,
      perspective: 0.0000000001,
      useMagnifier: false,
      physics: FixedExtentScrollPhysics(),
      onSelectedItemChanged: (index) {
        // Calculate the selected minute based on the index (00, 15, 30, 45)
        final selectedMinute = index * 15;
        onValueChanged(selectedMinute); // Pass the selected minute to the parent widget
      },
      children: List<Widget>.generate(isHour23 ? 1 : 4, (index) {
        if (isHour23) {
          return Text("00",
              style: context.textTheme.bodyMedium?.copyWith(
                fontSize: value == 0 ? AppSize.sp20 : AppSize.sp16,
                color: value == 0 ? AppColors.primaryColor : context.themeColors.textColor,
              ));
        } else {
          final minuteValue = (index * 15).toString().padLeft(2, '0'); // Convert to two-digit format
          final isSelected = value == index * 15; // Check if the minute is selected

          return Text(minuteValue,
              style: context.textTheme.bodyMedium?.copyWith(
                fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
                color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
              ));
        }
      }),
      controller: FixedExtentScrollController(
          initialItem: isHour23 ? 0 : value ~/ 15), // Set initial item based on selected minute
    );
  }
}
