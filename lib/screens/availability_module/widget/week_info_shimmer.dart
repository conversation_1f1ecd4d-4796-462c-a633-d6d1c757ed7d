import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../common/custom_widgets/shimmer_effect.dart';
import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class WeekInfoShimmerWidget extends StatelessWidget {
  const WeekInfoShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      // baseColor: AppColors.red,
      // highlightColor: AppColors.primaryColor,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  children: [
                    Container(
                       margin: EdgeInsets.symmetric(vertical: AppSize.h10,horizontal: AppSize.w10),
                      height: AppSize.h8,
                      color: AppColors.white,
                    ),
                    Divider(
                      color: context.themeColors.dividerAvailbilityColor,
                      height: 0,
                      thickness: 1.2,
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(vertical: AppSize.h10,horizontal: AppSize.w10),
                      height: AppSize.h8,
                      color: AppColors.white,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
