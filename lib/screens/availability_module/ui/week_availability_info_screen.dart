import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/availability_module/widget/week_info_shimmer.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../common/custom_widgets/appbar_custom.dart';
import '../../../utils/app_navigation/appnavigation.dart';
import '../../../utils/appsize.dart';
import '../bloc/availability_cubit.dart';
import '../model/dayweekLisModel.dart';
import '../widget/remark_textfield.dart';
import '../widget/time_picker_widget/time_picker.dart';

class WeekAvailabilityInfoScreen extends StatefulWidget {
  final int? index;
  final DateTimeRange? dateRange;
  final String? iosWeekYear;
  WeekAvailabilityInfoScreen(
      {Key? key, this.index, this.dateRange, this.iosWeekYear})
      : super(key: key);

  @override
  State<WeekAvailabilityInfoScreen> createState() =>
      _WeekAvailabilityInfoScreenState();
}

class _WeekAvailabilityInfoScreenState
    extends State<WeekAvailabilityInfoScreen> {
  List<DayWeekListModel> dayWeekList = [];
  List<DayWeekListModel> dayWeekList1 = [];
  ValueNotifier<String> remarkNotifier = ValueNotifier<String>('');
  ValueNotifier<String> timeData = ValueNotifier<String>('');
  ValueNotifier<String> fromTime = ValueNotifier<String>('');
  bool isApiCall = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);
    availabilityBloc.isRemarkUpdate = false;
    availabilityBloc.isNumberOfServiceUpdate = false;
    log("weekkkkk ===========>${widget.iosWeekYear}'}");

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      availabilityBloc.getWeekShiftInfoData(
          context: context, iosYearWeek: widget.iosWeekYear ?? '');
      // availabilityBloc.remark = availabilityBloc.availableWeekInfo?.Remark;
      // availabilityBloc.numberOfService = availabilityBloc.availableWeekInfo?.numberOfServices;
      //availabilityBloc.availableWeekInfo1 = availabilityBloc.availableWeekInfo;
      availabilityBloc.getWeekShiftDetailListInfoData(
          context: context, iosYearWeek: widget.iosWeekYear ?? '');
    });
  }

  String getWeekday(int weekday) {
    switch (weekday) {
      case 1:
        return AppLocalizations.of(context)!.monday;
      case 2:
        return AppLocalizations.of(context)!.tuesday;
      case 3:
        return AppLocalizations.of(context)!.wednesday;
      case 4:
        return AppLocalizations.of(context)!.thursday;
      case 5:
        return AppLocalizations.of(context)!.friday;
      case 6:
        return AppLocalizations.of(context)!.saturday;
      case 7:
        return AppLocalizations.of(context)!.sunday;
      default:
        return '';
    }
  }

  String getMonth(int month) {
    switch (month) {
      case 1:
        return AppLocalizations.of(context)!.january;
      case 2:
        return AppLocalizations.of(context)!.february;
      case 3:
        return AppLocalizations.of(context)!.march;
      case 4:
        return AppLocalizations.of(context)!.april;
      case 5:
        return AppLocalizations.of(context)!.may;
      case 6:
        return AppLocalizations.of(context)!.june;
      case 7:
        return AppLocalizations.of(context)!.july;
      case 8:
        return AppLocalizations.of(context)!.august;
      case 9:
        return AppLocalizations.of(context)!.september;
      case 10:
        return AppLocalizations.of(context)!.october;
      case 11:
        return AppLocalizations.of(context)!.november;
      case 12:
        return AppLocalizations.of(context)!.december;
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AvailabilityCubit, AvailabilityState>(
      builder: (ctx, state) {
        final availabilityBloc = ctx.read<AvailabilityCubit>();

        return Scaffold(
            backgroundColor: context.themeColors.homeContainerColor,
            appBar: CustomAppBar(
              actions: true,
              isLeading: true,
              title: 'Week: ${widget.index} - Open',
              onTap: () {
                AppNavigation.previousScreen(
                  context,
                );

                // if (availabilityBloc.availableWeekInfo1 !=
                //         availabilityBloc.availableWeekInfo ||
                //     availabilityBloc.isNumberOfServiceUpdate ||
                //     availabilityBloc.isRemarkUpdate) {
                //   availabilityBloc.settWeekShiftInfoData(
                //       context: context,
                //       iosYearWeek: widget.iosWeekYear ?? '',
                //       numberService: availabilityBloc
                //           .availableWeekInfo!.numberOfServices
                //           .toString(),
                //       remark: availabilityBloc.availableWeekInfo?.Remark);
                // }
                isApiCall = !listEquals(availabilityBloc.availabilityDayList,
                    availabilityBloc.availabilityDayList1);

                if (isApiCall) {
                  dayWeekList.clear();
                  availabilityBloc.availabilityDayList
                      .asMap()
                      .forEach((index, day) {
                    // Use default department times if no times are set or if they are 00:00
                    String fromTime = day.FromTime ?? '';
                    String toTime = day.ToTime ?? '';

                    if (fromTime.isEmpty || fromTime == '00:00') {
                      fromTime = day.rosterStartTime ?? '00:00';
                    }

                    if (toTime.isEmpty || toTime == '00:00') {
                      toTime = day.rosterEndTime ?? '00:00';
                    }

                    dayWeekList.add(
                      DayWeekListModel(
                        dayNumber: index + 1,
                        availabilityState: day.availabilityState ?? 0,
                        FromTime: fromTime,
                        remark: day.remark ?? '',
                        ToTime: toTime,
                      ),
                    );
                  });

                  List<Map<String, dynamic>> dayWeekJsonList =
                      dayWeekList.map((dayWeek) => dayWeek.toJson()).toList();
                  log("jsonList ======>$dayWeekJsonList");

                  availabilityBloc.settWeekShiftDetailListData(
                    context: context,
                    iosYearWeek: widget.iosWeekYear ?? '',
                    dayWeekList: dayWeekJsonList,
                  );
                }
              },
              actionList: [
                PopupMenuButton(
                    offset: Offset(0.0, AppBar().preferredSize.height * 0.9),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppSize.r4),
                    ),
                    color: context.themeColors.listGridColor1,
                    icon: Icon(Icons.more_vert_rounded, color: AppColors.white),
                    itemBuilder: (context) {
                      return <PopupMenuEntry<int>>[
                        PopupMenuItem<int>(
                          //padding: EdgeInsets.only(right: AppSize.w100, left: AppSize.w12),
                          value: 0,
                          child:
                              Text(AppLocalizations.of(context)!.allAvailable,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp15,
                                  )),
                        ),
                        PopupMenuDivider(height: 1),
                        PopupMenuItem<int>(
                          value: 1,
                          child:
                              Text(AppLocalizations.of(context)!.allUnAvailable,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp15,
                                  )),
                        ),
                      ];
                    },
                    onSelected: (value) {
                      availabilityBloc.containerColors.forEach((notifier) {
                        if (value == 0) {
                          log("All Available menu is selected.");
                          for (int index = 0;
                              index <
                                  availabilityBloc.availabilityDayList.length;
                              index++) {
                            notifier.value = AppColors.limeGreenColor;
                            availabilityBloc.availabilityDayList[index]
                                .availabilityState = 1;

                            // Set default department shift times if not already set
                            if (availabilityBloc
                                        .availabilityDayList[index].FromTime ==
                                    null ||
                                availabilityBloc.availabilityDayList[index]
                                    .FromTime!.isEmpty ||
                                availabilityBloc
                                        .availabilityDayList[index].FromTime ==
                                    '00:00') {
                              availabilityBloc.availabilityDayList[index]
                                  .FromTime = availabilityBloc
                                      .availabilityDayList[index]
                                      .rosterStartTime ??
                                  '00:00';
                            }

                            if (availabilityBloc
                                        .availabilityDayList[index].ToTime ==
                                    null ||
                                availabilityBloc.availabilityDayList[index]
                                    .ToTime!.isEmpty ||
                                availabilityBloc
                                        .availabilityDayList[index].ToTime ==
                                    '00:00') {
                              availabilityBloc.availabilityDayList[index]
                                  .ToTime = availabilityBloc
                                      .availabilityDayList[index]
                                      .rosterEndTime ??
                                  '00:00';
                            }
                          }
                        } else if (value == 1) {
                          for (int index = 0;
                              index <
                                  availabilityBloc.availabilityDayList.length;
                              index++) {
                            notifier.value = AppColors.lightModeRedColor;
                            availabilityBloc.availabilityDayList[index]
                                .availabilityState = 2;

                            // Set default department shift times if not already set
                            if (availabilityBloc
                                        .availabilityDayList[index].FromTime ==
                                    null ||
                                availabilityBloc.availabilityDayList[index]
                                    .FromTime!.isEmpty ||
                                availabilityBloc
                                        .availabilityDayList[index].FromTime ==
                                    '00:00') {
                              availabilityBloc.availabilityDayList[index]
                                  .FromTime = availabilityBloc
                                      .availabilityDayList[index]
                                      .rosterStartTime ??
                                  '00:00';
                            }

                            if (availabilityBloc
                                        .availabilityDayList[index].ToTime ==
                                    null ||
                                availabilityBloc.availabilityDayList[index]
                                    .ToTime!.isEmpty ||
                                availabilityBloc
                                        .availabilityDayList[index].ToTime ==
                                    '00:00') {
                              availabilityBloc.availabilityDayList[index]
                                  .ToTime = availabilityBloc
                                      .availabilityDayList[index]
                                      .rosterEndTime ??
                                  '00:00';
                            }
                          }
                        }
                      });

                      // Update week completion status after changing availability states
                      availabilityBloc
                          .updateWeekCompletionStatus(widget.iosWeekYear ?? '');
                    }),
              ],
            ),
            body: PopScope(
              // canPop: t,
              onPopInvokedWithResult: (didPop, result) {
                log("===========>${availabilityBloc.availableWeekInfo?.numberOfServices ?? '0'}");
                log("===========>${availabilityBloc.availableWeekInfo1?.numberOfServices ?? '0'}");
                log("===========>${availabilityBloc.availableWeekInfo?.Remark}");
                log("===========>${availabilityBloc.availableWeekInfo1?.Remark}");

                if (availabilityBloc.availableWeekInfo1 !=
                        availabilityBloc.availableWeekInfo ||
                    availabilityBloc.isNumberOfServiceUpdate ||
                    availabilityBloc.isRemarkUpdate) {
                  availabilityBloc.settWeekShiftInfoData(
                      context: context,
                      iosYearWeek: widget.iosWeekYear ?? '',
                      numberService: availabilityBloc
                          .availableWeekInfo!.numberOfServices
                          .toString(),
                      remark: availabilityBloc.availableWeekInfo?.Remark);
                }

                isApiCall = !listEquals(availabilityBloc.availabilityDayList,
                    availabilityBloc.availabilityDayList1);

                if (isApiCall) {
                  dayWeekList.clear();
                  availabilityBloc.availabilityDayList
                      .asMap()
                      .forEach((index, day) {
                    // Use default department times if no times are set or if they are 00:00
                    String fromTime = day.FromTime ?? '';
                    String toTime = day.ToTime ?? '';

                    if (fromTime.isEmpty || fromTime == '00:00') {
                      fromTime = day.rosterStartTime ?? '00:00';
                    }

                    if (toTime.isEmpty || toTime == '00:00') {
                      toTime = day.rosterEndTime ?? '00:00';
                    }

                    dayWeekList.add(
                      DayWeekListModel(
                        dayNumber: index + 1,
                        availabilityState: day.availabilityState ?? 0,
                        FromTime: fromTime,
                        remark: day.remark ?? '',
                        ToTime: toTime,
                      ),
                    );
                  });

                  List<Map<String, dynamic>> dayWeekJsonList =
                      dayWeekList.map((dayWeek) => dayWeek.toJson()).toList();
                  log("jsonList ======>$dayWeekJsonList");

                  availabilityBloc.settWeekShiftDetailListData(
                    context: context,
                    iosYearWeek: widget.iosWeekYear ?? '',
                    dayWeekList: dayWeekJsonList,
                  );
                }
                Navigator.canPop(context);
              },
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Column(
                      children: [
                        Container(
                          height: AppSize.h38,
                          color: context.themeColors.listGridColor1,
                          padding:
                              EdgeInsets.symmetric(horizontal: AppSize.w10),
                          alignment: Alignment.centerLeft,
                          child: Text(
                              AppLocalizations.of(context)!.weekInformation,
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.themeColors.textColor,
                                fontSize: AppSize.sp15,
                              )),
                        ),
                        ValueListenableBuilder(
                          valueListenable: availabilityBloc.isWeekInfoLoading,
                          builder: (context, value, child) {
                            if (!availabilityBloc.isWeekInfoLoading.value) {
                              return Column(
                                children: [
                                  RemarkTextFiled(
                                    // controller: controller,
                                    onChanged: (value) {
                                      availabilityBloc.availableWeekInfo
                                              ?.numberOfServices =
                                          int.tryParse(value) ?? 0;
                                      availabilityBloc.numberOfServicesUpdate(
                                          availabilityBloc.availableWeekInfo
                                                  ?.numberOfServices ??
                                              0);
                                    },
                                    initialValue: (availabilityBloc
                                                    .availableWeekInfo
                                                    ?.numberOfServices !=
                                                '' &&
                                            availabilityBloc.availableWeekInfo
                                                    ?.numberOfServices !=
                                                null)
                                        ? availabilityBloc.availableWeekInfo
                                                ?.numberOfServices
                                                .toString() ??
                                            '0'
                                        : '0',
                                    prefixText:
                                        AppLocalizations.of(context)!.shifts,
                                    focusBorderColor: true,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    //maxLines: 1,
                                  ),
                                  TextFormField(
                                    maxLines: 2,
                                    initialValue: (availabilityBloc
                                                    .availableWeekInfo
                                                    ?.Remark !=
                                                '' &&
                                            availabilityBloc.availableWeekInfo
                                                    ?.Remark !=
                                                null)
                                        ? availabilityBloc
                                            .availableWeekInfo?.Remark
                                            .toString()
                                        : '',
                                    onChanged: (value) {
                                      availabilityBloc
                                          .availableWeekInfo?.Remark = value;
                                      availabilityBloc.remarkUpdate(
                                          availabilityBloc
                                                  .availableWeekInfo?.Remark ??
                                              '');
                                    },
                                    keyboardType: TextInputType.multiline,
                                    textInputAction: TextInputAction.newline,
                                    decoration: InputDecoration(
                                      // prefixText: prefixText ?? '',
                                      prefixIcon: Padding(
                                        padding: EdgeInsets.only(
                                            left: AppSize.w10,
                                            right: AppSize.w10,
                                            top: AppSize.h12,
                                            bottom: AppSize.h14),
                                        child: Text(
                                            AppLocalizations.of(context)!
                                                .remarkDot,
                                            textAlign: TextAlign.center,
                                            style: context.textTheme.bodyMedium
                                                ?.copyWith(
                                              color:
                                                  context.themeColors.textColor,
                                              fontSize: AppSize.sp15,
                                            )),
                                      ),
                                      enabledBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(
                                            width: 0,
                                            color:
                                                context.themeColors.greyColor),
                                      ),
                                      focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 2,
                                              color: AppColors.white)),
                                      // hintText: hintText,
                                      hintStyle: context.textTheme.bodyMedium
                                          ?.copyWith(
                                              fontSize: AppSize.sp14,
                                              color: context
                                                  .themeColors.greyColor),
                                      contentPadding: EdgeInsets.only(
                                        left: AppSize.w10,
                                        right: AppSize.w10,
                                        top: AppSize.h14,
                                      ),
                                    ),
                                    cursorColor: context.themeColors.greyColor,
                                  ),
                                  SpaceV(AppSize.h20),
                                ],
                              );
                            } else {
                              return WeekInfoShimmerWidget();
                            }
                          },
                        )
                      ],
                    ),
                    Column(
                      children: List.generate(
                          widget.dateRange!.end
                                  .difference(widget.dateRange!.start)
                                  .inDays +
                              1, (index) {
                        DateTime date =
                            widget.dateRange!.start.add(Duration(days: index));
                        DateTime startDate = widget.dateRange!.start;
                        DateTime endDate = widget.dateRange!.end;
                        int lastIndex = endDate.difference(startDate).inDays;
                        String month = getMonth(date.month);
                        String weekday = getWeekday(
                            date.weekday); // Fetch the localized weekday
                        // String formattedDate = DateFormat('d MMMM').format(date);
                        String formattedDate =
                            "${date.day.toString().padLeft(2, '0')} $month"; // Combine the day and localized month
                        log("formattedDate $formattedDate");
                        log("formattedDate ${date.day.toString().padLeft(2, '0')} $month");
                        return Column(
                          children: [
                            Container(
                              height: AppSize.h38,
                              color: context.themeColors.listGridColor1,
                              padding:
                                  EdgeInsets.symmetric(horizontal: AppSize.w10),
                              alignment: Alignment.centerLeft,
                              child: Text("$weekday  ${formattedDate}",
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp15,
                                  )),
                            ),
                            ValueListenableBuilder(
                              valueListenable:
                                  availabilityBloc.isWeekDayLoading,
                              builder: (ctx, isWeekDayLoading, child) {
                                if (isWeekDayLoading == false &&
                                    availabilityBloc
                                        .availabilityDayList.isNotEmpty) {
                                  if (availabilityBloc
                                          .availabilityDayList[index]
                                          .editRight ==
                                      false) {
                                    return Row(
                                      //crossAxisAlignment: CrossAxisAlignment.start,
                                      // mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: AppSize.h10,
                                              horizontal: AppSize.w10),
                                          child: Text(
                                              AppLocalizations.of(context)!
                                                  .availabilityClosed,
                                              textAlign: TextAlign.start,
                                              style: context
                                                  .textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: context
                                                    .themeColors.textColor,
                                                fontSize: AppSize.sp14,
                                              )),
                                        ),
                                      ],
                                    );
                                  } else {
                                    return Column(
                                      children: [
                                        Container(
                                          height: AppSize.h38,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: AppSize.w10),
                                          child: Row(
                                            children: [
                                              Text(
                                                  AppLocalizations.of(context)!
                                                      .available,
                                                  style: context
                                                      .textTheme.bodyMedium
                                                      ?.copyWith(
                                                    color: context
                                                        .themeColors.textColor,
                                                    fontSize: AppSize.sp15,
                                                  )),
                                              SpaceH(AppSize.w6),
                                              GestureDetector(
                                                onTap: () {
                                                  final selectedColorNotifier =
                                                      availabilityBloc
                                                              .containerColors[
                                                          index];
                                                  final availabilityState =
                                                      availabilityBloc
                                                          .availabilityDayList[
                                                              index]
                                                          .availabilityState;

                                                  if (availabilityState == 0) {
                                                    availabilityBloc
                                                        .availabilityDayList[
                                                            index]
                                                        .availabilityState = 1;
                                                    selectedColorNotifier
                                                            .value =
                                                        AppColors
                                                            .limeGreenColor;
                                                  } else if (availabilityState ==
                                                      1) {
                                                    availabilityBloc
                                                        .availabilityDayList[
                                                            index]
                                                        .availabilityState = 2;
                                                    selectedColorNotifier
                                                            .value =
                                                        AppColors
                                                            .lightModeRedColor;
                                                  } else if (availabilityState ==
                                                      2) {
                                                    availabilityBloc
                                                        .availabilityDayList[
                                                            index]
                                                        .availabilityState = 0;
                                                    selectedColorNotifier
                                                            .value =
                                                        Color.fromRGBO(46, 76,
                                                            112, 1); // Blue
                                                  }

                                                  // Update week completion status after individual day change
                                                  availabilityBloc
                                                      .updateWeekCompletionStatus(
                                                          widget.iosWeekYear ??
                                                              '');
                                                },
                                                child: ValueListenableBuilder(
                                                  builder:
                                                      (context, value, child) {
                                                    final availabilityState =
                                                        availabilityBloc
                                                            .availabilityDayList[
                                                                index]
                                                            .availabilityState;
                                                    Color? bgColor;
                                                    Color? dividerColor;

                                                    if (availabilityState ==
                                                        0) {
                                                      bgColor = Color.fromRGBO(
                                                          46,
                                                          76,
                                                          112,
                                                          1); // Blue
                                                      dividerColor = AppColors
                                                          .white; // Blue
                                                    } else if (availabilityState ==
                                                        1) {
                                                      bgColor = AppColors
                                                          .limeGreenColor;
                                                      dividerColor =
                                                          Colors.transparent;
                                                    } else if (availabilityState ==
                                                        2) {
                                                      bgColor = AppColors
                                                          .lightModeRedColor;
                                                      dividerColor =
                                                          Colors.transparent;
                                                    }

                                                    return Container(
                                                      height: AppSize.w20,
                                                      width: AppSize.w20,
                                                      decoration: BoxDecoration(
                                                        color: bgColor,
                                                        shape: BoxShape.circle,
                                                      ),
                                                    );
                                                  },
                                                  valueListenable:
                                                      availabilityBloc
                                                              .containerColors[
                                                          index],
                                                ),
                                              ),
                                              Spacer(),
                                              Row(
                                                children: [
                                                  InkWell(
                                                    splashColor:
                                                        Colors.transparent,
                                                    onTap: () {
                                                      (availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .rosterStartTime !=
                                                                  null &&
                                                              availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .rosterEndTime !=
                                                                  null)
                                                          ? showModalBottomSheet(
                                                              context: context,
                                                              builder:
                                                                  (BuildContext
                                                                      context) {
                                                                print(
                                                                    "from ==========....${availabilityBloc.availabilityDayList[index].FromTime.toString()}");
                                                                print(
                                                                    "to ==========....${availabilityBloc.availabilityDayList[index].ToTime.toString()}");
                                                                print(
                                                                    "start ==========....${availabilityBloc.availabilityDayList[index].rosterStartTime.toString()}");
                                                                print(
                                                                    "end ==========....${availabilityBloc.availabilityDayList[index].rosterEndTime.toString()}");
                                                                return FractionallySizedBox(
                                                                  heightFactor:
                                                                      0.6,
                                                                  child: Column(
                                                                    children: [
                                                                      Row(
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.end,
                                                                        children: [
                                                                          TextButton(
                                                                            onPressed:
                                                                                () {
                                                                              Navigator.pop(context);
                                                                            },
                                                                            child:
                                                                                Text(
                                                                              AppLocalizations.of(context)!.cancelText.toUpperCase(),
                                                                              style: context.textTheme.headlineLarge?.copyWith(
                                                                                fontSize: AppSize.sp14,
                                                                                fontWeight: FontWeight.w500,
                                                                                color: context.themeColors.primaryColor,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          TextButton(
                                                                            onPressed:
                                                                                () {
                                                                              // setState(() {
                                                                              //   availabilityBloc
                                                                              //           .availabilityDayList[index].FromTime =
                                                                              //       availabilityBloc.timeSlots[
                                                                              //           availabilityBloc
                                                                              //               .selectedTimeSlotIndex.value];
                                                                              //   availabilityBloc
                                                                              //           .availabilityDayList[index].ToTime =
                                                                              //       availabilityBloc.timeUntilSlots[
                                                                              //           availabilityBloc
                                                                              //               .selectedTimeUntilSlotIndex
                                                                              //               .value];
                                                                              // });
                                                                              availabilityBloc.fromTimeAndToTimeDisplayUpdate(index);
                                                                              Navigator.pop(context);
                                                                            },
                                                                            child:
                                                                                Text(
                                                                              AppLocalizations.of(context)!.oK.toUpperCase(),
                                                                              style: context.textTheme.headlineLarge?.copyWith(
                                                                                fontSize: AppSize.sp14,
                                                                                fontWeight: FontWeight.w500,
                                                                                color: context.themeColors.primaryColor,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      Expanded(
                                                                        child:
                                                                            Row(
                                                                          mainAxisAlignment:
                                                                              MainAxisAlignment.center,
                                                                          children: [
                                                                            Padding(
                                                                              padding: EdgeInsets.only(left: AppSize.w40),
                                                                              child: Text(
                                                                                AppLocalizations.of(context)!.from1,
                                                                                style: context.textTheme.titleMedium?.copyWith(
                                                                                  fontSize: AppSize.sp20,
                                                                                  color: AppColors.primaryColor,
                                                                                ),
                                                                              ),
                                                                            ),
                                                                            Flexible(
                                                                                child: TimeSlotPicker(
                                                                              startTime: availabilityBloc.availabilityDayList[index].rosterStartTime ?? '00:00',
                                                                              endTime: availabilityBloc.availabilityDayList[index].rosterEndTime ?? '00:00',
                                                                              interval: 15,
                                                                              initialTime: (availabilityBloc.availabilityDayList[index].FromTime.toString().isEmpty || availabilityBloc.availabilityDayList[index].FromTime == null) ? availabilityBloc.availabilityDayList[index].rosterStartTime.toString() : availabilityBloc.availabilityDayList[index].FromTime.toString(),
                                                                              onTimeSelected: (String) {},
                                                                              onSelectedItemChanged: (p0) {
                                                                                availabilityBloc.fromTimeDisplayUpdate(
                                                                                  p0, // Convert p0 to a String
                                                                                  index,
                                                                                );

                                                                                print("From ========>${availabilityBloc.timeSlots[availabilityBloc.selectedTimeSlotIndex.value]}");
                                                                              },
                                                                            )),
                                                                            Text(
                                                                              AppLocalizations.of(context)!.untill1,
                                                                              style: context.textTheme.titleMedium?.copyWith(
                                                                                fontSize: AppSize.sp20,
                                                                                color: AppColors.primaryColor,
                                                                              ),
                                                                            ),
                                                                            Flexible(
                                                                              child: TimeUntilSlotPicker(
                                                                                startTime: availabilityBloc.availabilityDayList[index].rosterStartTime.toString(),
                                                                                endTime: availabilityBloc.availabilityDayList[index].rosterEndTime.toString(),
                                                                                interval: 15,
                                                                                initialTime: (availabilityBloc.availabilityDayList[index].ToTime.toString().isEmpty || availabilityBloc.availabilityDayList[index].ToTime == null) ? availabilityBloc.availabilityDayList[index].rosterEndTime.toString() : availabilityBloc.availabilityDayList[index].ToTime.toString(),
                                                                                onTimeSelected: (String) {},
                                                                                onSelectedItemChanged: (p0) {
                                                                                  // setState(() {
                                                                                  //   availabilityBloc
                                                                                  //       .selectedTimeUntilSlotIndex
                                                                                  //       .value = p0;
                                                                                  //   availabilityBloc
                                                                                  //           .availabilityDayList[index]
                                                                                  //           .FromTime =
                                                                                  //       availabilityBloc.timeUntilSlots[
                                                                                  //           availabilityBloc
                                                                                  //               .selectedTimeUntilSlotIndex
                                                                                  //               .value];
                                                                                  // });
                                                                                  availabilityBloc.toTimeDisplayUpdate(p0, index);
                                                                                  print("until time ========>${availabilityBloc.timeUntilSlots[availabilityBloc.selectedTimeUntilSlotIndex.value]}");
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                );
                                                              },
                                                            )
                                                          : SizedBox();
                                                    },
                                                    child: Text(
                                                      // Display the selected time for the second GestureDetector
                                                      (availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .ToTime ==
                                                                  '' ||
                                                              availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .ToTime ==
                                                                  null ||
                                                              availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .FromTime ==
                                                                  null ||
                                                              availabilityBloc
                                                                      .availabilityDayList[
                                                                          index]
                                                                      .FromTime ==
                                                                  '')
                                                          ? '${availabilityBloc.availabilityDayList[index].rosterStartTime ?? '00:00'}-${availabilityBloc.availabilityDayList[index].rosterEndTime ?? '00:00'}'
                                                          : '${availabilityBloc.availabilityDayList[index].FromTime}-${availabilityBloc.availabilityDayList[index].ToTime}',
                                                      style: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                        color: (availabilityBloc.availabilityDayList[index].ToTime != null &&
                                                                availabilityBloc
                                                                        .availabilityDayList[
                                                                            index]
                                                                        .FromTime !=
                                                                    null &&
                                                                DateTime.parse(
                                                                        '2023-01-01 ${availabilityBloc.availabilityDayList[index].FromTime}')
                                                                    .isAfter(DateTime
                                                                        .parse(
                                                                            '2023-01-01 ${availabilityBloc.availabilityDayList[index].ToTime}')))
                                                            ? AppColors
                                                                .darkModeRedColor
                                                            : context
                                                                .themeColors
                                                                .textColor,
                                                        fontSize: AppSize.sp15,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                        ),
                                        Divider(
                                          color: context.themeColors
                                              .dividerAvailbilityColor,
                                          height: 0,
                                          thickness: 1.2,
                                        ),
                                        RemarkTextFiled(
                                          initialValue: availabilityBloc
                                              .availabilityDayList[index]
                                              .remark,
                                          onChanged: (value) {
                                            availabilityBloc
                                                .availabilityDayList[index]
                                                .remark = value;
                                            setState(() {});
                                            // remarkNotifier.value =
                                            // value;
                                            print(
                                                "remark12 ========>${availabilityBloc.availabilityDayList[1].remark}"); //
                                            setState(
                                                () {}); // Update the ValueNotifier
                                          },
                                        ),
                                      ],
                                    );
                                  }
                                } else {
                                  return WeekInfoShimmerWidget();
                                }
                              },
                            ),
                          ],
                        );
                        //   }
                      }),
                    )
                  ],
                ),
              ),
            ));
      },
    );
  }
}
