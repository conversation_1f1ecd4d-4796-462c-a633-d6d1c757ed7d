import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../main.dart';
import '../../../service/api_service/api_function.dart';
import '../../../service/api_service/server_constants.dart';

class availabilityApiProvider {
  DateTime selectedDate = DateTime.now();

  Future<Response?> getWeekShiftInfoDataApiCall(BuildContext context, String? iosYearWeek) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOYearWeek": iosYearWeek,
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.availabilityYearWeek,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> settWeekShiftInfoDataApi(
      BuildContext context, String? iosYearWeek, String? numberService, String? remark) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearWeek": iosYearWeek,
        "NumberOfServices": numberService,
        "Remark": remark
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.availabilityYearWeekSave,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> getWeekShiftDetailListInfoDataApiCall(BuildContext context, String? iosYearWeek) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOYearWeek": iosYearWeek,
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.availabilityYearWeekDetailList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> setWeekShiftDetailListInfoDataApiCall(
      BuildContext context, String? iosYearWeek, List? dayWeekList) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearWeek": iosYearWeek,
        "Availability": dayWeekList
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.availabilityYearWeekDaySave,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
