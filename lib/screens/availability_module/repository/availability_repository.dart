import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/availability_module/repository/availability_provider.dart';

import '../model/availability_yearweak_detail_model.dart';
import '../model/availbilty_day_details_model.dart';

class availabilityApiRepository {
  final availabilityProvider = availabilityApiProvider();

  Future<AvailabilityYearWeekResponseModel?> getWeekShiftInfoDataApi(
      {required BuildContext context, required String iosYearWeek}) async {
    final response = await availabilityProvider.getWeekShiftInfoDataApiCall(context, iosYearWeek);

    if (response != null) {
      return AvailabilityYearWeekResponseModel.fromJson(response.data, response.statusCode!);
    } else {
      return null;
    }
  }

  Future<Response?> settWeekShiftInfoDataApiCall(
      {required BuildContext context,
      required String iosYearWeek,
      required String numberService,
      required String? remark}) async {
    final response = await availabilityProvider.settWeekShiftInfoDataApi(context, iosYearWeek, numberService, remark);

    return response;
  }

  Future<List<AvailabilityWeekDayListResponseModel>?> getWeekShiftDetailListInfoDataApi(
      {required BuildContext context, required String iosYearWeek}) async {
    final response = await availabilityProvider.getWeekShiftDetailListInfoDataApiCall(context, iosYearWeek);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<AvailabilityWeekDayListResponseModel> dayDetail = [];

      for (var item in dataList) {
        dayDetail.add(AvailabilityWeekDayListResponseModel.fromJson(item, response.statusCode!));
      }

      return dayDetail;
    } else {
      return null;
    }
  }

  Future<Response?> setWeekShiftDetailListInfoDataApi(
      {required BuildContext context, required String iosYearWeek, required List? dayWeekList}) async {
    final response =
        await availabilityProvider.setWeekShiftDetailListInfoDataApiCall(context, iosYearWeek, dayWeekList);

    if (response != null) {
      // return SetWeekListResponse.fromJson(response.data,response.statusCode!);
      return response;
    } else {
      return null;
    }
  }
}
