import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/availability_module/model/availability_yearweak_detail_model.dart';
import 'package:staff_medewerker/screens/availability_module/model/availbilty_day_details_model.dart';
import 'package:staff_medewerker/screens/availability_module/repository/availability_repository.dart';

part 'availability_state.dart';

class AvailabilityCubit extends Cubit<AvailabilityState> {
  AvailabilityCubit() : super(AvailabilityInitial());
  final availabilityApiRepository availabilityApi = availabilityApiRepository();

  ValueNotifier<int> initialFetchYear = ValueNotifier(2);
  ValueNotifier<String> shift = ValueNotifier('');
  ValueNotifier<bool> isWeekInfoLoading = ValueNotifier(false);
  ValueNotifier<bool> isWeekMainInfoLoading = ValueNotifier(false);
  ValueNotifier<bool> isWeekDayLoading = ValueNotifier(false);
  ValueNotifier<int> selectedTimeSlotIndex = ValueNotifier(0);
  ValueNotifier<int> selectedTimeUntilSlotIndex = ValueNotifier(0);
  ValueNotifier<Map<int, bool>> weekDaysEditedMap = ValueNotifier({});
  ValueNotifier<List<bool?>> currentYearWeekDaysEditedList = ValueNotifier([]);
  ValueNotifier<List<bool?>> nextYearWeekDaysEditedList = ValueNotifier([]);
  ValueNotifier<bool> isFirstAPICallCompleted = ValueNotifier(false);
  ValueNotifier<bool> isSecondAPICallCompleted = ValueNotifier(false);

  ScrollController scrollController = ScrollController();

  String remarkList = '';
  String remarkValue = '';
  bool isFromSelected = false;
  int currentYear = DateTime.now().year;
  int currentWeekNumber =
      DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays ~/
              7 +
          1;
  // int currentWeekNumber = int.parse('${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}');
  int remainingWeeks = 0;
  int year = 0;
  bool? currentWeekDaysEdited;
  bool? nextWeekDaysEdited;

  AvailabilityYearWeekResponseModel? availableWeekInfo;
  AvailabilityYearWeekResponseModel? availableMainWeekInfo;
  AvailabilityYearWeekResponseModel? availableWeekInfo1;
  // check number of service and remark update
  bool isNumberOfServiceUpdate = false;
  bool isRemarkUpdate = false;

  // Store week data by iosYearWeek for better data management
  Map<String, AvailabilityYearWeekResponseModel> weekDataCache = {};
  List<AvailabilityWeekDayListResponseModel> availabilityDayList = [];
  List<AvailabilityWeekDayListResponseModel> availabilityDayList1 = [];

  List<ValueNotifier<Color>> containerColors = List.generate(
      8, (index) => ValueNotifier<Color>(Color.fromRGBO(46, 76, 112, 1)));
  List<String> timeSlots = [];
  List<String> timeUntilSlots = [];
  List<int> nextWeeks = [];
  List<int> currentYearWeekNumberList = [];
  List<int> nextYearWeekNumberList = [];

  DateTimeRange getISOWeekDateRange(int year, int weekNumber) {
    DateTime januaryFourth = DateTime(year, 1, 4);
    int daysToMonday = 1 - januaryFourth.weekday;
    DateTime startOfWeek =
        januaryFourth.add(Duration(days: daysToMonday + (weekNumber - 1) * 7));
    DateTime endOfWeek = startOfWeek.add(Duration(days: 6));

    if (endOfWeek.year != year) {
      endOfWeek = DateTime(year, 12, 31);
    }

    return DateTimeRange(start: startOfWeek, end: endOfWeek);
  }

  List<int> generateWeekNumbers(int year, int numberOfWeeks) {
    List<int> weekNumbers = [];
    for (int i = 1; i <= numberOfWeeks; i++) {
      weekNumbers.add(i);
    }
    return weekNumbers;
  }

  Future<void> getWeekShiftInfoData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isWeekInfoLoading.value = true;
    final response = await availabilityApi.getWeekShiftInfoDataApi(
        context: context, iosYearWeek: iosYearWeek);
    availableWeekInfo = response!;
    availableWeekInfo1 = response;
    isWeekInfoLoading.value = false;
    if (response.statusCode != 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }

  Future<void> getWeekMainShiftInfoData({
    required BuildContext context,
    required String iosYearWeek,
    required int weekNumber,
  }) async {
    // Check if data already exists in cache to avoid repeated API calls
    if (weekDataCache.containsKey(iosYearWeek)) {
      return;
    }

    isWeekMainInfoLoading.value = true;
    final response = await availabilityApi.getWeekShiftInfoDataApi(
        context: context, iosYearWeek: iosYearWeek);
    isWeekMainInfoLoading.value = false;

    if (response?.statusCode == 200) {
      // Store data in cache for this specific week
      weekDataCache[iosYearWeek] = response!;

      // Update the main reference for backward compatibility
      availableMainWeekInfo = response;

      // Update the weekDaysEditedMap
      final updatedMap = Map<int, bool>.from(weekDaysEditedMap.value);
      updatedMap[weekNumber] = response.WeekDaysEdited!;
      weekDaysEditedMap.value = updatedMap;
    }

    // log('availableWeekInfo ======>${availableMainWeekInfo?.WeekDaysEdited}');
    // log('availableWeekInfo ======>${availableMainWeekInfo?.EditRight}');
    // log('availableWeekInfo ======>${availableMainWeekInfo?.iSOYearWeek}');

    if (response?.statusCode != 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }

  Future<void> settWeekShiftInfoData(
      {required BuildContext context,
      required String iosYearWeek,
      required String numberService,
      required String? remark}) async {
    // isWeekInfoLoading.value = true;
    final response = await availabilityApi.settWeekShiftInfoDataApiCall(
        context: context,
        iosYearWeek: iosYearWeek,
        numberService: numberService,
        remark: remark);
    // isWeekInfoLoading.value = false;
    if (response?.statusCode == 200 && response?.data['Done'] == 1) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.saved,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );

      // Update the week completion status immediately after saving
      final weekNumber = int.tryParse(iosYearWeek.substring(4));
      if (weekNumber != null) {
        // Mark the week as saved (show check icon immediately)
        final updatedMap = Map<int, bool>.from(weekDaysEditedMap.value);
        updatedMap[weekNumber] = true;
        weekDaysEditedMap.value = updatedMap;
      }

      fetchAllYearWeekAvailabilityData();
    }
  }

  Future<void> getWeekShiftDetailListInfoData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isWeekDayLoading.value = true;
    final response = await availabilityApi.getWeekShiftDetailListInfoDataApi(
        context: context, iosYearWeek: iosYearWeek);
    availabilityDayList.clear();
    availabilityDayList1.clear();
    availabilityDayList = response!;
    availabilityDayList1 = response
        .map((item) => new AvailabilityWeekDayListResponseModel.clone(item))
        .toList();

    isWeekDayLoading.value = false;
    // if(response.statusCode != 200){
    //   customSnackBar(
    //     context: navigatorKey.currentContext!,
    //     message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
    //     actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
    //   );
    // }
  }

  Future<void> settWeekShiftDetailListData(
      {required BuildContext context,
      required String iosYearWeek,
      required List dayWeekList}) async {
    //isWeekInfoLoading.value = true;
    final response = await availabilityApi.setWeekShiftDetailListInfoDataApi(
        context: context, iosYearWeek: iosYearWeek, dayWeekList: dayWeekList);
    // isWeekInfoLoading.value = false;
    if (response?.statusCode == 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.saved,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );

      // Update the week completion status immediately after saving
      final weekNumber = int.tryParse(iosYearWeek.substring(4));
      if (weekNumber != null) {
        // Mark the week as saved (show check icon immediately)
        final updatedMap = Map<int, bool>.from(weekDaysEditedMap.value);
        updatedMap[weekNumber] = true;
        weekDaysEditedMap.value = updatedMap;
      }

      fetchAllYearWeekAvailabilityData();
    } else {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }

  // Method to get week data from cache
  AvailabilityYearWeekResponseModel? getWeekData(String iosYearWeek) {
    return weekDataCache[iosYearWeek];
  }

  // Method to check and update week completion status
  void updateWeekCompletionStatus(String iosYearWeek) {
    final weekNumber = int.tryParse(iosYearWeek.substring(4));
    if (weekNumber != null) {
      // Check if all days in the week are completed (either available=1 or unavailable=2)
      bool allDaysCompleted = availabilityDayList.every((day) =>
          day.availabilityState != null &&
          (day.availabilityState == 1 || day.availabilityState == 2));

      // Update the weekDaysEditedMap to reflect the actual completion status
      final updatedMap = Map<int, bool>.from(weekDaysEditedMap.value);
      updatedMap[weekNumber] = allDaysCompleted;
      weekDaysEditedMap.value = updatedMap;

      emit(AvailabilityInitial());
    }
  }

  // Method to clear cache if needed
  void clearWeekDataCache() {
    weekDataCache.clear();
  }

  // Method to clear all user-specific data on logout
  void clearAllUserData() {
    // Clear all ValueNotifiers
    weekDaysEditedMap.value = {};
    currentYearWeekDaysEditedList.value = [];
    nextYearWeekDaysEditedList.value = [];
    isFirstAPICallCompleted.value = false;
    isSecondAPICallCompleted.value = false;
    isWeekInfoLoading.value = false;
    isWeekMainInfoLoading.value = false;
    isWeekDayLoading.value = false;
    selectedTimeSlotIndex.value = 0;
    selectedTimeUntilSlotIndex.value = 0;
    initialFetchYear.value = 2;
    shift.value = '';

    // Clear all data models
    availableWeekInfo = null;
    availableMainWeekInfo = null;
    availableWeekInfo1 = null;
    weekDataCache.clear();
    availabilityDayList.clear();
    availabilityDayList1.clear();

    // Clear other data
    remarkList = '';
    remarkValue = '';
    isFromSelected = false;
    currentWeekDaysEdited = null;
    nextWeekDaysEdited = null;

    // Clear lists
    timeSlots.clear();
    timeUntilSlots.clear();
    nextWeeks.clear();
    currentYearWeekNumberList.clear();
    nextYearWeekNumberList.clear();

    // Reset container colors
    for (var notifier in containerColors) {
      notifier.value = Color.fromRGBO(46, 76, 112, 1);
    }

    emit(AvailabilityInitial());
  }

  // Method to debug cache contents
  void debugCacheContents() {
    print('=== Week Data Cache Contents ===');
    weekDataCache.forEach((key, value) {
      print(
          'Key: $key, EditRight: ${value.EditRight}, WeekDaysEdited: ${value.WeekDaysEdited}');
    });
    print('=== End Cache Contents ===');
  }

  Future<void> fetchAllYearWeekAvailabilityData() async {
    isFirstAPICallCompleted.value = false;
    DateTime now = DateTime.now();
    int currentYear = now.year;

    final futures = <Future<void>>[];

    // Match the UI logic: iterate through current year and next year
    for (int year = currentYear; year <= currentYear + 1; year++) {
      // Determine how many weeks to fetch for this year
      int startWeek = (year == currentYear) ? currentWeekNumber : 1;
      int endWeek =
          (year == currentYear) ? 52 : 25; // Fetch 25 weeks of next year

      for (int weekIndex = 0;
          weekIndex < (endWeek - startWeek + 1);
          weekIndex++) {
        int weekNumber = (year == currentYear)
            ? currentWeekNumber + weekIndex
            : weekIndex + 1;

        // Skip if week number exceeds 52
        if (weekNumber > 52) continue;

        String iosYearWeek = '$year${weekNumber.toString().padLeft(2, '0')}';

        final Future<void> future = getWeekMainShiftInfoData(
            context: navigatorKey.currentContext!,
            iosYearWeek: iosYearWeek,
            weekNumber: weekNumber);

        futures.add(future);
      }
    }

    try {
      await Future.wait(futures);
    } catch (e) {
      // Log error for debugging but don't print repeatedly
      print('Error fetching availability data: $e');
    }
    isFirstAPICallCompleted.value = true;
    emit(AvailabilityInitial());
  }

  void remarkUpdate(String value) {
    isRemarkUpdate = true;
    availableWeekInfo?.Remark = value;
    emit(AvailabilityInitial());
  }

  void updateNextYearEditList(bool? value) {
    nextWeekDaysEdited = value;
    emit(AvailabilityInitial());
  }

  void numberOfServicesUpdate(int value) {
    isNumberOfServiceUpdate = true;
    availableWeekInfo?.numberOfServices = value;
    emit(AvailabilityInitial());
  }

  void fromTimeUpdate(String timeSlot) {
    isFromSelected = timeSlots[selectedTimeSlotIndex.value] == timeSlot;
    emit(AvailabilityInitial());
  }

  void fromTimeDisplayUpdate(
    int timeSlot,
    int index,
  ) {
    selectedTimeSlotIndex.value = timeSlot;
    emit(AvailabilityInitial());
  }

  void toTimeDisplayUpdate(
    int timeSlot,
    int index,
  ) {
    selectedTimeUntilSlotIndex.value = timeSlot;
    emit(AvailabilityInitial());
  }

  void fromTimeAndToTimeDisplayUpdate(
    int index,
  ) {
    availabilityDayList[index].FromTime =
        timeSlots[selectedTimeSlotIndex.value];
    availabilityDayList[index].ToTime =
        timeUntilSlots[selectedTimeUntilSlotIndex.value];
    emit(AvailabilityInitial());
  }

  remarkListUpdate(int index, String value) {
    availabilityDayList[index].remark = value;
    emit(AvailabilityInitial());
  }
}
