import 'dart:convert';

List<PaySlipModel> paySlipModelFromJson(String str) => List<PaySlipModel>.from(json.decode(str).map((x) => PaySlipModel.fromJson(x)));

String paySlipModelToJson(List<PaySlipModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PaySlipModel {
  String fileId;
  String name;
  String dateTime;
  String fileType;

  PaySlipModel({
    required this.fileId,
    required this.name,
    required this.dateTime,
    required this.fileType,
  });

  factory PaySlipModel.fromJson(Map<String, dynamic> json) => PaySlipModel(
    fileId: json["FileId"],
    name: json["Name"],
    dateTime: json["DateTime"],
    fileType: json["FileType"],
  );

  Map<String, dynamic> toJson() => {
    "FileId": fileId,
    "Name": name,
    "DateTime": dateTime,
    "FileType": fileType,
  };
}
