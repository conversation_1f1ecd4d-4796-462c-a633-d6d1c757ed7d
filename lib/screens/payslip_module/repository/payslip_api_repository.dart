
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/payslip_module/repository/payslip_api_provider.dart';


import 'model.dart';

class PaySlipApiRepository {
  final paySlipProvider = PaySlipApiProvider();

  Future<List<PaySlipModel>?> paySlipApi({required BuildContext context}) async {
    final response = await paySlipProvider.paySlipApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<PaySlipModel> paySlips = [];

      for (var item in dataList) {

        paySlips.add(PaySlipModel.fromJson(item));
        print("api started =====>1${paySlips.last.fileId}");

      }
      //print("api started =====>${paySlips[0]}");

      return paySlips;
    } else {
      return null;
    }
  }


  Future<Response?> paySlipDocumentApi({required BuildContext context,required String guid}) async {
    final response = await paySlipProvider.paySlipDocumentApiCall(context,guid);

   return response;

  }

}