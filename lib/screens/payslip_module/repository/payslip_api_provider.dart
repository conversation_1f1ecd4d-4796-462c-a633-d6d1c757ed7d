import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class PaySlipApiProvider {
  Future<Response?> paySlipApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.payslip,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> paySlipDocumentApiCall(BuildContext context, String guid) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "guid": guid
      };

      print("object: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.payslipDocument,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
