import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/payslip_module/repository/model.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import '../bloc/payslip_screen_cubit.dart';

class PaySlipScreen extends StatelessWidget {
  const PaySlipScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.payslips),
      body: BlocProvider(
        create: (context) => PaySlipCubit()..paySlipApiCall(context: context),
        child: BlocBuilder<PaySlipCubit, bool>(
          builder: (ctx, state) {
            final payslipBloc = ctx.read<PaySlipCubit>();
            return ValueListenableBuilder(
              valueListenable: payslipBloc.isLoading,
              builder: (BuildContext context, isLoading, Widget? child) {
                if (!isLoading) {
                  return CustomScrollView(
                      slivers: buildSlivers(payslipBloc.paySlipList, context));
                } else if (payslipBloc.paySlipList.isEmpty ||
                    payslipBloc.paySlipList == null) {
                  return Container(
                      height: MediaQuery.of(context).size.height * 2,
                      width: MediaQuery.of(context).size.width,
                      child: Center(
                          child: Text(AppLocalizations.of(context)!
                              .noDocumentAvailableText)));
                } else {
                  return Column(
                    children: [
                      SpaceV(AppSize.h20),
                      Expanded(
                        child: ListView.builder(
                          itemCount: 20,
                          itemBuilder: (context, index) {
                            return ShimmerWidget(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 15),
                              height: AppSize.h10,
                            );
                          },
                        ),
                      ),
                    ],
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }

  List<Widget> buildSlivers(
    List<PaySlipModel> data,
    BuildContext context,
  ) {
    log("api done =====>${data}");

    // Sort data by DateTime in descending order (reverse order)
    data.sort((a, b) => b.dateTime.compareTo(a.dateTime));
    // Create a map to group data by year
    Map<int, List<dynamic>> groupedData = {};
    final payslipBloc = BlocProvider.of<PaySlipCubit>(context);
    for (var item in data) {
      DateTime dateTime = DateTime.parse(item.dateTime);
      int year = dateTime.year;

      if (!groupedData.containsKey(year)) {
        groupedData[year] = [];
      }

      groupedData[year]!.add(item);
    }

    // Create SliverStickyHeader and SliverList widgets for each year
    List<Widget> slivers = [];

    groupedData.forEach((year, yearData) {
      slivers.add(
        SliverStickyHeader(
          header: Container(
            height: AppSize.h38,
            color: context.themeColors.listGridColor1,
            padding: EdgeInsets.symmetric(horizontal: AppSize.sp15),
            alignment: Alignment.centerLeft,
            child: Text(
              year.toString(),
              style: context.textTheme.bodyMedium?.copyWith(
                  color: context.themeColors.textColor, fontSize: AppSize.sp14),
            ),
          ),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                log("api done =====>${data[index].fileId}    - ${index}");

                final file = yearData[index];
                return ListTile(
                  dense: true,
                  tileColor: context.themeColors.cardColor,
                  title: Text(file.name,
                      style: context.textTheme.bodyMedium?.copyWith(
                          color: context.themeColors.textColor,
                          fontSize: AppSize.sp13)),
                  trailing: IconButton(
                      onPressed: () async {
                        print("api started =====>${data[index].fileId}");
                        await payslipBloc.paySlipDocumentData(
                            context: context, guid: file.fileId);
                        // AppNavigation.nextScreen(context, ExternalViewer());
                      },
                      icon: Icon(
                        Ionicons.download,
                        color: context.themeColors.iconColor,
                      )),
                );
              },
              childCount: yearData.length,
            ),
          ),
        ),
      );
    });
    return slivers;
  }
}
