import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/app/bloc/app_theme_bloc/app_theme_cubit.dart';
import 'package:staff_medewerker/app/bloc/localization_cubit/language_cubit.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_listtile.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_switch.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/pin_module/bloc/pin_screen_cubit.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/screens/pin_module/pin_screen.dart';
import 'package:staff_medewerker/screens/settings_module/common_dialog.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  List<String> list = [
    AppLocalizations.of(navigatorKey.currentContext!)!.englishLanguage,
    AppLocalizations.of(navigatorKey.currentContext!)!.dutchLanguage
  ];
  List<String> list2 = [
    AppLocalizations.of(navigatorKey.currentContext!)!.weekText,
    AppLocalizations.of(navigatorKey.currentContext!)!.monthText
  ];
  List<String> list3 = ["1", "2", "3", "4", "5"];

  final pinBloc =
      BlocProvider.of<PinSetCubit>(navigatorKey.currentContext!, listen: true);

  ValueNotifier<int> dashBoardModeNotifier =
      ValueNotifier<int>(appDB.dashBoardMode);
  ValueNotifier<int> newItemNotifier = ValueNotifier<int>(appDB.newsItem);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    pinBloc.checkPinSetOrNot();
  }

  @override
  Widget build(BuildContext context) {
    print("dashBoardMode value: ${appDB.dashBoardMode}");
    print("dashBoardMode value: ${appDB.shareAnyltical}");
    return Scaffold(
        appBar: CustomAppBar(title: AppLocalizations.of(context)!.settings),
        body: BlocBuilder<LanguageCubit, Locale>(
          builder: (ctx, state) {
            String currentLanguageCode = state.languageCode;

            return ListView(
              children: [
                CustomListTile(
                  title: AppLocalizations.of(context)!.generalText,
                  bgColor: context.themeColors.listGridColor1,
                ),
                CustomListTile(
                  title: AppLocalizations.of(context)!.languageText,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        int selectedValue = 1;
                        print("currentLanguageCode$currentLanguageCode");
                        if (currentLanguageCode != "nl") {
                          selectedValue = 1;
                        } else {
                          selectedValue = 2;
                        }
                        return CustomRadioButtonDialog(
                            context: context,
                            dialogTitle:
                                AppLocalizations.of(context)!.languageText,
                            list: list,
                            onOKPressed: (selectedValue) {
                              if (selectedValue != 1) {
                                appDB.language = 'nl';

                                ctx
                                    .read<LanguageCubit>()
                                    .setLocale(Locale('nl'));
                                print("Selected value: $selectedValue");
                              } else {
                                appDB.language = 'en';

                                ctx
                                    .read<LanguageCubit>()
                                    .setLocale(Locale('en'));
                                print("Selected value: $selectedValue");
                              }
                            },
                            selectedValue: selectedValue);
                      },
                    );
                  },
                  trailingWidget: GestureDetector(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          currentLanguageCode != "nl"
                              ? AppLocalizations.of(context)!.englishLanguage
                              : AppLocalizations.of(context)!.dutchLanguage,
                          style: context.textTheme.headlineLarge?.copyWith(
                            fontSize: AppSize.sp14,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        SpaceH(AppSize.w6),
                        Icon(
                          Ionicons.caret_down,
                          color: context.themeColors.iconColor,
                          size: AppSize.sp12,
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  color: context.themeColors.dividerAvailbilityColor,
                  height: 0,
                  thickness: 1,
                ),
                CustomListTile(
                    title: AppLocalizations.of(context)!.dashBoardModeText,
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          if (appDB.dashBoardMode == 1) {
                            appDB.dashBoardMode = 1;
                          } else {
                            appDB.dashBoardMode = 2;
                          }
                          return CustomRadioButtonDialog(
                              context: context,
                              dialogTitle: AppLocalizations.of(context)!
                                  .dashBoardModeText,
                              list: list2,
                              onOKPressed: (selectedValue) {
                                print("Selected value: $selectedValue");
                                appDB.dashBoardMode = selectedValue;
                                dashBoardModeNotifier.value =
                                    appDB.dashBoardMode;
                                print(
                                    "Selected value: ${dashBoardModeNotifier.value}");
                                print(
                                    "dashBoardMode value: ${appDB.dashBoardMode}");

                                customSnackBar(
                                  context: navigatorKey.currentContext!,
                                  message: AppLocalizations.of(
                                          navigatorKey.currentContext!)!
                                      .reflectChanges,
                                  actionButtonText: AppLocalizations.of(
                                          navigatorKey.currentContext!)!
                                      .closeText
                                      .toUpperCase(),
                                );
                              },
                              selectedValue: appDB.dashBoardMode);
                        },
                      );
                    },
                    trailingWidget: GestureDetector(
                      // onTap: () {
                      //   showDialog(
                      //     context: context,
                      //     builder: (context) {
                      //
                      //       if (appDB.dashBoardMode == 1) {
                      //         appDB.dashBoardMode = 1;
                      //       } else {
                      //         appDB.dashBoardMode = 2;
                      //       }
                      //       return CustomRadioButtonDialog(
                      //           context: context,
                      //           dialogTitle: AppLocalizations.of(context)!
                      //               .dashBoardModeText,
                      //           list: list2,
                      //           onOKPressed: (selectedValue) {
                      //             print("Selected value: $selectedValue");
                      //             appDB.dashBoardMode = selectedValue;
                      //             dashBoardModeNotifier.value = appDB.dashBoardMode;
                      //             print("Selected value: ${dashBoardModeNotifier.value }");
                      //             print("dashBoardMode value: ${appDB.dashBoardMode}");
                      //
                      //           },
                      //           selectedValue: appDB.dashBoardMode);
                      //     },
                      //   );
                      // },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: dashBoardModeNotifier,
                            builder: (context, value, child) {
                              return Text(
                                value == 1
                                    ? AppLocalizations.of(context)!.weekText
                                    : AppLocalizations.of(context)!.monthText,
                                style:
                                    context.textTheme.headlineLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.normal,
                                ),
                              );
                            },
                          ),
                          SpaceH(AppSize.w6),
                          Icon(
                            Ionicons.caret_down,
                            color: context.themeColors.iconColor,
                            size: AppSize.sp12,
                          ),
                        ],
                      ),
                    )),
                Divider(
                  color: context.themeColors.dividerAvailbilityColor,
                  height: 0,
                  thickness: 1,
                ),
                CustomListTile(
                    title: AppLocalizations.of(context)!.amountOfNewsText,
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          if (appDB.newsItem == 1) {
                            appDB.newsItem = 1;
                          } else if (appDB.newsItem == 2) {
                            appDB.newsItem = 2;
                          } else if (appDB.newsItem == 3) {
                            appDB.newsItem = 3;
                          } else if (appDB.newsItem == 4) {
                            appDB.newsItem = 4;
                          } else if (appDB.newsItem == 5) {
                            appDB.newsItem = 5;
                          }

                          return CustomRadioButtonDialog(
                              context: context,
                              dialogTitle: AppLocalizations.of(context)!
                                  .amountOfNewsText,
                              list: list3,
                              onOKPressed: (selectedValue) {
                                // You can use the selectedValue for further processing
                                // ctx.read<LanguageCubit>().setLocale(Locale(switchLanguageCode));
                                print("Selected value: $selectedValue");
                                appDB.newsItem = selectedValue;
                                newItemNotifier.value = appDB.newsItem;
                                print("Selected value: ${appDB.newsItem}");
                              },
                              selectedValue: appDB.newsItem);
                        },
                      );
                    },
                    trailingWidget: GestureDetector(
                      // onTap: () {
                      //   showDialog(
                      //     context: context,
                      //     builder: (context) {
                      //
                      //       if (appDB.newsItem == 1) {
                      //         appDB.newsItem = 1;
                      //       } else if(appDB.newsItem == 2) {
                      //         appDB.newsItem = 2;
                      //       } else if (appDB.newsItem == 3){
                      //         appDB.newsItem = 3;
                      //       }else if (appDB.newsItem == 4){
                      //         appDB.newsItem = 4;
                      //       }else if (appDB.newsItem == 5){
                      //         appDB.newsItem = 5;
                      //       }
                      //
                      //       return CustomRadioButtonDialog(
                      //           context: context,
                      //           dialogTitle: AppLocalizations.of(context)!
                      //               .amountOfNewsText,
                      //           list: list3,
                      //           onOKPressed: (selectedValue) {
                      //             // You can use the selectedValue for further processing
                      //             // ctx.read<LanguageCubit>().setLocale(Locale(switchLanguageCode));
                      //             print("Selected value: $selectedValue");
                      //             appDB.newsItem = selectedValue;
                      //             newItemNotifier.value = appDB.newsItem;
                      //             print("Selected value: ${appDB.newsItem}");
                      //
                      //           },
                      //           selectedValue: appDB.newsItem);
                      //     },
                      //   );
                      // },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: newItemNotifier,
                            builder: (context, value, child) {
                              return Text(
                                newItemNotifier.value.toString(),
                                style:
                                    context.textTheme.headlineLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.normal,
                                ),
                              );
                            },
                          ),
                          // Text(
                          //   "1",
                          //   style: context.textTheme.headlineLarge?.copyWith(
                          //     fontSize: AppSize.sp14,
                          //     fontWeight: FontWeight.normal,
                          //   ),
                          // ),
                          SpaceH(AppSize.w6),
                          Icon(
                            Ionicons.caret_down,
                            color: context.themeColors.iconColor,
                            size: AppSize.sp12,
                          ),
                        ],
                      ),
                    )),
                Divider(
                  color: context.themeColors.dividerAvailbilityColor,
                  height: 0,
                  thickness: 1,
                ),
                CustomListTile(
                    title: AppLocalizations.of(context)!.darkThemeText,
                    trailingWidget: BlocBuilder<AppThemeCubit, bool>(
                      builder: (context, isDarkModeOn) {
                        // isDarkModeOn = appDB.darkTheme;
                        return CustomSwitch(
                          value: appDB.darkTheme,
                          onChanged: (value) {
                            context.read<AppThemeCubit>().toggleTheme();
                            appDB.darkTheme = value;
                          },
                          inactiveThumbColor: AppColors.white,
                          inactiveTrackColor: AppColors.darkModeGreyColor,
                        );
                      },
                    )),
                CustomListTile(
                    title: AppLocalizations.of(context)!.hideShiftEndTimeText,
                    trailingWidget: CustomSwitch(
                      value: appDB.hideShiftEndTime,
                      inactiveThumbColor: AppColors.white,
                      inactiveTrackColor: AppColors.darkModeGreyColor,
                      onChanged: (value) {
                        appDB.hideShiftEndTime = value;
                        setState(() {});
                      },
                    )),
                CustomListTile(
                  title: AppLocalizations.of(context)!.securityText,
                  bgColor: context.themeColors.listGridColor1,
                ),
                ListTile(
                  dense: true,
                  tileColor: context.themeColors.cardColor,
                  title: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Text(
                            "${AppLocalizations.of(context)!.usePinText}",
                            style: TextStyle(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp13,
                            ),
                          ),
                          PopupMenuButton(
                            tooltip: "",
                            splashRadius: 1,
                            elevation: 20,
                            iconSize: 50,
                            offset: Offset(10, 20),
                            child: Container(
                              padding: EdgeInsets.only(
                                bottom: AppSize.sp3,
                                left: AppSize.sp3,
                              ),
                              alignment: Alignment.centerRight,
                              child: Icon(
                                Ionicons.help_circle_outline,
                                size: AppSize.sp14,
                                color: context.themeColors.textColor,
                              ),
                            ),
                            color: context.themeColors.cardColor,
                            itemBuilder: (context) => [
                              PopupMenuItem(
                                child: Text(
                                    AppLocalizations.of(context)!
                                        .usePinHelpInfoText,
                                    softWrap: true,
                                    style: context.textTheme.bodyMedium
                                        ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color:
                                                context.themeColors.textColor)),
                                enabled: false,
                              ),
                            ],
                            // splashRadius: 1,
                          ),
                        ],
                      ),

                      // RichText(
                      //   text: TextSpan(
                      //     children: [
                      //       TextSpan(
                      //         text: "${AppLocalizations.of(context)!.usePinText}",
                      //         style: TextStyle(
                      //           color: context.themeColors.textColor,
                      //           fontSize: AppSize.sp13,
                      //         ),
                      //       ),
                      //       TextSpan(
                      //         text: " ?", // Add the question mark here
                      //         style: TextStyle(
                      //           color: context.themeColors.textColor,
                      //           fontSize: AppSize.sp13,
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // )
                    ],
                  ),
                  trailing: BlocBuilder<PinSetCubit, PinScreenMode>(
                    builder: (ctx, state) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: pinBloc.isPinSet,
                            builder: (context, isPinSet, child) {
                              return CustomSwitch(
                                value: isPinSet,
                                inactiveThumbColor: AppColors.white,
                                inactiveTrackColor: AppColors.darkModeGreyColor,
                                onChanged: (value) {
                                  ctx.read<PinSetCubit>().clearTextField();
                                  print("${appDB.savedPinNumber}");
                                  if (appDB.savedPinNumber == '' ||
                                      appDB.savedPinNumber == null ||
                                      appDB.savedPinNumber.isEmpty) {
                                    AppNavigation.nextScreen(
                                        context,
                                        EnterPinPage(
                                          isRemovePinScreen: false,
                                          isNavigateFromHomeScreen: false,
                                        ));
                                  } else {
                                    showDialog(
                                      context: context,
                                      builder: (context) {
                                        return CustomAlertDialog(
                                          context: context,
                                          title: AppLocalizations.of(context)!
                                              .disablePinText,
                                          message: AppLocalizations.of(context)!
                                              .disablePinMessageText,
                                          isCancelButton: true,
                                          okButtonText:
                                              AppLocalizations.of(context)!
                                                  .disablePinText,
                                          onOkPressed: () {
                                            Navigator.pop(context);
                                            AppNavigation.nextScreen(
                                                context,
                                                EnterPinPage(
                                                  isRemovePinScreen: true,
                                                  isNavigateFromHomeScreen:
                                                      false,
                                                ));
                                          },
                                        );
                                      },
                                    );
                                  }
                                },
                              );
                            },
                          )
                        ],
                      );
                    },
                  ),
                ),
                CustomListTile(
                  title: AppLocalizations.of(context)!.privacyText,
                  bgColor: context.themeColors.listGridColor1,
                ),
                CustomListTile(
                    title:
                        AppLocalizations.of(context)!.shareAnalyticalDataText,
                    trailingWidget: CustomSwitch(
                      value: appDB.shareAnyltical,
                      inactiveThumbColor: AppColors.white,
                      inactiveTrackColor: AppColors.darkModeGreyColor,
                      onChanged: (value) {
                        appDB.shareAnyltical = !appDB.shareAnyltical;
                        setState(() {});
                      },
                    )),
              ],
            );
          },
        ));
  }
}
