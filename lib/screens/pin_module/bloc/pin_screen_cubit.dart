import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/home_module/ui/bottom_bar_screen.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

// Define an enum to represent the PIN entry steps
enum PinScreenMode {
  SetFirstTimePin,
  RepeatSecondTimePin,
}

class PinSetCubit extends Cubit<PinScreenMode> {
  PinSetCubit() : super(PinScreenMode.SetFirstTimePin);

  ValueNotifier<String> enteredPinNumber = ValueNotifier("");
  ValueNotifier<String> pinScreenTitle = ValueNotifier(
      AppLocalizations.of(navigatorKey.currentContext!)!.enterPinFirstTimeText);
  String newPin = "";
  String repeatedPin = "";
  ValueNotifier<bool> isPinSet = ValueNotifier(false);

  Future<void> addPinNumber(
      {required BuildContext context,
      required String numberPressed,
      required bool isNavigateFromHomeScreen,
      required bool isFromHomeScreen}) async {
    final currentStep = state;
    print("isFromFirstTimewi =======>${isFromHomeScreen}");
    if (currentStep == PinScreenMode.SetFirstTimePin) {
      // Handle the first-time PIN entry logic
      if (enteredPinNumber.value.length < 4) {
        enteredPinNumber.value += numberPressed;
        print("pinNumber: ${enteredPinNumber.value}");
        newPin = enteredPinNumber.value;
      }

      // Automatically switch to the second-time PIN entry step when the length is 4
      //String setPin = prefs.getString(AppConstants.savedPinNumber) ?? "";
      String setPin = appDB.savedPinNumber;
      if (enteredPinNumber.value.length == 4 &&
          (setPin.isEmpty || setPin == "")) {
        switchToSecondTimePinStep();
      } else if (((setPin.isNotEmpty || setPin != "") && newPin.length == 4)) {
        if (isNavigateFromHomeScreen) {
          if (setPin == newPin) {
            print("pin verified successfully");
            enteredPinNumber.value = "";
            AppNavigation.nextScreen(context, BottomBarScreen());
          } else {
            enteredPinNumber.value = "";
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context1) {
                return CustomAlertDialog(
                  context: context1,
                  title: AppLocalizations.of(context)!.pinIncorrectText,
                  message:
                      AppLocalizations.of(context)!.pinIncorrectMessageText,
                  onOkPressed: () {
                    Navigator.pop(context);
                  },
                );
              },
            );
          }
        } else {
          if (setPin == newPin) {
            print("pin removed");
            Prefs.preferences.remove(AppConstants.savedPinNumber);
            appDB.savedPinNumber = '';
            enteredPinNumber.value = "";
            AppNavigation.previousScreen(context);
            // isFromHomeScreen == true ?     AppNavigation.nextScreen(context, BottomBarScreen()) : AppNavigation.previousScreen(context) ;
          } else {
            enteredPinNumber.value = "";
            customSnackBar(
                context: context,
                message: AppLocalizations.of(context)!.pinIncorrectText);
          }
        }

        checkPinSetOrNot();
      }
    } else if (currentStep == PinScreenMode.RepeatSecondTimePin) {
      // Handle the second-time PIN entry logic
      if (enteredPinNumber.value.length < 4) {
        enteredPinNumber.value += numberPressed;
        print("pinNumber2: ${enteredPinNumber.value}");
        repeatedPin = enteredPinNumber.value;

        if (enteredPinNumber.value.length == 4) {
          if (newPin == repeatedPin) {
            // Show a snackbar when both PINs match
            await Prefs().setString(AppConstants.savedPinNumber, repeatedPin);
            appDB.savedPinNumber = repeatedPin;
            print("savedPinNumber: ${appDB.savedPinNumber}");
            checkPinSetOrNot();
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context1) {
                return CustomAlertDialog(
                  context: context1,
                  title: AppLocalizations.of(context)!.pinSetText,
                  message: AppLocalizations.of(context)!.pinSetMessageText,
                  onOkPressed: () async {
                    if (isFromHomeScreen == true) {
                      AppNavigation.nextScreen(context, BottomBarScreen());
                    } else {
                      AppNavigation.previousScreen(context);
                      AppNavigation.previousScreen(
                          navigatorKey.currentContext!);
                    }

                    switchToFirstTimePinStep();
                    enteredPinNumber.value = '';
                  },
                );
              },
            );
          } else {
            // Pins don't match, reset PIN screen
            print("Pins don't match: ${enteredPinNumber.value}");
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context1) {
                return CustomAlertDialog(
                  context: context1,
                  title: AppLocalizations.of(context)!.pinNotMatchText,
                  message: AppLocalizations.of(context)!.errorPinNotMatchText,
                  onOkPressed: () {
                    Navigator.pop(context);
                    switchToFirstTimePinStep();
                    enteredPinNumber.value = '';
                  },
                );
              },
            );
          }
        }
      }
    }
  }

  void clearTextField() {
    enteredPinNumber.value = "";
    newPin = "";
    repeatedPin = "";
    emit(PinScreenMode.SetFirstTimePin);
    switchToFirstTimePinStep();
  }

  void removePinNumber({required BuildContext context}) {
    if (enteredPinNumber.value.isNotEmpty) {
      enteredPinNumber.value = enteredPinNumber.value
          .substring(0, enteredPinNumber.value.length - 1);
      print("pinNumber: ${enteredPinNumber.value}");
    }
  }

  // Function to switch to the second PIN entry step
  void switchToSecondTimePinStep() {
    emit(PinScreenMode.RepeatSecondTimePin);
    pinScreenTitle.value = AppLocalizations.of(navigatorKey.currentContext!)!
        .enterPinSecondTimeText;
    enteredPinNumber.value = "";
  }

  // Function to switch back to the first PIN entry step
  void switchToFirstTimePinStep() {
    emit(PinScreenMode.SetFirstTimePin);
    pinScreenTitle.value = AppLocalizations.of(navigatorKey.currentContext!)!
        .enterPinFirstTimeText;
    enteredPinNumber.value = "";
  }

  // Function to show a snackbar when both PINs match
  void showPinSetSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('PIN set'),
      ),
    );
  }

  // Function to check if a PIN is set
  void checkPinSetOrNot() {
    //String pin = Prefs().getString(AppConstants.savedPinNumber) ?? "";
    String pin = appDB.savedPinNumber;
    print("=====>pin $pin");
    if (pin == '' || pin.isEmpty) {
      isPinSet.value = false;
    } else {
      isPinSet.value = true;
    }
  }
}
