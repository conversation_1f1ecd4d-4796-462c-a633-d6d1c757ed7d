import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CircleIndicator extends StatelessWidget {
  final String pin;

  const CircleIndicator({Key? key, required this.pin}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 20.h,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: 4,
        shrinkWrap: true,
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(width: 13.w);
        },
        itemBuilder: (_, index) {
          return pin.length > index
              ? Icon(
                  Ionicons.radio_button_on,
                  color: AppColors.white,
                  size: AppSize.sp28,
                )
              : Icon(
                  Ionicons.radio_button_off,
                  color: AppColors.white,
                  size: AppSize.sp28,
                );
        },
      ),
    );
  }
}
