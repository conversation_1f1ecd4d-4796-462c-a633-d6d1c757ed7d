import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/about_app_module/send_feedback_screen/repository/feedback_repository.dart';

import '../../../../common/custom_widgets/common_snackbar.dart';

class SendFeedbackCubit extends Cubit<bool> {
  SendFeedbackCubit() : super(false);
  ImagePicker picker = ImagePicker();
  XFile? image;
  ValueNotifier<String> imagePath = ValueNotifier('');
  ValueNotifier<bool> checkBox1 = ValueNotifier(false);
  ValueNotifier<bool> checkBox2 = ValueNotifier(false);
  ValueNotifier<bool> inProgress = ValueNotifier(false);
  ValueNotifier<bool> isFeedBackLoading = ValueNotifier(false);
  final feedbackApiRepository feedBackApi = feedbackApiRepository();
  String imageBaseString = '';

  Future<void> pickImage(
      {required ImageSource profileImage,
      required BuildContext context}) async {
    image = await picker.pickImage(source: profileImage);
    imagePath.value = image!.path;
    inProgress.value = true;
    print("image: ${image!.path}");

    File imageFile = File(imagePath.value);
    Uint8List imageBytes = await imageFile.readAsBytes();

    // Convert the image bytes to a Base64 string
    imageBaseString = base64Encode(imageBytes);
    print("Base64 Image: $imageBaseString");
  }

  clearImage() {
    imagePath.value = "";
  }

  void changeValue(
      {required BuildContext context, bool? isAttachAnonymousDeviceInfo}) {
    checkBox1.value = isAttachAnonymousDeviceInfo ?? false;
  }

  void changeValue2(
      {required BuildContext context, bool? isAttachPersonalInfo}) {
    checkBox2.value = isAttachPersonalInfo ?? false;
  }

  Future<void> feedBackApiData(
      {required BuildContext context,
      required String? taskTest,
      required String? title,
      required String? attachment}) async {
    isFeedBackLoading.value = true;
    final response =
        // await feedBackApi.feedBackApi(context: context, taskTest: title, title: taskTest, attachment: attachment);
        await feedBackApi.feedBackApi(
            context: context,
            taskTest: taskTest,
            title: title,
            attachment: attachment);
    print('feedBackApiData ======>${taskTest}');
    print('feedBackApiData ======>${title}');
    print('feedBackApiData ======>${attachment}');
    print('feedBackApiData ======>${response}');
    isFeedBackLoading.value = false;
    if (response?.statusCode == 200 && response?.data['Done'] == true) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message:
            AppLocalizations.of(navigatorKey.currentContext!)!.thankFeedBack,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    } else {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!
            .thankFeedBackError,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }
}
