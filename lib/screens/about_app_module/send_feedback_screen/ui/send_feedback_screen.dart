import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_listtile.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_textfield.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/about_app_module/send_feedback_screen/bloc/send_feedback_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class SendFeedBackScreen extends StatelessWidget {
  TextEditingController titleController = TextEditingController();
  TextEditingController taskTextController = TextEditingController();
  Map<String, dynamic> deviceInfo1 = {};
  Map<String, dynamic> userInfo = {};
  String jsonDeviceInfo = '';
  String userJsonInfo = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          CustomAppBar(title: AppLocalizations.of(context)!.sendFeedbackText),
      body: BlocProvider(
        create: (context) => SendFeedbackCubit(),
        child: BlocBuilder<SendFeedbackCubit, bool>(
          builder: (ctx, state) {
            final sendFeedBackBloc = ctx.read<SendFeedbackCubit>();
            return Padding(
              padding: EdgeInsets.symmetric(
                  vertical: AppSize.sp27, horizontal: AppSize.sp14),
              child: ListView(
                children: [
                  Text(
                    AppLocalizations.of(context)!.doYouWantToReportText,
                    style: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp14),
                  ),
                  SpaceV(AppSize.h14),
                  CustomTextField(
                      controller: titleController,
                      hintText:
                          AppLocalizations.of(context)!.titleOrSubjectText),
                  SpaceV(AppSize.h10),
                  CustomTextField(
                      controller: taskTextController,
                      maxLines: 11,
                      hintText:
                          AppLocalizations.of(context)!.problemExplanationText,
                      border: InputBorder.none),
                  ValueListenableBuilder(
                    valueListenable: sendFeedBackBloc.imagePath,
                    builder: (context, imagePath, child) {
                      if (imagePath.isEmpty) {
                        return Ink(
                          width: double.infinity,
                          height: AppSize.h30,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                  width: 2, color: AppColors.primaryColor)),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(4),
                            onTap: () {
                              sendFeedBackBloc.pickImage(
                                  context: context,
                                  profileImage: ImageSource.gallery);
                            },
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 16.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!
                                          .attachImageText
                                          .toUpperCase(),
                                      style: context.textTheme.bodyLarge
                                          ?.copyWith(
                                              color: AppColors.primaryColor,
                                              fontSize: AppSize.sp14),
                                    ),
                                    Icon(
                                      Ionicons.attach,
                                      color: AppColors.primaryColor,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      } else {
                        return CustomListTile(
                          onTap: () {
                            sendFeedBackBloc.clearImage();
                          },
                          padding: EdgeInsets.zero,
                          title: AppLocalizations.of(context)!.attachmentText,
                          trailingWidget: Icon(Icons.close),
                        );
                      }
                    },
                  ),
                  SpaceV(AppSize.h8),
                  ValueListenableBuilder(
                    valueListenable: sendFeedBackBloc.checkBox1,
                    builder: (context, value, child) {
                      WidgetsBinding.instance
                          .addPostFrameCallback((timeStamp) async {
                        DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
                        String model = 'Unknown';
                        String operatingSystem = 'Unknown';
                        String osVersion = 'Unknown';
                        String platform = 'Unknown';
                        String manufacturer = 'Unknown';
                        bool isVirtual = false;
                        String name = 'Unknown';
                        String webViewVersion = 'Unknown';

                        if (Platform.isAndroid) {
                          AndroidDeviceInfo androidInfo =
                              await deviceInfo.androidInfo;
                          model = androidInfo.model;
                          operatingSystem = 'android';
                          osVersion = androidInfo.version.sdkInt.toString();
                          platform = 'android';
                          manufacturer = androidInfo.manufacturer;
                          isVirtual = androidInfo.isPhysicalDevice;
                          name = androidInfo.device;
                          // webViewVersion = androidInfo.webViewVersion;
                        } else if (Platform.isIOS) {
                          IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
                          model = iosInfo.model;
                          operatingSystem = 'ios';
                          osVersion = iosInfo.systemVersion;
                          platform = 'ios';
                        }

                        deviceInfo1 = {
                          "memUsed": 4911840,
                          "diskFree": 50360320,
                          "diskTotal": 874815488,
                          "model": "$model",
                          "operatingSystem": "$operatingSystem",
                          "osVersion": "$osVersion",
                          "platform": "$platform",
                          "manufacturer": "$manufacturer",
                          "isVirtual": "$isVirtual",
                          "name": "$name",
                          "webViewVersion": "$webViewVersion"
                        };

                        jsonDeviceInfo = jsonEncode(deviceInfo1);
                      });

                      return CheckboxListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                            AppLocalizations.of(context)!
                                .attachAnonymousDeviceInfo,
                            style: context.textTheme.bodyMedium?.copyWith(
                                color: AppColors.greyColor,
                                fontSize: AppSize.sp15)),
                        value: value,
                        activeColor: AppColors.primaryColor,
                        onChanged: (value) {
                          sendFeedBackBloc.changeValue(
                              context: context,
                              isAttachAnonymousDeviceInfo: value);
                        },
                      );
                    },
                  ),
                  ValueListenableBuilder(
                    valueListenable: sendFeedBackBloc.checkBox2,
                    builder: (context, value, child) {
                      userInfo = {
                        "PersonId": appDB.user?.personId,
                        "UserNamePerson": appDB.user?.UserNamePerson,
                        "UserCallNamePerson": appDB.user?.UserCallNamePerson,
                        "InitialsPerson": appDB.user?.InitialsPerson,
                        "PhotoId": appDB.user?.PhotoId,
                        "UserAccountMail": appDB.user?.UserAccountMail,
                        "LanguageId": appDB.user?.LanguageId,
                        "Language": appDB.user?.Language,
                        "Culture": appDB.user?.Culture,
                        "DefaultSkinId": appDB.user?.DefaultSkinId,
                        "DefaultSkinChanged": appDB.user?.DefaultSkinChanged,
                        "DefaultSkinPath": appDB.user?.DefaultSkinPath,
                        "LicenseId": appDB.user?.LicenseId,
                        "LicenseName": appDB.user?.LicenseName,
                        "LicenseSupportMessage":
                            appDB.user?.LicenseSupportMessage,
                        "LicenseSupportMessageMobile":
                            appDB.user?.LicenseSupportMessageMobile,
                        "MustChangePassword": appDB.user?.MustChangePassword,
                        "MustEnterTwoFactorAuthenticationToken":
                            appDB.user?.MustEnterTwoFactorAuthenticationToken,
                        "AppFunctionRights": appDB.user?.AppFunctionRightsList,
                        "IPAddressesSpecificUse":
                            appDB.user?.IPAddressesSpecificUseList,
                        "APIKey": appDB.user?.APIKey,
                      };

                      userJsonInfo = jsonEncode(userInfo);

                      return CheckboxListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                            AppLocalizations.of(context)!
                                .attachPersonalInfoLabel,
                            style: context.textTheme.bodyMedium?.copyWith(
                                color: AppColors.greyColor,
                                fontSize: AppSize.sp15)),
                        value: value,
                        activeColor: AppColors.primaryColor,
                        onChanged: (value) {
                          sendFeedBackBloc.changeValue2(
                              context: context, isAttachPersonalInfo: value);
                        },
                      );
                    },
                  ),
                  SpaceV(AppSize.h12),
                  ReusableContainerButton(
                    height: AppSize.h32,
                    borderRadius: BorderRadius.zero,
                    onPressed: () {
                      if (titleController.text.trim().isEmpty &&
                          titleController.text.trim().isEmpty) {
                        customSnackBar(
                            context: context,
                            message: AppLocalizations.of(context)!
                                .passwordCantEmptyText);
                      } else {
                        String taskTextWithCheckBox1 =
                            """${taskTextController.text}\n\n Apparaat informatie \n\n$jsonDeviceInfo""";
                        String taskTextWithCheckBox2 =
                            """${taskTextController.text}\n\n Gebruiker informatie \n\n$userJsonInfo""";
                        String taskTextWithCheckBox1andCheckBox2 =
                            """${taskTextController.text}\n\nApparaat informatie $jsonDeviceInfo\n\n Gebruiker informatie \n\n$userJsonInfo""";

                        sendFeedBackBloc.feedBackApiData(
                            context: context,
                            taskTest: (sendFeedBackBloc.checkBox2.value &&
                                    sendFeedBackBloc.checkBox1.value)
                                ? taskTextWithCheckBox1andCheckBox2
                                : sendFeedBackBloc.checkBox1.value
                                    ? taskTextWithCheckBox1
                                    : sendFeedBackBloc.checkBox2.value
                                        ? taskTextWithCheckBox2
                                        : taskTextController.text,
                            title: titleController.text,
                            attachment:
                                (sendFeedBackBloc.imageBaseString != null &&
                                        sendFeedBackBloc.imageBaseString != '')
                                    ? sendFeedBackBloc.imageBaseString
                                    : null);
                        AppNavigation.previousScreen(context);
                      }
                    },
                    buttonText: AppLocalizations.of(context)!.sendButtonLabel,
                    textStyle: context.textTheme.bodyLarge?.copyWith(
                        color: AppColors.white, fontSize: AppSize.sp14),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

// disk_space.dart

class DiskSpace {
  static const MethodChannel _channel = MethodChannel('disk_space');

  static Future<Map<String, dynamic>> getDiskSpaceInfo() async {
    try {
      final Map<String, dynamic> result =
          await _channel.invokeMethod('getDiskSpaceInfo');
      return result;
    } on PlatformException catch (e) {
      print("Error getting disk space information: $e");
      return {'error': e.message};
    }
  }
}
