import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';

import '../../../../service/api_service/api_function.dart';
import '../../../../service/api_service/server_constants.dart';

class feedBackApiProvider {
  Future<Response?> feedBackApiCall(
      {required BuildContext context, String? taskTest, String? title, String? attachment}) async {
    try {
      final query = {
        "TaskText": taskTest,
        "TaskTypeId": "94084a33-d2df-47cc-a523-a4a1cc827f05",
        "SubjectTypeId": "93eba5dc-7b65-4cc2-a274-c43ed45343e1",
        "Title": title,
        "Attachment": attachment,
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };
      log(query.toString());
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.feedBack,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
