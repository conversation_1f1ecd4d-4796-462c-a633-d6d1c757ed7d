import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import 'feedback_provider.dart';

class feedbackApiRepository {
  final feedbackProvider = feedBackApiProvider();

  Future<Response?> feedBackApi(
      {required BuildContext context,
      required String? taskTest,
      required String? title,
      required String? attachment}) async {
    final response = await feedbackProvider.feedBackApiCall(
        context: context, attachment: attachment, taskTest: taskTest, title: title);

    return response;
  }
}
