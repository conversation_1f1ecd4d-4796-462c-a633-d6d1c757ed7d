import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CommonButton extends StatelessWidget {
  final double? height;
  final double? width;
  final double? borderRadius;
  final String title;
  final Color? buttonColor;
  final TextStyle? titleStyle;
  final void Function()? onPressed;
  final bool isLoading;

  const CommonButton({
    Key? key,
    this.height,
    this.width,
    this.borderRadius,
    this.onPressed,
    required this.title,
    this.buttonColor,
    this.titleStyle,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 3,
      child: Ink(
        width: width ?? double.infinity,
        height: height ?? AppSize.h42,
        decoration: BoxDecoration(
          color: buttonColor ?? AppColors.primaryColor,
          borderRadius: BorderRadius.circular(borderRadius ?? 4),
        ),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(borderRadius ?? AppSize.r4),
          child: Container(
            height: height ?? AppSize.h30,
            width: width ?? double.infinity,
            decoration: BoxDecoration(
              color: buttonColor ?? AppColors.primaryColor,
              borderRadius: BorderRadius.circular(borderRadius ?? AppSize.r4),
            ),
            child: Center(
              child: isLoading
                  ? SizedBox(
                      height: AppSize.h10,
                      width: AppSize.h10,
                      child: CircularProgressIndicator(
                        strokeWidth: AppSize.h1,
                        color: AppColors.white,
                      ),
                    )
                  : Text(
                      title,
                      style: titleStyle ??
                          context.textTheme.titleSmall?.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w500,
                            letterSpacing: 1,
                            fontSize: AppSize.sp12,
                          ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
