import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';

import '../../../../../service/api_service/api_function.dart';
import '../../../../../service/api_service/server_constants.dart';

class ResetPasswordApiProvider {
  Future<Response?> resetPasswordApiCall(String userName, BuildContext context,String password) async {
    try {


      final query = {
      "Username": userName,
      "Password": password,
      "DeviceId": deviceId
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.resetPassword,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
