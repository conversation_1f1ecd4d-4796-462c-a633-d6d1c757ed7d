import 'package:flutter/cupertino.dart';

import '../model/forget_password_response_model.dart';
import 'forget_password_api_provider.dart';


class ResetPasswordApiRepository {
  final resetPasswordProvider = ResetPasswordApiProvider();

  Future<ResetPasswordResponseModel?> resetPasswordApi({required BuildContext context, required String userName,required String password}) async {
    final response = await resetPasswordProvider.resetPasswordApiCall(userName,context,password, );

    if (response != null) {
      return ResetPasswordResponseModel.fromJson(response.data,response.statusCode!);
    } else {
      return null;
    }
  }
}
