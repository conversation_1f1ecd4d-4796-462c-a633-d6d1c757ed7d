class ResetPasswordResponseModel {
  final List<String> result;
  final bool done;
  final String newGuid;
  final String? newTitle;
  final String? redirectUrl;
  final int statusCode;

  ResetPasswordResponseModel({
    required this.result,
    required this.done,
    required this.newGuid,
    this.newTitle,
    this.redirectUrl,
    required this.statusCode,
  });

  factory ResetPasswordResponseModel.fromJson(Map<String, dynamic> json, int statusCode) {
    return ResetPasswordResponseModel(
      result: List<String>.from(json['Result']),
      done: json['Done'],
      newGuid: json['NewGuid'],
      newTitle: json['NewTitle'],
      redirectUrl: json['RedirectURL'],
      statusCode: statusCode,
    );
  }
}

