import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../app/bloc/localization_cubit/language_cubit.dart';
import '../../../main.dart';
import '../../../utils/asset_path/assets_path.dart';
import '../../../utils/constant/constant.dart';
import 'on_boarding_screen/ui/onboarding_screens.dart';

class WelComeScreen extends StatefulWidget {
  const WelComeScreen({Key? key}) : super(key: key);

  @override
  State<WelComeScreen> createState() => _WelComeScreenState();
}

class _WelComeScreenState extends State<WelComeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      // await PermissionProvider.checkForPermission();
      await prefs.setString(
          AppConstants.lastPosition, AppConstants.welcomeScreen);
      print(
          "============>${prefs.setString(AppConstants.lastPosition, AppConstants.welcomeScreen)}");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      body: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.welcomeToStaff,
                      style: context.textTheme.titleLarge
                          ?.copyWith(color: AppColors.white),
                    ),
                    SpaceV(AppSize.h14),
                    Text(
                      AppLocalizations.of(context)!.knowDifferentName,
                      textAlign: TextAlign.center,
                      style: context.textTheme.titleMedium
                          ?.copyWith(color: AppColors.white),
                    ),
                    SpaceV(AppSize.h10),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w50),
                  child: Image.asset(
                    AssetsPath.cateringManagerImg,
                  ),
                ),
                SpaceV(AppSize.h8),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w50),
                  child: Image.asset(
                    AssetsPath.horecaManagerImg,
                  ),
                ),
                SpaceV(AppSize.h8),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w50),
                  child: Image.asset(
                    AssetsPath.personeelsManagerImg,
                    height: AppSize.h30,
                  ),
                ),
                SpaceV(AppSize.h18),
                Column(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.oneTimeActivation,
                      textAlign: TextAlign.center,
                      style: context.textTheme.titleMedium?.copyWith(
                          color: AppColors.white, fontSize: AppSize.sp14),
                    ),
                    SpaceV(AppSize.h22),
                    Text(
                      AppLocalizations.of(context)!.easyQuick,
                      textAlign: TextAlign.center,
                      style: context.textTheme.titleMedium?.copyWith(
                          color: AppColors.white, fontSize: AppSize.sp14),
                    ),
                    SpaceV(AppSize.h16),
                    ReusableContainerButton(
                        onPressed: () {
                          AppNavigation.nextScreen(context, OnBoardingScreen());
                        },
                        backgroundColor: AppColors.darkGreenColor,
                        borderRadius: BorderRadius.circular(AppSize.r4),
                        height: AppSize.h34,
                        textStyle: context.textTheme.titleSmall?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 1,
                          fontSize: AppSize.sp12,
                        ),
                        buttonText: AppLocalizations.of(context)!
                            .activate
                            .toUpperCase()),
                    SpaceV(AppSize.h22),
                    // Text(AppLocalizations.of(context)!.switchToEnglish,textAlign: TextAlign.center,style: context.textTheme.titleMedium?.copyWith(color: AppColors.white,fontSize: AppSize.sp14),),
                    BlocBuilder<LanguageCubit, Locale>(
                      builder: (ctx, state) {
                        String currentLanguageCode = state.languageCode;
                        String switchLanguageCode =
                            currentLanguageCode == 'en' ? 'nl' : 'en';

                        return GestureDetector(
                            onTap: () {
                              ctx
                                  .read<LanguageCubit>()
                                  .setLocale(Locale(switchLanguageCode));
                              appDB.language = switchLanguageCode;
                              print("Selected value: $switchLanguageCode");
                            },
                            child: Text(
                              switchLanguageCode == 'en'
                                  ? AppLocalizations.of(context)!
                                      .switchToEnglish
                                  : 'Switch to dutch',
                              textAlign: TextAlign.center,
                              style: context.textTheme.titleMedium?.copyWith(
                                  color: AppColors.white,
                                  fontSize: AppSize.sp14),
                            ));
                      },
                    )
                  ],
                )
              ].animate(interval: 500.ms).fade(duration: 400.ms),
            ),
          ),
        ),
      ),
    );
  }
}
