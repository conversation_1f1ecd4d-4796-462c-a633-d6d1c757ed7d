import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';

import '../../../../../main.dart';
import '../../../../../utils/colors/app_colors.dart';
import '../bloc/scanner_cubit.dart';

class QrScannerScreen extends StatefulWidget {
  QrScannerScreen({Key? key}) : super(key: key);

  @override
  State<QrScannerScreen> createState() => _QrScannerScreenState();
}

class _QrScannerScreenState extends State<QrScannerScreen> {
  late QRViewController controller;

  GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  StreamSubscription? scanSubscription;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller.pauseCamera();
      controller.resumeCamera();
    }
    controller.resumeCamera();
  }

  Future<void> _onQRViewCreated(QRViewController controller) async {
    final bloc = BlocProvider.of<ScannerCubit>(navigatorKey.currentContext!,
        listen: false);

    bloc.result.value = '';
    scanSubscription = controller.scannedDataStream.listen((scanData) {
      bloc.result.value = scanData.code ?? '';

      Navigator.pop(navigatorKey.currentContext!);
      bloc.validateActivationLinkApiForQRCode(navigatorKey.currentContext!);

      scanSubscription?.cancel();
    });
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    log('${DateTime.now().toIso8601String()}_onPermissionSet $p');
    if (!p) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('no Permission')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var scanArea = (MediaQuery.of(context).size.width < 400 ||
            MediaQuery.of(context).size.height < 400)
        ? 200.0
        : 300.0;

    final bloc = BlocProvider.of<ScannerCubit>(context, listen: false);
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          QRView(
            key: qrKey,
            onQRViewCreated: _onQRViewCreated,
            onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
            overlay: QrScannerOverlayShape(
                borderColor: Colors.black,
                borderLength: 30,
                // borderWidth: 10,
                cutOutSize: scanArea),
          ),
          Positioned(
            bottom: 5,
            left: 0,
            right: 0,
            child: ValueListenableBuilder(
              valueListenable: bloc.result,
              builder: (BuildContext context, String value, Widget? child) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w30),
                  child: Text(
                    value.isEmpty
                        ? AppLocalizations.of(context)!.scannerScreenText
                        : 'Found URL: ${value}',
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.white, fontSize: AppSize.sp14),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
