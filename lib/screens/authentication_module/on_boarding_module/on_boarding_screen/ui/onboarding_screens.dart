import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../../../common/custom_widgets/spacebox.dart';
import '../../../../../utils/asset_path/assets_path.dart';
import '../../../../../utils/constant/constant.dart';
import '../../widget/onboarding_container_widget.dart';

class OnBoardingScreen extends StatefulWidget {
  OnBoardingScreen({Key? key}) : super(key: key);

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  final TextEditingController textController = TextEditingController();
  @override
  void initState() {
    super.initState();

    controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 3000), // Duration for the animation
      reverseDuration:
          Duration(milliseconds: 3000), // Duration to reverse the animation
    )..repeat(reverse: true);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      // await PermissionProvider.checkForPermission();
      await prefs.setString(
          AppConstants.lastPosition, AppConstants.welcomeScreen);
      print(
          "============>${prefs.setString(AppConstants.lastPosition, AppConstants.welcomeScreen)}");
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    controller.dispose();
  }

  final PageController pageController = PageController();

  List<Widget> buildPages() {
    return [
      OnBoardingContainerWidget(
        title: AppLocalizations.of(navigatorKey.currentContext!)!
            .appActivationStep1,
        icon: Ionicons.log_in,
        controller: controller,
        topPadding: AppSize.h26,
        bottomPadding: AppSize.h36,
      ),
      OnBoardingContainerWidget(
        title: AppLocalizations.of(navigatorKey.currentContext!)!
            .appActivationStep2,
        icon: Ionicons.person,
        controller: controller,
        topPadding: AppSize.h26,
        bottomPadding: AppSize.h36,
      ),
      OnBoardingContainerWidget(
        textController: textController,
        isScanner: true,
        iconWidget: Image.asset(
          AssetsPath.scannerImg,
          height: AppSize.h110,
          width: AppSize.w110,
          color: AppColors.white,
        ),
        title: AppLocalizations.of(navigatorKey.currentContext!)!
            .appActivationStep3,
        icon: Ionicons.qr_code_sharp,
        controller: controller,
        topPadding: AppSize.h26,
        bottomPadding: AppSize.h36,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final pages = buildPages();
    return Scaffold(
        backgroundColor: AppColors.primaryColor,
        body: SafeArea(
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: AppSize.h24, horizontal: AppSize.w20),
              child: Column(
                children: [
                  Text(
                    AppLocalizations.of(context)!.appActivation,
                    style: context.textTheme.titleLarge?.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                  SpaceV(AppSize.h8),
                  Text(
                    AppLocalizations.of(context)!.appActivationDetailText,
                    style: context.textTheme.titleSmall?.copyWith(
                        // fontSize: AppSize.sp12,
                        color: AppColors.white,
                        fontWeight: FontWeight.w400),
                  ),
                  SpaceV(AppSize.h70),
                  Expanded(
                      child: PageView(
                    controller: pageController,
                    children: pages,
                  ))
                ],
              ),
            ),
          ),
        ));
  }
}
