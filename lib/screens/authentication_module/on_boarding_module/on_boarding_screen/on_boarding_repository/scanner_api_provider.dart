import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../../../service/api_service/api_function.dart';
import '../../../../../service/api_service/server_constants.dart';

class ScannerApiProvider {
  Future<int?> scannerApiCall(
    String skinId,
    BuildContext context,
  ) async {
    try {
      final query = {"SkinId": skinId};
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.scanner,
        header: {ServerConstant.Header_Authorization_KEY: ServerConstant.BEARER_TOKEN},
        query,
        context: context,
      );

      return response.statusCode;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
  }
}
