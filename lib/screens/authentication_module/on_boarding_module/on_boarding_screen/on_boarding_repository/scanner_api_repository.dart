import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/authentication_module/on_boarding_module/on_boarding_screen/on_boarding_repository/scanner_api_provider.dart';

class ScannerApiRepository {
  final scannerProvider = ScannerApiProvider();

  Future<int?> scannerApi({required BuildContext context, required String skinDateTime}) {
    return scannerProvider.scannerApiCall(skinDateTime, context);
  }
}
