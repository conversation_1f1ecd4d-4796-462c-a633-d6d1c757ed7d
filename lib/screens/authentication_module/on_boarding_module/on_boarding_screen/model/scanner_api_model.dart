// class ScannerResponseModel {
//   String? skinDateTime;
//   int? statusCode;
//   String? error;
//
//   ScannerResponseModel({this.skinDateTime, this.statusCode,this.error});
//
//   ScannerResponseModel.withError(String errorMessage) {
//     error = errorMessage;
//   }
//
//   ScannerResponseModel.fromJson(List<Map<String, dynamic>> json) {
//     skinDateTime = json[0];
//     statusCode = json[1];
//
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['SkinDateTime'] = this.skinDateTime;
//
//
//     return data;
//   }
// }