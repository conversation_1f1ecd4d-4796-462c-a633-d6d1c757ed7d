import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../../main.dart';
import '../../../../utils/app_navigation/appnavigation.dart';
import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';
import '../on_boarding_screen/bloc/scanner_cubit.dart';
import '../on_boarding_screen/ui/qr_code_scanner_screen.dart';

class OnBoardingContainerWidget extends StatelessWidget {
  final String title;
  final double? topPadding;
  final double? bottomPadding;
  final IconData? icon;
  final Widget? iconWidget;
  final IconData? arrowIcon;
  late AnimationController controller;
  final TextEditingController? textController;
  final bool isScanner;
  OnBoardingContainerWidget(
      {Key? key,
      required this.title,
      this.textController,
      this.topPadding,
      this.bottomPadding,
      required this.icon,
      required this.controller,
      this.arrowIcon,
      this.isScanner = false,
      this.iconWidget})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(title,
              style: navigatorKey.currentContext!.textTheme.titleMedium
                  ?.copyWith(color: AppColors.white)),
          Padding(
            padding: EdgeInsets.only(
                top: topPadding ?? AppSize.h26,
                bottom: bottomPadding ?? AppSize.h36),
            child: iconWidget ??
                Icon(
                  icon,
                  size: AppSize.sp120,
                  color: AppColors.white,
                ),
          ),
          if (isScanner)
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 90.w,
                  ),
                  child: ReusableContainerButton(
                      onPressed: () {
                        AppNavigation.nextScreen(context, QrScannerScreen());
                      },
                      backgroundColor: AppColors.darkGreenColor,
                      height: AppSize.h30,
                      textStyle: context.textTheme.titleSmall?.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 1,
                        fontSize: AppSize.sp12,
                      ),
                      buttonText:
                          AppLocalizations.of(navigatorKey.currentContext!)!
                              .scanCode
                              .toUpperCase()),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.sp14),
                  child: Text(
                      AppLocalizations.of(navigatorKey.currentContext!)!.or,
                      style: navigatorKey.currentContext!.textTheme.titleMedium
                          ?.copyWith(color: AppColors.white)),
                ),
                TextField(
                  textAlign: TextAlign.center,
                  cursorColor: AppColors.white,
                  style: navigatorKey.currentContext!.textTheme.titleSmall
                      ?.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w400,
                  ),
                  controller: textController,
                  onChanged: (value) async {
                    String? skinId;
                    // final skinIdPattern = RegExp(r'SkinId=([a-zA-Z0-9-]+)');
                    // final match = skinIdPattern.firstMatch(textController!.text.trim());
                    final bloc =
                        BlocProvider.of<ScannerCubit>(context, listen: false);
                    //skinId = match?.group(1);
                    print("Extracted SkinId: $skinId");
                    print("Extracted SkinId:  ${textController?.text}");
                    // if(skinId == skinIdPattern) {
                    await bloc.validateActivationLink(context, value.trim());
                    FocusManager.instance.primaryFocus?.unfocus();
                    textController!.clear();
                    // }else {
                    //   Loader.showLoaderDialog(context);
                    //   await Future.delayed(Duration(milliseconds: 100));
                    //   Loader.closeLoadingDialog(context);
                    //   customSnackBar(
                    //     context: navigatorKey.currentContext!,
                    //     message: 'Invalid link',
                    //     actionButtonText: 'close',
                    //   );
                    //   FocusManager.instance.primaryFocus?.unfocus();
                    //   textController!.clear();
                    // }
                    print("==========>");

                    // bloc.validateActivationLink(context, controller.toString());
                  },
                  decoration: InputDecoration(
                    isDense: true,
                    fillColor: AppColors.darkGreenColor,
                    filled: true,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: AppSize.h8), // Adjust vertical padding
                    enabledBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.darkGreenColor),
                      borderRadius: BorderRadius.zero,
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.darkGreenColor),
                      borderRadius: BorderRadius.zero,
                    ),
                    hintText: AppLocalizations.of(navigatorKey.currentContext!)!
                        .pasteLink,
                    hintStyle: navigatorKey.currentContext!.textTheme.titleSmall
                        ?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w400,
                    ),
                    alignLabelWithHint: true,
                  ),
                ),
              ],
            )
          else
            AnimatedBuilder(
              animation: controller,
              builder: (BuildContext context, Widget? child) {
                final scale =
                    1.0 + (controller.value * 0.1); // Scale from 1.0 to 1.2
                return Transform.scale(
                  scale: scale,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Ionicons.arrow_back_circle,
                        size: AppSize.sp30,
                        color: AppColors.white,
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: AppSize.w10),
                        child: AnimatedDefaultTextStyle(
                          style: navigatorKey
                              .currentContext!.textTheme.titleMedium!
                              .copyWith(
                                  color: AppColors.white,
                                  fontSize: AppSize.sp18),
                          curve: Curves.bounceOut,
                          duration: const Duration(milliseconds: 350),
                          child: Text(
                              AppLocalizations.of(navigatorKey.currentContext!)!
                                  .swipeLeftToContinue,
                              style: navigatorKey
                                  .currentContext!.textTheme.titleMedium
                                  ?.copyWith(color: AppColors.white)),
                        ),
                      ),
                    ],
                  ),
                );
              },
            )
        ],
      ),
    );
  }
}
