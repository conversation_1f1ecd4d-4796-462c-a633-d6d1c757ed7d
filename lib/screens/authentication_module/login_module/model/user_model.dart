class UserModel {
  String? personId;
  final int statusCode;
  String? UserNamePerson;
  String? UserCallNamePerson;
  String? InitialsPerson;
  String? PhotoId;
  String? UserAccountMail;
  String? LanguageId;
  String? Language;
  String? Culture;
  String? DefaultSkinId;
  String? DefaultSkinChanged;
  String? DefaultSkinPath;
  String? LicenseId;
  String? LicenseName;
  String? APIKey;
  String? LicenseSupportMessage;
  String? LicenseSupportMessageMobile;
  bool? MustChangePassword;
  bool? MustEnterTwoFactorAuthenticationToken;
  List? AppFunctionRights;
  List? IPAddressesSpecificUse;
  String? error;

  UserModel({
       this.personId,
       required this.statusCode,
        this.UserNamePerson,
        this.UserCallNamePerson,
        this.InitialsPerson,
        this.PhotoId,
        this.UserAccountMail,
        this.LanguageId,
        this.Language,
        this.Culture,
        this.DefaultSkinId,
        this.DefaultSkinChanged,
        this.DefaultSkinPath,
        this.LicenseId,
        this.LicenseName,
        this.LicenseSupportMessage,
        this.LicenseSupportMessageMobile,
        this.MustChangePassword,
        this.MustEnterTwoFactorAuthenticationToken,
        this.APIKey,
        this.AppFunctionRights,
        this.IPAddressesSpecificUse,}
      );

  UserModel.fromJson(Map<String, dynamic> json, this.statusCode) {
    personId = json['PersonId'];

    UserNamePerson = json['UserNamePerson'];
    UserCallNamePerson = json['UserCallNamePerson'];
    InitialsPerson = json['InitialsPerson'];
    PhotoId = json['PhotoId'];
    UserAccountMail = json['UserAccountMail'];
    LanguageId = json['LanguageId'];
    Language = json['Language'];
    Culture = json['Culture'];
    DefaultSkinId = json['DefaultSkinId'];
    DefaultSkinChanged = json['DefaultSkinChanged'];
    DefaultSkinPath = json['DefaultSkinPath'];
    LicenseId = json['LicenseId'];
    LicenseName = json['LicenseName'];
    LicenseSupportMessage = json['LicenseSupportMessage'];
    LicenseSupportMessageMobile = json['LicenseName'];
    MustChangePassword = json['MustChangePassword'];
    MustEnterTwoFactorAuthenticationToken = json['MustEnterTwoFactorAuthenticationToken'];
    APIKey = json['APIKey'];
    AppFunctionRights = json['AppFunctionRights'];
    IPAddressesSpecificUse = json['IPAddressesSpecificUse'];
    statusCode;

  }

  // UserModel.withError(String errorMessage) {
  //   error = errorMessage;
  // }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['PersonId'] = this.personId;
    data['UserNamePerson'] = this.UserNamePerson;
    data['UserCallNamePerson'] = this.UserCallNamePerson;
    data['InitialsPerson'] = this.InitialsPerson;
    data['PhotoId'] = this.PhotoId;
    data['UserAccountMail'] = this.UserAccountMail;
    data['LanguageId'] = this.LanguageId;
    data['Language'] = this.Language;
    data['Culture'] = this.Culture;
    data['DefaultSkinId'] = this.DefaultSkinId;
    data['DefaultSkinChanged'] = this.DefaultSkinChanged;
    data['DefaultSkinPath'] = this.DefaultSkinPath;
    data['LicenseId'] = this.LicenseId;
    data['LicenseName'] = this.LicenseName;
    data['LicenseSupportMessage'] = this.LicenseSupportMessage;
    data['LicenseSupportMessageMobile'] = this.LicenseSupportMessageMobile;
    data['MustEnterTwoFactorAuthenticationToken'] = this.MustEnterTwoFactorAuthenticationToken;
    data['APIKey'] = this.APIKey;
    data['AppFunctionRights'] = this.AppFunctionRights;
    data['IPAddressesSpecificUse'] = this.IPAddressesSpecificUse;

    return data;
  }
}