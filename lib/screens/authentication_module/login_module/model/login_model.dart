// import 'package:staff_medewerker/screens/authentication/login_module/model/user_model.dart';
//
// import '../bloc/login_cubit.dart';
//
// class LoginModel {
//   bool? success;
//   User? user;
//
//   LoginModel({this.success, this.user,});
//
//   LoginModel.fromJson(Map<String, dynamic> json) {
//     success = json['success'];
//     user = json['user'] != null ? new User.fromJson(json['user']) : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['success'] = this.success;
//     if (this.user != null) {
//       data['user'] = this.user!.toJson();
//     }
//
//     return data;
//   }
// }
