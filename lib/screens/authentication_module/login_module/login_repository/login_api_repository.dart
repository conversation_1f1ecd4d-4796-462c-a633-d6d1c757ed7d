import 'package:flutter/cupertino.dart';

import '../model/user_model.dart';
import 'login_api_provider.dart';

class LoginApiRepository {
  final loginProvider = LoginApiProvider();

  Future<UserModel?> loginApiData({
    required BuildContext context,
    required String username,
    required String password,
    required String deviceId,
  }) async {
   try {
     final response = await loginProvider.loginApiCall(username, password, deviceId, context);

    if (response != null) {
      return UserModel.fromJson(response.data, response.statusCode!);
    } else {
      print("else =====> +++");

      return null;
    }
   }catch (e) {
     print("else =====> +++ 23");
     return null;
   }
  }
}
