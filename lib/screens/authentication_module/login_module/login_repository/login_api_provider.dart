import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../../service/api_service/api_function.dart';
import '../../../../service/api_service/server_constants.dart';

class LoginApiProvider {
  Future<Response?> loginApiCall(
    String username,
    String password,
    String deviceId,
    BuildContext context,
  ) async {
    try {
      final query = {"Username": username, "Password": password, "DeviceId": ""};
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.login,
        header: {ServerConstant.Header_Authorization_KEY: ServerConstant.BEARER_TOKEN},
        query,
        context: context,
      );
      print("response statusCode =====>${response.statusCode}");
      return response;
      // return User.fromJson(response.data,response.statusCode!);
    } catch (error) {
      print("else =====> +++ 234 ${error}");

      // return User.withError(error.toString());
    }
    return null;
  }
}
