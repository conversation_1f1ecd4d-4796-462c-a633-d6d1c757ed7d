import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../../utils/appsize.dart';

class CommonTextField extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final String? Function(String?)? validator;
  final bool isSuffixIcon;
  const CommonTextField({Key? key, required this.controller, this.hintText, this.validator, this.isSuffixIcon = false}) : super(key: key);

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  bool _obscureText = true;

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextForm<PERSON>ield(
      controller: widget.controller,
      cursorColor: Colors.black45,
      cursorWidth: 1,
      validator: widget.validator,
      style: context.textTheme.titleMedium
          ?.copyWith(
          color: AppColors.mediumBlackColor,
          fontWeight: FontWeight.w400,
          fontSize: AppSize.sp14
      ),
      obscureText: widget.isSuffixIcon ? _obscureText
          : false,
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        hintText: widget.hintText,
        suffixIcon: widget.isSuffixIcon ? GestureDetector(onTap: () {
          _togglePasswordVisibility();
        },
          child: Icon(_obscureText ? Icons.visibility : Icons.visibility_off,color: AppColors.darkGreyColor,
          ),
        ) : null,
        hintStyle: context.textTheme.titleMedium
            ?.copyWith(
          color: AppColors.mediumBlackColor,
          fontWeight: FontWeight.w400,
            fontSize: AppSize.sp14
        ),
        contentPadding: EdgeInsets.symmetric(
            vertical: AppSize.h2,horizontal: AppSize.w10),
        enabledBorder:  OutlineInputBorder(
          borderSide:
          BorderSide(color: AppColors.white),
          borderRadius: BorderRadius.zero,
        ),
        focusedBorder:  OutlineInputBorder(
          borderSide:
          BorderSide(color: AppColors.white),
          borderRadius: BorderRadius.zero,
        ),
      ),
    );
  }
}
