import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/clocking_module/models/clocking_in.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clocking_status_api_provider.dart';

class ClockingInApiRepository {
  final clockingInProvider = ClockingStatusApiProvider();

  Future<ClockingInModel?> clockingInApi({required BuildContext context, required String defaultCostDepartmentId, required String defaultCostCenterId}) async {
    final response = await clockingInProvider.clockingInApiCall(context,defaultCostCenterId: defaultCostCenterId,defaultCostDepartmentId:defaultCostDepartmentId);

    if (response != null) {
      return ClockingInModel.fromJson(response.data,);
    } else {
      return null;
    }
  }
}
