import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clocking_status_api_provider.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/cost_centers_non_time_bound_person.dart';

class CostCentersNonTimeBoundPersonApiRepository {
  final clockingStatusApiProvider = ClockingStatusApiProvider();

  Future<List<CostCentersNonTimeBoundPersonModel>?> costCentersNonTimeBoundPersonApi({
    required BuildContext context,
    required String startIsoDate,
  }) async {
    final response = await clockingStatusApiProvider.costCentersNonTimeBoundPersonModelApiCall(context, startIsoDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<CostCentersNonTimeBoundPersonModel> costCentersNonTimeBoundPersonList = [];

      for (var item in dataList) {
        costCentersNonTimeBoundPersonList.add(CostCentersNonTimeBoundPersonModel.fromJson(item));
      }

      return costCentersNonTimeBoundPersonList;
    } else {
      return null;
    }
  }
}
