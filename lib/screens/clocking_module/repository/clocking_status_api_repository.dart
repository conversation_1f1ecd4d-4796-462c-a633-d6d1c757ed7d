import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/clocking_module/models/clocking_status.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clocking_status_api_provider.dart';

class ClockingStatusApiRepository {
  final clockingStatusProvider = ClockingStatusApiProvider();

  Future<ClockingStatusModel?> clockingStatusApi({required BuildContext context}) async {
    final response = await clockingStatusProvider.clockingStatusApiCall(context);

    if (response != null) {
      return ClockingStatusModel.fromJson(response.data);
    } else {
      return null;
    }
  }
}
