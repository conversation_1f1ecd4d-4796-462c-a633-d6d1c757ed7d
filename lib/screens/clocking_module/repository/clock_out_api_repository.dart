import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/clocking_module/models/clocking_out.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clocking_status_api_provider.dart';

class ClockingOutApiRepository {
  final clockingOutProvider = ClockingStatusApiProvider();

  Future<ClockingOutResponse?> clockingOutApi(
      {required BuildContext context,
      required String lastClockingId,
      String? remark,
      required List<String> nonTimeBoundCostCenterIds}) async {
    final response = await clockingOutProvider.clockingOutApiCall(
        context: context,
        lastClockingId: lastClockingId,
        nonTimeBoundCostCenterIds: nonTimeBoundCostCenterIds,
        remark: remark);

    if (response != null) {
      return ClockingOutResponse.fromJson(response.data);
    } else {
      return null;
    }
  }
}
