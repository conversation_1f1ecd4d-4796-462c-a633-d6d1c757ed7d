import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';

import '../../../../../service/api_service/api_function.dart';
import '../../../../../service/api_service/server_constants.dart';

class ClockingStatusApiProvider {
  Future<Response?> clockingStatusApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.my_clocking_status,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> clockingInApiCall(BuildContext context,
      {required String defaultCostCenterId, required String defaultCostDepartmentId}) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "DepartmentId": defaultCostDepartmentId,
        "CostCenterId": defaultCostCenterId
      };
      print("query clockingInApiCall: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.my_clocking_in,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> clockingOutApiCall(
      {required BuildContext context,
      required String lastClockingId,
      String? remark,
      required List<String> nonTimeBoundCostCenterIds}) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ClockingId": lastClockingId,
        "NonTimeBoundCostCenterIds": nonTimeBoundCostCenterIds,
        "Remark": remark
      };

      print("query: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.my_clocking_out,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> costCentersNonTimeBoundPersonModelApiCall(BuildContext context, String startIsoDate) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOStartDate": startIsoDate,
        "ISOEndDate": startIsoDate
      };

      print("query: $query");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.costCentersNonTimeBoundPerson,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
