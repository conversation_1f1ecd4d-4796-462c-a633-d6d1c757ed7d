import 'dart:convert';

ClockingStatusModel clockingOutResponseFromJson(String str) => ClockingStatusModel.fromJson(json.decode(str));

String clockingOutResponseToJson(ClockingStatusModel data) => json.encode(data.toJson());

class ClockingStatusModel {
  String? defaultCostDepartmentId;
  String? defaultCostCenterId;
  String? lastClockingId;
  String? lastDate;
  String? lastCostDepartmentId;
  String? lastTimeFrom;
  String? lastCostCenterId;

  ClockingStatusModel({
    this.defaultCostDepartmentId,
    this.defaultCostCenterId,
    this.lastClockingId,
    this.lastDate,
    this.lastCostDepartmentId,
    this.lastTimeFrom,
    this.lastCostCenterId,
  });

  factory ClockingStatusModel.fromJson(Map<String, dynamic> json) => ClockingStatusModel(
        defaultCostDepartmentId: json["DefaultCostDepartmentId"],
        defaultCostCenterId: json["DefaultCostCenterId"],
        lastClockingId: json["LastClockingId"],
        lastDate: json["LastDate"],
        lastCostDepartmentId: json["LastCostDepartmentId"],
        lastTimeFrom: json["LastTimeFrom"],
        lastCostCenterId: json["LastCostCenterId"],
      );

  Map<String, dynamic> toJson() => {
        "DefaultCostDepartmentId": defaultCostDepartmentId,
        "DefaultCostCenterId": defaultCostCenterId,
        "LastClockingId": lastClockingId,
        "LastDate": lastDate,
        "LastCostDepartmentId": lastCostDepartmentId,
        "LastTimeFrom": lastTimeFrom,
        "LastCostCenterId": lastCostCenterId,
      };
}
