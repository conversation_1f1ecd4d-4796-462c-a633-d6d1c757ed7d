import 'dart:convert';

ClockingInModel clockingInModelFromJson(String str) => ClockingInModel.fromJson(json.decode(str));

String clockingInModelToJson(ClockingInModel data) => json.encode(data.toJson());

class ClockingInModel {
  List<String> result;
  bool done;
  String newGuid;
  dynamic newTitle;
  dynamic redirectUrl;

  ClockingInModel({
    required this.result,
    required this.done,
    required this.newGuid,
    required this.newTitle,
    required this.redirectUrl,
  });

  factory ClockingInModel.fromJson(Map<String, dynamic> json) => ClockingInModel(
    result: List<String>.from(json["Result"].map((x) => x)),
    done: json["Done"],
    newGuid: json["NewGuid"],
    newTitle: json["NewTitle"],
    redirectUrl: json["RedirectURL"],
  );

  Map<String, dynamic> toJson() => {
    "Result": List<dynamic>.from(result.map((x) => x)),
    "Done": done,
    "NewGuid": newGuid,
    "NewTitle": newTitle,
    "RedirectURL": redirectUrl,
  };
}
