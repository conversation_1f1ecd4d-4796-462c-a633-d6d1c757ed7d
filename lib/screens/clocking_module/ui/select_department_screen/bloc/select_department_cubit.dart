import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_department_screen/department_repository/department_api_repository.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/departmentlist_response_model.dart';

part 'select_department_state.dart';

class SelectDepartmentCubit extends Cubit<SelectDepartmentState> {
  SelectDepartmentCubit() : super(SelectDepartmentInitial());

  List<DepartmentListResponseModel> departmentList = [];
  String selectedDepartmentName = '';
  String selectedDepartmentId = '';
  List<DepartmentListResponseModel> filterDepartmentList = [];
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  Future<void> setDepartmentData(
      {required String selectedValue, required BuildContext context, required selectedId}) async {
    print("====> ${departmentList}");

    filterDepartmentList = List.from(departmentList);
    selectedDepartmentName = selectedValue;
    selectedDepartmentId = selectedId;
    emit(SelectDepartmentInitial());
  }

  void filterDepartment(String query) {
    filterDepartmentList.clear();

    print('departmentList: $departmentList');
    filterDepartmentList =
        departmentList.where((element) => element.title.toLowerCase().contains(query.toLowerCase())).toList();

    emit(SelectDepartmentInitial());
  }

  Future<void> departmentApiCall({required BuildContext context}) async {
    print("api started =====>");
    final DepartmentListRepository departmentListRepository = DepartmentListRepository();
    final response = await departmentListRepository.departmentListApi(context: context);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      departmentList.clear();
      departmentList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(SelectDepartmentInitial());
  }
}
