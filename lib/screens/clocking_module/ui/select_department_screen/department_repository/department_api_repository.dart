import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_department_screen/department_repository/department_api_provider.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/departmentlist_response_model.dart';

class DepartmentListRepository {
  final departmentApiProvider = DepartmentApiProvider();

  Future<List<DepartmentListResponseModel>?> departmentListApi({required BuildContext context}) async {
    final response = await departmentApiProvider.departmentListApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<DepartmentListResponseModel> activityList = [];

      for (var item in dataList) {
        activityList.add(DepartmentListResponseModel.fromJson(item));
      }

      return activityList;
    } else {
      return null;
    }
  }
}
