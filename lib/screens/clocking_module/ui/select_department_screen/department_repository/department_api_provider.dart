import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class DepartmentApiProvider {
  Future<Response?> departmentListApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        }
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.departmentListData,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
