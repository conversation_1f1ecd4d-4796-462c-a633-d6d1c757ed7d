import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_exclusions_list.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_list_repository.dart';

part 'select_activity_state.dart';

class SelectActivityCubit extends Cubit<SelectActivityState> {
  SelectActivityCubit() : super(SelectActivityInitial());

  List<ActivityListResponseModel> activityList = [];
  List<ActivityExclusionsListResponseModel> activityExclusionsList = [];
  List<ActivityListResponseModel> timeBondFalseList = [];
  String selectedActivityName = '';
  String selectedActivityId = '';
  List<ActivityListResponseModel> filterActivityList = [];
  List<Map<String, dynamic>> hierarchyList = []; // Simplified hierarchy data
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  Future<void> setActivityData(
      {required String selectedValue,
      required String selectedId,
      required BuildContext context}) async {
    filterActivityList = List.from(activityList);
    selectedActivityName = selectedValue;
    selectedActivityId = selectedId;
    emit(SelectActivityInitial());
  }

  Future<void> costCentersActivityApiCall(
      {required BuildContext context,
      required String selectedDate,
      String? departmentId}) async {
    print("api started =====>");
    final ActivityListApiRepository activityListApiRepository =
        ActivityListApiRepository();
    final response = await activityListApiRepository.activityListApi(
      context: context,
      selectedDate: selectedDate,
    );
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      timeBondFalseList.clear();
      activityList.clear();

      print(
          "Clocking Activity API response received: ${response.length} activities");
      print(
          "Clocking Activity Exclusions available: ${activityExclusionsList.length} exclusions");
      print("Clocking Department ID for filtering: $departmentId");

      // Parse selected date for date range validation
      DateTime selectedDate2 = DateTime.parse(
          "${selectedDate.substring(0, 4)}-${selectedDate.substring(4, 6)}-${selectedDate.substring(6)}");

      // Apply filtering logic: Valid=true AND not excluded AND within date range
      List<ActivityListResponseModel> filteredActivities = [];

      for (var element in response) {
        // Step 1: Check if Valid = true
        if (element.valid != true) {
          print("CLOCKING FILTERED OUT (Invalid): ${element.title}");
          continue;
        }

        // Step 1.5: Hide non-productive activities (requirement: non-productive hours should not be visible)
        if (element.productive != true) {
          print("CLOCKING FILTERED OUT (Non-productive): ${element.title}");
          continue;
        }

        // Step 1.6: Hide non-timebound activities (requirement: if timeBound:false we exclude it)
        if (element.timeBound != true) {
          print("CLOCKING FILTERED OUT (Non-timebound): ${element.title}");
          continue;
        }

        // Step 2: Check if excluded for this department or its parent organizational units
        bool isExcluded = false;
        if (departmentId != null && activityExclusionsList.isNotEmpty) {
          // Check if this cost center is excluded for the department or any of its parent organizational units
          isExcluded = _isActivityExcludedForDepartment(
              element.costCenterId, departmentId);

          if (isExcluded) {
            print(
                "CLOCKING FILTERED OUT (Hierarchical Exclusion): ${element.title} for department $departmentId");
            continue;
          }
        }

        // Step 3: Check date range (StartDate <= selectedDate <= EndDate)
        bool isDateValid = (element.startDate.isBefore(selectedDate2) ||
                element.startDate.isAtSameMomentAs(selectedDate2)) &&
            (element.endDate.isAfter(selectedDate2) ||
                element.endDate.isAtSameMomentAs(selectedDate2));

        if (!isDateValid) {
          print(
              "CLOCKING FILTERED OUT (Date): ${element.title} - not active on ${selectedDate2.toIso8601String().split('T')[0]}");
          continue;
        }

        // If we get here, the activity passed all filters
        filteredActivities.add(element);
      }

      // Categorize filtered activities
      for (var element in filteredActivities) {
        if (element.timeBound == false) {
          // Non-timebound activities (both productive and non-productive)
          timeBondFalseList.add(element);
        } else if (element.timeBound == true) {
          // Timebound activities (both productive and non-productive)
          activityList.add(element);
        }
      }

      print("Clocking activity filtering completed:");
      print("- Total received: ${response.length}");
      print("- After filtering: ${filteredActivities.length}");
      print("- Non-timebound: ${timeBondFalseList.length}");
      print("- Timebound: ${activityList.length}");
    } else {
      print("No activities returned from Clocking API");
    }
    emit(SelectActivityInitial());
  }

  Future<void> activityExclusionsApiCall(
      {required BuildContext context}) async {
    print("api started =====>");
    final ActivityExclusionsApiRepository activityExclusionsApiRepository =
        ActivityExclusionsApiRepository();
    final response = await activityExclusionsApiRepository
        .activityExclusionsListApi(context: context);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      activityExclusionsList.clear();
      activityExclusionsList.addAll(response);
      print("activityExclusionsList: $activityExclusionsList");
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(SelectActivityInitial());
  }

  void filterActivity(String query) {
    filterActivityList = activityList
        .where((element) =>
            element.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
    emit(SelectActivityInitial());

    print("filterValue=====>$filterActivityList");
  }

  /// Check if an activity is excluded for a department or any of its parent organizational units
  /// This implements hierarchical exclusion checking as described in the requirements
  bool _isActivityExcludedForDepartment(
      String costCenterId, String departmentId) {
    // Get all organizational units that this department belongs to (including itself)
    Set<String> departmentHierarchy = _getDepartmentHierarchy(departmentId);

    // Check if the cost center is excluded for any organizational unit in the hierarchy
    return activityExclusionsList.any((exclusion) =>
        exclusion.costCenterId == costCenterId &&
        departmentHierarchy.contains(exclusion.organisationalUnitId));
  }

  /// Get all organizational unit IDs that a department belongs to (including itself)
  /// This traverses up the organizational hierarchy to find all parent units
  Set<String> _getDepartmentHierarchy(String departmentId) {
    Set<String> hierarchy = {
      departmentId
    }; // Always include the department itself

    if (hierarchyList.isEmpty) {
      print(
          "Warning: Clocking hierarchy list is empty, only checking direct department exclusions");
      return hierarchy;
    }

    // Find all parent organizational units by traversing up the hierarchy
    String? currentId = departmentId;
    Set<String> visited = {departmentId}; // Prevent infinite loops

    while (currentId != null) {
      // Find the parent organizational unit for currentId
      Map<String, dynamic>? parentUnit = hierarchyList
          .where((unit) => unit['OrganisationalUnitId'] == currentId)
          .firstOrNull;

      if (parentUnit != null &&
          parentUnit['ParentOrganisationalUnitId'] != null) {
        String parentId = parentUnit['ParentOrganisationalUnitId'];
        if (!visited.contains(parentId)) {
          hierarchy.add(parentId);
          visited.add(parentId);
          currentId = parentId;
          print(
              "Clocking found parent organizational unit: $parentId for $departmentId");
        } else {
          break; // Prevent infinite loops
        }
      } else {
        break; // No more parents found
      }
    }

    print("Clocking department $departmentId hierarchy: $hierarchy");
    return hierarchy;
  }

  /// Load organizational unit hierarchy data for clocking module from API
  Future<void> loadOrganizationalUnitHierarchy(BuildContext context) async {
    try {
      final ActivityListApiRepository activityListApiRepository =
          ActivityListApiRepository();
      final response = await activityListApiRepository
          .organisationalUnitHierarchyApi(context: context);

      if (response != null && response.data != null) {
        hierarchyList.clear();

        // The API returns data in format: "DepartmentId;OrganisationalUnitId": 1
        // We need to convert this to our internal format
        Map<String, dynamic> hierarchyData = response.data;

        hierarchyData.forEach((key, value) {
          if (key.contains(';')) {
            List<String> parts = key.split(';');
            if (parts.length == 2) {
              String departmentId = parts[0]; // First part is DepartmentId
              String organisationalUnitId =
                  parts[1]; // Second part is OrganisationalUnitId (parent)

              hierarchyList.add({
                'OrganisationalUnitId': departmentId, // The department itself
                'ParentOrganisationalUnitId':
                    organisationalUnitId, // Its parent organizational unit
              });
            }
          }
        });

        print(
            "Clocking loaded ${hierarchyList.length} hierarchy relationships from API");
        hierarchyList.forEach((item) {
          print(
              "Clocking Department ${item['OrganisationalUnitId']} -> Parent ${item['ParentOrganisationalUnitId']}");
        });
      } else {
        print("Clocking: No hierarchy data received from API");
      }
    } catch (e) {
      print("Error loading clocking organizational unit hierarchy: $e");
      // Fallback to empty hierarchy - will only check direct exclusions
      hierarchyList = [];
    }
  }
}
