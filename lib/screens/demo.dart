import 'dart:async';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';

class MyHomePage extends StatelessWidget {
  Widget build(BuildContext context) {
    //Image image = new Image.asset('assets/icons/images.jpeg');
    Image image = Image.network(
        'https://media.istockphoto.com/id/816519074/photo/swiftcurrent-lake-at-dawn.jpg?s=2048x2048&w=is&k=20&c=A1jOvEKDnRG3qMSgBsQqDCc9HnwlMwwQY578jd_UtVs=');
    Completer<ui.Image> completer = Completer<ui.Image>();
    image.image.resolve(ImageConfiguration()).addListener(ImageStreamListener((ImageInfo info, bool synchronousCall) {
      completer.complete(info.image);
    }));
    return Scaffold(
      appBar: AppBar(
        title: Text("Image Dimensions Example"),
      ),
      body: ListView(
        children: [
          FutureBuilder<ui.Image>(
            future: completer.future,
            builder: (BuildContext context, AsyncSnapshot<ui.Image> snapshot) {
              if (snapshot.hasData) {
                return Text(
                  '${snapshot.data?.width}x${snapshot.data?.height}px',
                  style: Theme.of(context).textTheme.bodyLarge,
                );
              } else {
                return Text('Loading...');
              }
            },
          ),
          image,
        ],
      ),
    );
  }
}
