part of 'declration_cubit.dart';

sealed class DeclrationState extends Equatable {
  const DeclrationState();

  @override
  List<Object> get props => [];
}

final class DeclrationInitial extends DeclrationState {}

final class DeclrationUdateDate extends DeclrationState {
  final DateTime confirmDate; // The DateTime field you want to update

  // Constructor to accept the DateTime
  const DeclrationUdateDate({required this.confirmDate});

  @override
  List<Object> get props => [confirmDate]; 
}


