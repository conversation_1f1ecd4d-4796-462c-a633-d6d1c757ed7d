import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_loader.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/declaration_module/model/declaration_model.dart';
import 'package:staff_medewerker/screens/declaration_module/repository/declration_repository.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/declaration_list_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';

import '../../../common/custom_widgets/common_snackbar.dart';
import '../../../common/custom_widgets/spacebox.dart';
import '../../../utils/appsize.dart';
import '../model/declaration_type.dart';

part 'declration_state.dart';

class DeclrationCubit extends Cubit<DeclrationState> {
  DeclrationCubit() : super(DeclrationInitial()) {
    getDeclrationListData(context: navigatorKey.currentContext!);
  }

  ValueNotifier<DateTime> selectedDay = ValueNotifier(DateTime.now());

  final TextEditingController nameController = TextEditingController();
  final TextEditingController totalAmountController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController dateController = TextEditingController();

  ImagePicker picker = ImagePicker();
  XFile? image;
  ValueNotifier<String> imagePath = ValueNotifier('');
  ValueNotifier<String> imageName = ValueNotifier('');

  ValueNotifier<String> bonGuid = ValueNotifier('');
  ValueNotifier<String> kmGuid = ValueNotifier('');

  ValueNotifier<String> km = ValueNotifier('');
  ValueNotifier<String> bon = ValueNotifier('');

  ValueNotifier<bool> inProgress = ValueNotifier(false);
  ValueNotifier<bool> isLoading = ValueNotifier(false);
  ValueNotifier<bool> isTypeLoading = ValueNotifier(false);
  ValueNotifier<bool> isSaveLoading = ValueNotifier(false);

  String imageBaseString = '';
  List<DeclrationModel> declrationList = [];
  List<DeclarationTypeModel> declarationTypeList = [];

  final declrationRepository = DeclrationRepository();

  void clearData() {
    nameController.clear();
    descriptionController.clear();
    totalAmountController.clear();
    dateController.clear();
    emit(DeclrationInitial());
  }

  void updateDate(DateTime date) {
    // confirmDate = date;
    log('date$date');
    //  emit(DeclrationInitial());
    emit(DeclrationUdateDate(confirmDate: date));
  }

  Future<String> pickImage(
      {required ImageSource profileImage,
      required BuildContext context}) async {
    image = await picker.pickImage(source: profileImage);
    imagePath.value = image!.path;
    imageName.value = image!.name;
    inProgress.value = true;
    print("imagepath: ${image!.path}");
    print("imagename: ${image!.name}");

    File imageFile = File(imagePath.value);
    Uint8List imageBytes = await imageFile.readAsBytes();

    // Convert the image bytes to a Base64 string
    imageBaseString = base64Encode(imageBytes);
    print("Base64 Image: $imageBaseString");
    clearData();
    return imageBaseString;
  }

  Future<void> getDeclrationListData({
    required BuildContext context,
  }) async {
    try {
      declrationList.clear();
      isLoading.value = true;
      final response = await declrationRepository.getDeclrationListApi(
        context: context,
      );
      log("declrationList api done =====>${declrationList}");
      isLoading.value = false;

      if (response != null) {
        // scheduleMonthList1 = scheduleMonthList;
        declrationList = response;
        // scheduleDayList = response.map((item) => new ScheduleMonthResponseModel.clone(item)).toList();
        //scheduleFilter1List = response.map((item) => new ScheduleMonthResponseModel.clone(item)).toList();
        log("declrationList api done =====>${declrationList}");
        log("declrationList api done =====>${declrationList.length}");
      }

      // Emit state change to notify UI
      emit(DeclrationInitial());
    } catch (e) {
      isLoading.value = false;
      // Emit state change even on error
      emit(DeclrationInitial());
    }
  }

  Future<void> getDeclarationTypeData({required BuildContext context}) async {
    try {
      isTypeLoading.value = true;
      final response = await declrationRepository.getDeclarationTypesApi(
        context: context,
      );
      isTypeLoading.value = false;
      if (response != null) {
        declarationTypeList = response;
        log("declrationType api done =====>${declarationTypeList}");
        log("declrationType api done =====>${declarationTypeList.length}");

        bon.value = declarationTypeList[0].Title.toString();
        km.value = declarationTypeList[1].Title.toString();

        bonGuid.value = declarationTypeList[0].guid.toString();
        kmGuid.value = declarationTypeList[1].guid.toString();

        print("bonGuuuuuid:${bonGuid.value}");
        print("kmGuidddddd:${kmGuid.value}");
      }
    } catch (e) {
      print("getDeclarationTypeDataError:$e");
      isTypeLoading.value = false;
    }
  }

  Future<void> getCreateDeclarationData(
      {required BuildContext context,
      required Map<String, dynamic> data}) async {
    try {
      Loader.showLoaderDialog(navigatorKey.currentContext!);
      final response = await declrationRepository.createDeclarationApi(
        context: context,
        data: data,
      );

      print("1111111111111111111111111111111111111");
      if (response!.data != null && response.data["Done"] == true) {
        Loader.closeLoadingDialog(navigatorKey.currentContext!);

        Navigator.pop(context);
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: response.data['Result'][0],
          actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
              .closeText
              .toUpperCase(),
        );

        AppNavigation.previousScreen(navigatorKey.currentContext!);
        if (data.containsKey('Bon')) {
          AppNavigation.previousScreen(navigatorKey.currentContext!);
        }

        // Refresh the declaration list after navigation back to list screen
        await getDeclrationListData(context: navigatorKey.currentContext!);
        // AppNavigation.pushAndRemoveAllScreen(context, DeclarationListScreen());
      } else {
        Loader.closeLoadingDialog(navigatorKey.currentContext!);
        Navigator.pop(context);
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: response.data['Result'][0],
          actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
              .closeText
              .toUpperCase(),
        );
        AppNavigation.previousScreen(navigatorKey.currentContext!);
        if (data.containsKey('Bon')) {
          AppNavigation.previousScreen(navigatorKey.currentContext!);
        }
        // AppNavigation.pushAndRemoveAllScreen(context, DeclarationListScreen());
        //  AppNavigation.previousScreen(context);
      }
    } catch (e) {
      Loader.closeLoadingDialog(navigatorKey.currentContext!);
      print("getCreateDeclarationDataError:$e");
    }
  }

  @override
  Future<void> close() {
    nameController.dispose();
    totalAmountController.dispose();
    descriptionController.dispose();
    imagePath.value = '';
    inProgress.value = false;
    isLoading.value = false;

    return super.close();
  }
}
