// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class CustomDelcarationTextfield extends StatelessWidget {
  const CustomDelcarationTextfield(
      {Key? key,
      this.labelText,
      this.controller,
      this.maxLines,
      this.onTap,
      this.onChange,
      this.keyboardType,
      this.focusNode,
      this.validateMsg,
      this.padding,
      this.readOnly,
      this.enable, this.inputFormatters})
      : super(key: key);
  final String? labelText;
  final TextEditingController? controller;
  final int? maxLines;
  final void Function()? onTap;
  final void Function(String)? onChange;
  final TextInputType? keyboardType;
  final FocusNode? focusNode;
  final String? validateMsg;
  final double? padding;
  final bool? readOnly;
  final bool? enable;
  final List<TextInputFormatter>? inputFormatters;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      focusNode: focusNode,
      enabled: enable,
      onTap: onTap,
      readOnly: readOnly ?? false,
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      style: context.textTheme.bodySmall?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp13),
      decoration: InputDecoration(
        // contentPadding: EdgeInsets.symmetric(vertical:padding??0.0),
        alignLabelWithHint: true,
        isDense: true,
        errorStyle: TextStyle(color: context.themeColors.buttonRedColor),
        labelStyle: context.textTheme.bodySmall?.copyWith(color: context.themeColors.greyColor, fontSize: AppSize.sp13),
        labelText: labelText ?? '',
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            AppSize.r4,
          ),
          borderSide: BorderSide(
            color: context.themeColors.greyColor,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            AppSize.r4,
          ),
          borderSide: BorderSide(
            color: context.themeColors.buttonRedColor,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSize.r4),
          borderSide: BorderSide(
            color: context.themeColors.buttonRedColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            AppSize.r4,
          ),
          borderSide: BorderSide(
            color: context.themeColors.greyColor,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            AppSize.r4,
          ),
          borderSide: BorderSide(
            color: context.themeColors.greyColor,
          ),
        ),
      ),
      validator: (val) {
        if (val == null || val.isEmpty) {
          return validateMsg;
        } else {
          return null;
        }
      },
      onChanged: onChange,
    );
  }
}
