class DeclrationModel {
  final String? declarationId;
  final int? number;
  final String? date;
  final String? typeId;
  final String? type;
  final String? fileName;
  final int? fileSize;
  final String? file;
  final double? kilometers;
  final double? costs;
  final String? description;
  final String? projectTaskId;
  final String? projectTask;
  final String? stateId;
  final String? state;
  final String? created;
  final String? handled;

  DeclrationModel({
     this.declarationId,
     this.number,
     this.date,
     this.typeId,
     this.type,
     this.fileName,
     this.fileSize,
     this.file,
     this.kilometers,
     this.costs,
     this.description,
     this.projectTaskId,
     this.projectTask,
     this.stateId,
     this.state,
     this.created,
     this.handled,
  });

  factory DeclrationModel.fromJson(Map<String, dynamic> json) {
    return DeclrationModel(
      declarationId: json['DeclarationId'] ?? '',
      number: json['Number'] ?? 0,
      date: json['Date'] ?? '',
      typeId: json['TypeId'] ?? '',
      type: json['Type'] ?? '',
      fileName: json['FileName'] ?? '',
      fileSize: json['FileSize'] ?? 0,
      file: json['File'] ?? '',
      kilometers: json['Kilometers']?.toDouble() ?? 0.0,
      costs: json['Costs']?.toDouble() ?? 0.0,
      description: json['Description'] ?? '',
      projectTaskId: json['ProjectTaskId'] ?? '',
      projectTask: json['ProjectTask'] ?? '',
      stateId: json['StateId'] ?? '',
      state: json['State'] ?? '',
      created: json['Created'] ?? '',
      handled: json['Handled'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'DeclarationId': declarationId,
      'Number': number,
      'Date': date,
      'TypeId': typeId,
      'Type': type,
      'FileName': fileName,
      'FileSize': fileSize,
      'File': file,
      'Kilometers': kilometers,
      'Costs': costs,
      'Description': description,
      'ProjectTaskId': projectTaskId,
      'ProjectTask': projectTask,
      'StateId': stateId,
      'State': state,
      'Created': created,
      'Handled': handled,
    };
  }
}
