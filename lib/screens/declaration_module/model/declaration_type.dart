
import 'dart:convert';

List<DeclarationTypeModel> DeclarationTypeModelListFromJson(String str) =>
    List<DeclarationTypeModel>.from(json.decode(str).map((x) => DeclarationTypeModel.fromJson(x)));

List<DeclarationTypeModel> DeclarationTypeModelList(List<dynamic> data) =>
    List<DeclarationTypeModel>.from(data.map((x) => DeclarationTypeModel.fromJson(x)));

String DeclarationTypeModelListToJson(List<DeclarationTypeModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

Map<String,dynamic> DeclarationTypeModelToJson(DeclarationTypeModel data) => data.toJson();

class DeclarationTypeModel{
  String? guid;
  String? Title;

  DeclarationTypeModel({
    this.guid,
    this.Title,
  });

  factory DeclarationTypeModel.fromJson(Map<String, dynamic> json) {
    return DeclarationTypeModel(
      guid: (json['guid'] ?? "").toString(),
      Title: (json['Title'] ?? "").toString(),
    );
  }

  Map<String, dynamic> toJson() => {
    'guid': guid,
    'Title': Title,
  };

}


