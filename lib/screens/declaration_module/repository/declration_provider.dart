import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class declarationAPIProvider {
  Future<Response?> getDeclrationListApiCall(
    BuildContext context,
  ) async {
    try {
      // print("ISOWEEKYEAR ========>${DateFormatFunctions.IosYearWeekData(selectedDate)}");

      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.getDeclrationList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      print("Query=======>$query");

      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> getDeclrationTypesApiCall(BuildContext context)async{
    try{

      Response response = await APIFunction.getAPICall(
        apiName: ServerConstant.base_url + ServerConstant.getDeclrationTypes,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_XAPI_KEY:APIKey,
        },
        context: context,
      );
      return response;

    } catch(error, stacktrace){
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
  Future<Response?> getCreateDeclrationApiCall(BuildContext context,{required Map<String,dynamic> data})async{
    try{
      final query = data;
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.createDeclaration,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;

    } catch(error, stacktrace){
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

}
