import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/screens/declaration_module/model/declaration_model.dart';
import 'package:staff_medewerker/screens/declaration_module/model/declaration_type.dart';
import 'package:staff_medewerker/screens/declaration_module/repository/declration_provider.dart';

class DeclrationRepository {
  final declarationProvider = declarationAPIProvider();

  Future<List<DeclrationModel>?> getDeclrationListApi({
    required BuildContext context,
  }) async {
    final response = await declarationProvider.getDeclrationListApiCall(context);
    log('----------->getDeclrationListApi ${response?.data}');

    if (response != null && response.data != null) {
      List<dynamic> dataList = response.data; // Assuming response.data is a List
      return dataList.map((data) => DeclrationModel.fromJson(data)).toList();
    } else {
      return null;
    }
  }

  Future <List<DeclarationTypeModel>?> getDeclarationTypesApi({required BuildContext context})async{

      final response = await declarationProvider.getDeclrationTypesApiCall(context);
      log('----------->createDeclrationTypesApi ${response?.data}');

      if (response != null && response.data != null) {
        List<dynamic> dataList = response.data;
        return dataList.map((data) => DeclarationTypeModel.fromJson(data)).toList();// Assuming response.data is a List
      } else {
        return null;
      }
    }

    Future<Response?> createDeclarationApi({ required BuildContext context,required Map<String,dynamic> data}
        )async{
      final response = await declarationProvider.getCreateDeclrationApiCall(context,data:data);
      log('----------->createDeclrationApi ${response?.data}');
      log('------>dattta:$data');

      if (response != null && response.data != null) {
        print('----------->createDeclrationApiResult:${response.data}');
      //  customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText);

        return response;
      } else {
        return null;
      }
    }



}
