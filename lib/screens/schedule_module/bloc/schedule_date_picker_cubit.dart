import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class ScheduleTimeCubit extends Cubit<bool> {
  ScheduleTimeCubit() : super(false);
  String selectedDate = "${formatDate(DateTime.now())}";
  ValueNotifier<String> selectedDateString = ValueNotifier('${formatDate(DateTime.now())}');

  String? selectedValue;

  void setDate({required DateTime selectedValue}) {
    selectedDateString.value = formatDate(selectedValue);
  }

  // void setDate(DateTime selectedValue) {
  //   setDay(date.day);
  //   setMonth(date.month);
  //   setYear(date.year);
  // }


  static formatDate(DateTime date) {
    final formatter = DateFormat('dd MMMM yyyy');
    return formatter.format(date);
  }

  updateValue(String value){
    selectedValue = value;

    emit(state);
  }

}


