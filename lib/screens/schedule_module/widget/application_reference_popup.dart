import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';

import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class applicationReferenceShowDialog extends StatelessWidget {
  final BuildContext context;
  final ValueNotifier<List<Item>> list;
  final Function(List<Item>) onChanged;
  final String? dialogTitle;
  final Function() onOKPressed;

  const applicationReferenceShowDialog({
    Key? key,
    required this.context,
    required this.list,
    required this.onChanged,
    this.dialogTitle,
    required this.onOKPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: context.themeColors.cardColor,
      actionsPadding: EdgeInsets.all(0),
      contentPadding: EdgeInsets.symmetric(vertical: AppSize.h6, horizontal: 0),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SpaceV(AppSize.h26),
          Divider(
            color: context.themeColors.dividerAvailbilityColor,
            height: 0,
            thickness: 1,
          ),
          Padding(
              padding: EdgeInsets.only(left: AppSize.w6),
              child: ValueListenableBuilder(
                valueListenable: list,
                builder: (context, value, child) {
                  return Column(
                    children: list.value.asMap().entries.map((entry) {
                      final index = entry.key;
                      final item = entry.value;
                      return Row(
                        children: [
                          Checkbox(
                            value: item.isSelected,
                            onChanged: (newValue) {
                              item.isSelected = newValue!;
                              onChanged(list.value);
                            },
                            activeColor: AppColors.primaryColor,
                          ),
                          Flexible(
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  item.title,
                                  style:
                                      context.textTheme.headlineLarge?.copyWith(
                                    fontSize: AppSize.sp14,
                                    fontWeight: FontWeight.normal,
                                    color: context.themeColors.textColor,
                                  ),
                                ),
                                Container(
                                  width: AppSize.sp14,
                                  height: AppSize.sp14,
                                  margin: EdgeInsets.only(
                                      right: AppSize.w20, left: AppSize.w10),
                                  color: item.color,
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  );
                },
              )),
          Divider(
            color: context.themeColors.dividerAvailbilityColor,
            height: 0,
            thickness: 1,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(
            AppLocalizations.of(context)!.cancelText.toUpperCase(),
            style: context.textTheme.headlineLarge?.copyWith(
              fontSize: AppSize.sp14,
              fontWeight: FontWeight.w500,
              color: context.themeColors.primaryColor,
            ),
          ),
        ),
        TextButton(
          onPressed: onOKPressed,
          child: Text(
            "OK",
            style: context.textTheme.headlineLarge?.copyWith(
              fontSize: AppSize.sp14,
              fontWeight: FontWeight.w500,
              color: context.themeColors.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}

class Item {
  final String title;
  bool isSelected;
  final Color color;

  Item(this.title, this.isSelected, this.color);
}
