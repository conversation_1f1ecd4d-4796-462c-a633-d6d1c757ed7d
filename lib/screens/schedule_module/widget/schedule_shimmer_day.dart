import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../common/custom_widgets/shimmer_effect.dart';
import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class ScheduleDayShimmerWidget extends StatelessWidget {
  const ScheduleDayShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Column(
        children: [
          Expanded(
              child: ListView.builder(
            itemBuilder: (context, index) {
              return Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: AppSize.h8, bottom: AppSize.h10),
                    child: Container(
                      margin: EdgeInsets.only(top: AppSize.h6, bottom: AppSize.h6),
                      height: AppSize.h8,
                      color: AppColors.white,
                    ),
                  ),
                  Divider(
                    color: context.themeColors.dividerAvailbilityColor,
                    height: 0,
                    thickness: 1.2,
                  ),
                ],
              );
            },
            itemCount: 10,
          ))
        ],
      ),
    );
  }
}
