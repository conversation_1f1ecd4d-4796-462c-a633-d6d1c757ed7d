import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class scheduleTypeShowDialog extends StatelessWidget {
  final BuildContext context;
  final List<String?> list;
  final ValueNotifier<int> selectedValue;
  final void Function(int?)? onChanged;
  final String? dialogTitle;
  final Function(int) onOKPressed;

  const scheduleTypeShowDialog({
    Key? key,
    required this.context,
    required this.list,
    required this.selectedValue,
    this.onChanged,
    this.dialogTitle,
    required this.onOKPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
        backgroundColor: context.themeColors.cardColor,
        actionsPadding: EdgeInsets.all(0),
        titlePadding: EdgeInsets.only(
          top: AppSize.sp14,
          left: AppSize.sp20,
          bottom: AppSize.sp10,
        ),
        title: Text(
          dialogTitle ?? '',
          style: context.textTheme.headlineLarge?.copyWith(
            fontSize: AppSize.sp17,
            fontWeight: FontWeight.w500,
            color: context.themeColors.textColor,
          ),
        ),
        contentPadding:
            EdgeInsets.symmetric(vertical: AppSize.h6, horizontal: 0),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              AppLocalizations.of(context)!.cancelText.toUpperCase(),
              style: context.textTheme.headlineLarge?.copyWith(
                fontSize: AppSize.sp14,
                fontWeight: FontWeight.w500,
                color: context.themeColors.primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              onOKPressed(selectedValue.value);

              Navigator.pop(context);
            },
            child: Text(
              "OK",
              style: context.textTheme.headlineLarge?.copyWith(
                fontSize: AppSize.sp14,
                fontWeight: FontWeight.w500,
                color: context.themeColors.primaryColor,
              ),
            ),
          ),
        ],
        content: StatefulBuilder(builder: (ctx, setState2) {
          return ValueListenableBuilder(
            valueListenable: selectedValue,
            builder: (context, value, child) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Divider(
                    color: context.themeColors.dividerAvailbilityColor,
                    height: 0,
                    thickness: 1,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        left: AppSize.w8,
                        right: AppSize.w8,
                        bottom: AppSize.h8),
                    child: StatefulBuilder(builder: (ctx, setState2) {
                      return Column(
                        children: list.map((language) {
                          final index = list.indexOf(language) + 1;
                          print("index =======>$language");
                          return Row(
                            children: [
                              Radio(
                                value: index,
                                groupValue: value,
                                onChanged: onChanged,
                                activeColor: AppColors.primaryColor,
                              ),
                              Expanded(
                                child: Text(
                                  language ?? '',
                                  style:
                                      context.textTheme.headlineLarge?.copyWith(
                                    fontSize: AppSize.sp14,
                                    fontWeight: FontWeight.normal,
                                    color: context.themeColors.textColor,
                                  ),
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                      );
                    }),
                  ),
                  Divider(
                    color: context.themeColors.dividerAvailbilityColor,
                    height: 0,
                    thickness: 1,
                  ),
                ],
              );
            },
          );
        }));
  }
}
