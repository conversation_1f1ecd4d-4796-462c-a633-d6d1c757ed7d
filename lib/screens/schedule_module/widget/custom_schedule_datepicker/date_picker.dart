import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/screens/schedule_module/widget/custom_schedule_datepicker/month_widget.dart';
import 'package:staff_medewerker/screens/schedule_module/widget/custom_schedule_datepicker/year_widget.dart';

import '../../../../common/custom_widgets/spacebox.dart';
import '../../../../utils/app_navigation/appnavigation.dart';
import '../../../../utils/appsize.dart';
import '../../../absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';

class ScheduleDatePickerWidget extends StatelessWidget {
  final Function(DateTime) onOkPressed;

  ScheduleDatePickerWidget({required this.onOkPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.of(context).size.height * .334,
        child: Column(children: [
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.end,
          //   children: [
          //     TextButton(
          //       onPressed: () {
          //         AppNavigation.previousScreen(context);
          //       },
          //       child: Text(AppLocalizations.of(context)!.cancelText.toUpperCase()),
          //     ),
          //     BlocBuilder<DateCubit, DateTime>(
          //       builder: (context, state) {
          //         return TextButton(
          //           onPressed: () {
          //             onOkPressed(state);
          //             Navigator.pop(context);
          //           },
          //           child: Text(AppLocalizations.of(context)!.oK),
          //         );
          //       },
          //     ),
          //   ],
          // ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SpaceH(AppSize.w20),
                // Expanded(
                //   child: ScheduleDayPicker(),
                // ),

                Expanded(
                  child: ScheduleMonthPicker(),
                ),
                Expanded(
                  child: ScheduleYearPickerWidget(),
                ),
                //SpaceH(AppSize.w50),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  AppNavigation.previousScreen(context);
                },
                child: Text(
                    AppLocalizations.of(context)!.cancelText.toUpperCase()),
              ),
              BlocBuilder<DateCubit, DateTime>(
                builder: (context, state) {
                  return TextButton(
                    onPressed: () {
                      onOkPressed(state);
                      Navigator.pop(context);
                    },
                    child: Text(AppLocalizations.of(context)!.oK.toUpperCase()),
                  );
                },
              ),
            ],
          ),
        ]));
  }
}
