import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_date_picker_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class ScheduleYearPickerWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    DateFormat format = DateFormat("dd MMMM yyyy");

    int currentYear = format.parse(BlocProvider.of<ScheduleTimeCubit>(context).selectedDateString.value).year;
    context.read<DateCubit>().setYear(currentYear);
    // int currentYear = DateTime.now().year+1;
    int startYear = DateTime.now().year - 100; // Define the start year

    List<int> yearsList = List<int>.generate(currentYear - startYear + 1, (index) {
      return DateTime.now().year + 1 - index;
    });

    return BlocBuilder<DateCubit, DateTime>(
      builder: (context, date) {
        return ListWheelScrollView(
          perspective: 0.000001,
          useMagnifier: false,
          itemExtent: 40,
          physics: FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            int selectedYear = yearsList[index];
            context.read<DateCubit>().setYear(selectedYear);
            print(selectedYear); // Print the selected year
          },
          children: yearsList.map((value) {
            final isSelected = (value == date.year);
            return Text(
              value.toString(),
              style: context.textTheme.bodyMedium?.copyWith(
                fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
                color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
              ),
            );
          }).toList(),
          controller: FixedExtentScrollController(
              initialItem: yearsList.indexOf(date.year)), // Set the initial item to the current year
        );
      },
    );
  }
}
