class ScheduleDateDetailResponseModel {
  final String? timeFrom;
  final String? timeUntil;
  final String? breakTime;
  final String? employee;
  final String? costCenters;
  final String? department;
  final String? service;
  final String? remark;
  final String? calendarEntry;
  final int statusCode;

  ScheduleDateDetailResponseModel(
      {this.remark,this.calendarEntry,
      this.employee,
      required this.statusCode,
      this.breakTime,
      this.service,
      this.costCenters,
      this.department,
      this.timeFrom,
      this.timeUntil});

  factory ScheduleDateDetailResponseModel.fromJson(
          Map<String, dynamic> json, int statusCode) =>
      ScheduleDateDetailResponseModel(
        timeFrom: json["TimeFrom"],
        timeUntil: json['TimeUntil'],
        employee: json["Employee"],
        costCenters: json["CostCenters"],
        remark: json["Remark"],
        service: json["Service"],
        department: json["Department"],
        calendarEntry: json["CalendarEntry"],
        statusCode: statusCode,
      );

  Map<String, dynamic> toJson() => {
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "Employee": employee,
        "Remark": remark,
        "BreakTime": breakTime,
        "CostCenters": costCenters,
    "Service":service
      };
}


class ScheduleDateDepartmentListResponseModel {
  final String? guid;
  final String? title;

  final int statusCode;

  ScheduleDateDepartmentListResponseModel(
      {
        required this.statusCode,

        this.guid,
        this.title});

  factory ScheduleDateDepartmentListResponseModel.fromJson(
      Map<String, dynamic> json, int statusCode) =>
      ScheduleDateDepartmentListResponseModel(statusCode: statusCode,guid:json["guid"] ,title:json["Title"]
      );

  Map<String, dynamic> toJson() => {
    "guid": guid,
    "TimeUntil": title,

  };
}
