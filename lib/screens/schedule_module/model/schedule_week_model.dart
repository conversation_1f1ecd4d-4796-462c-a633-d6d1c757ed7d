import 'package:equatable/equatable.dart';

class ScheduleWeekResponseModel extends Equatable {
  final String? personId;
  final String? department;
  final String? date;
  final String? iSODate;
  final String? timeFrom;
  final String? timeUntil;
  final String? costCenters;
  final String? dayRemark;
  final String? breakTime;
  final String? remark;
  final String? service;
  final int statusCode;
  final SwapModel? swap;
  final OpenWeekServiceModel? openService;

  ScheduleWeekResponseModel(
      {this.swap,
      this.openService,
      required this.statusCode,
      this.breakTime,
      this.remark,
      this.date,
      this.service,
      this.department,
      this.costCenters,
      this.dayRemark,
      this.iSODate,
      this.personId,
      this.timeFrom,
      this.timeUntil});
  ScheduleWeekResponseModel.clone(ScheduleWeekResponseModel source)
      : this.personId = source.personId,
        this.remark = source.remark,
        this.swap = source.swap,
        this.openService = source.openService,
        this.breakTime = source.breakTime,
        this.date = source.date,
        this.service = source.service,
        this.department = source.department,
        this.costCenters = source.costCenters,
        this.dayRemark = source.dayRemark,
        this.iSODate = source.iSODate,
        this.timeFrom = source.timeFrom,
        this.timeUntil = source.timeUntil,
        this.statusCode = source.statusCode;
  factory ScheduleWeekResponseModel.fromJson(Map<String, dynamic> json, int statusCode) => ScheduleWeekResponseModel(
        personId: json["PersonId"],
        department: json["CostDepartment"],
        date: json["Date"],
        iSODate: json["ISODate"],
        timeFrom: json["TimeFrom"],
        timeUntil: json["TimeUntil"],
        costCenters: json["CostCenters"],
        dayRemark: json["DayRemark"],
        swap: json.containsKey("Swap") ? SwapModel.fromJson(json["Swap"]) : null,
        openService: json.containsKey("OpenService") ? OpenWeekServiceModel.fromJson(json["OpenService"]) : null,
        service: json["Service"],
        statusCode: statusCode,
        remark: json['Remark'],
        breakTime: json["BreakTime"],
      );

  Map<String, dynamic> toJson() => {
        "PersonId": personId,
        "CostDepartment": department,
        "Date": date,
        "ISODate": iSODate,
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "CostCenters": costCenters,
        "DayRemark": dayRemark,
        "Service": service,
        "Remark": remark,
        "Swap": swap,
        "BreakTime": breakTime,
        "OpenService": openService,
      };

  @override
  // TODO: implement props
  List<Object?> get props => [
        this.statusCode,
        this.personId,
        this.department,
        this.remark,
        this.date,
        this.iSODate,
        this.timeUntil,
        this.timeFrom,
        this.service,
        this.costCenters,
        this.swap,
        this.openService,
        this.breakTime,
        this.dayRemark
      ];
}

class SwapModel {
  final String? dateEntryId;
  final String? personId;
  final String? initiatorId;
  final String? initiator;
  final String? remark;
  final String? stateId;
  final String? state;
  final bool? avUnavailable;
  final String? avFromTime;
  final String? avToTime;
  final bool? managerResponsible;
  final bool? managerSkipInvite;

  SwapModel({
    this.dateEntryId,
    this.personId,
    this.initiatorId,
    this.initiator,
    this.remark,
    this.stateId,
    this.state,
    this.avUnavailable,
    this.avFromTime,
    this.avToTime,
    this.managerResponsible,
    this.managerSkipInvite,
  });

  factory SwapModel.fromJson(Map<String, dynamic> json) {
    return SwapModel(
      dateEntryId: json["DateEntryId"],
      initiatorId: json["InitiatorId"],
      initiator: json["Initiator"],
      remark: json["Remark"],
      stateId: json["StateId"],
      state: json["State"],
      avUnavailable: json["AvUnavailable"] ?? false,
      avFromTime: json["AvFromTime"],
      personId: json["PersonId"],
      avToTime: json["AvToTime"],
      managerResponsible: json["ManagerResponsible"] ?? false,
      managerSkipInvite: json["ManagerSkipInvite"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "PersonId": personId,
        "DateEntryId": dateEntryId,
        "InitiatorId": initiatorId,
        "Initiator": initiator,
        "Remark": remark,
        "StateId": stateId,
        "State": state,
        "AvUnavailable": avUnavailable,
        "AvFromTime": avFromTime,
        "AvToTime": avToTime,
        "ManagerResponsible": managerResponsible,
        "ManagerSkipInvite": managerSkipInvite,
      };
}

class OpenWeekServiceModel {
  final String? departmentId;
  final String? baseServiceId;
  final bool? subscribed;

  OpenWeekServiceModel({
    this.departmentId,
    this.baseServiceId,
    this.subscribed,
  });

  factory OpenWeekServiceModel.fromJson(Map<String, dynamic> json) {
    return OpenWeekServiceModel(
      departmentId: json["DepartmentId"],
      baseServiceId: json["BaseServiceId"],
      subscribed: json["Subscribed"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "DepartmentId": departmentId,
        "BaseServiceId": baseServiceId,
        "Subscribed": subscribed,
      };
}
