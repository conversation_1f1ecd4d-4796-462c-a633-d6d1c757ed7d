import 'package:equatable/equatable.dart';
import 'schedule_week_model.dart';

class ScheduleMonthResponseModel extends Equatable {
  final String? personId;
  final String? department;
  final String? date;
  String? iSODate;
  final String? timeFrom;
  final String? timeUntil;
  final String? breakTime;
  final String? costCenters;
  final String? dayRemark;
  final String? service;
  final String? remark;
  final int statusCode;
  final SwapMonthModel? swap;
  final CalendarEntry? calendarEntry;
  final OpenServiceModel? openService;

  ScheduleMonthResponseModel(
      {this.remark,
      this.swap,
      this.openService,
      required this.statusCode,
      this.calendarEntry,
      this.breakTime,
      this.date,
      this.service,
      this.department,
      this.costCenters,
      this.dayRemark,
      this.iSODate,
      this.personId,
      this.timeFrom,
      this.timeUntil});

  ScheduleMonthResponseModel.clone(
    ScheduleMonthResponseModel source,
  )   : this.personId = source.personId,
        this.remark = source.remark,
        this.calendarEntry = source.calendarEntry,
        this.swap = source.swap,
        this.openService = source.openService,
        this.breakTime = source.breakTime,
        this.date = source.date,
        this.service = source.service,
        this.department = source.department,
        this.costCenters = source.costCenters,
        this.dayRemark = source.dayRemark,
        this.iSODate = source.iSODate,
        this.timeFrom = source.timeFrom,
        this.timeUntil = source.timeUntil,
        this.statusCode = source.statusCode;

  // Constructor to convert from ScheduleWeekResponseModel
  ScheduleMonthResponseModel.fromWeekModel(
    ScheduleWeekResponseModel source,
  )   : this.personId = source.personId,
        this.remark = source.remark,
        this.calendarEntry = null, // Week model doesn't have calendar entry
        this.swap = source.swap != null
            ? SwapMonthModel.fromWeekSwap(source.swap!)
            : null,
        this.openService = source.openService != null
            ? OpenServiceModel.fromWeekService(source.openService!)
            : null,
        this.breakTime = source.breakTime,
        this.date = source.date,
        this.service = source.service,
        this.department = source.department,
        this.costCenters = source.costCenters,
        this.dayRemark = source.dayRemark,
        this.iSODate = source.iSODate,
        this.timeFrom = source.timeFrom,
        this.timeUntil = source.timeUntil,
        this.statusCode = source.statusCode;

  factory ScheduleMonthResponseModel.fromJson(
      Map<String, dynamic> json, int statusCode) {
    return ScheduleMonthResponseModel(
      calendarEntry: json.containsKey("CalendarEntry")
          ? CalendarEntry.fromJson(json["CalendarEntry"])
          : null,
      personId: json["PersonId"],
      remark: json['Remark'],
      department: json["CostDepartment"],
      breakTime: json["BreakTime"],
      date: json["Date"],
      iSODate: json["ISODate"],
      timeFrom: json["TimeFrom"],
      timeUntil: json["TimeUntil"],
      costCenters: json["CostCenters"],
      dayRemark: json["DayRemark"],
      service: json["Service"],
      swap: json.containsKey("Swap")
          ? SwapMonthModel.fromJson(json["Swap"])
          : null,
      openService: json.containsKey("OpenService")
          ? OpenServiceModel.fromJson(json["OpenService"])
          : null,
      statusCode: statusCode,
    );
  }

  Map<String, dynamic> toJson() => {
        "PersonId": personId,
        "CostDepartment": department,
        "CalendarEntry": calendarEntry,
        "Date": date,
        "Remark": remark,
        "BreakTime": breakTime,
        "ISODate": iSODate,
        "TimeFrom": timeFrom,
        "TimeUntil": timeUntil,
        "CostCenters": costCenters,
        "DayRemark": dayRemark,
        "Service": service,
        "Swap": swap,
        "OpenService": openService,
      };

  @override
  // TODO: implement props
  List<Object?> get props => [
        this.statusCode,
        this.personId,
        this.department,
        this.remark,
        this.date,
        this.iSODate,
        this.timeUntil,
        this.timeFrom,
        this.service,
        this.costCenters,
        this.swap,
        this.openService,
        this.breakTime,
        this.dayRemark
      ];
}

class SwapMonthModel {
  final String? dateEntryId;
  final String? personId;
  final String? initiatorId;
  final String? initiator;
  final String? remark;
  final String? stateId;
  final String? state;
  final bool? avUnavailable;
  final String? avFromTime;
  final String? avToTime;
  final bool? managerResponsible;
  final bool? managerSkipInvite;

  SwapMonthModel({
    this.dateEntryId,
    this.personId,
    this.initiatorId,
    this.initiator,
    this.remark,
    this.stateId,
    this.state,
    this.avUnavailable,
    this.avFromTime,
    this.avToTime,
    this.managerResponsible,
    this.managerSkipInvite,
  });

  factory SwapMonthModel.fromJson(Map<String, dynamic> json) {
    return SwapMonthModel(
      dateEntryId: json["DateEntryId"],
      initiatorId: json["InitiatorId"],
      initiator: json["Initiator"],
      remark: json["Remark"],
      stateId: json["StateId"],
      state: json["State"],
      avUnavailable: json["AvUnavailable"] ?? false,
      avFromTime: json["AvFromTime"],
      personId: json["PersonId"],
      avToTime: json["AvToTime"],
      managerResponsible: json["ManagerResponsible"] ?? false,
      managerSkipInvite: json["ManagerSkipInvite"] ?? false,
    );
  }

  // Convert from week SwapModel to month SwapMonthModel
  factory SwapMonthModel.fromWeekSwap(SwapModel weekSwap) {
    return SwapMonthModel(
      dateEntryId: weekSwap.dateEntryId,
      initiatorId: weekSwap.initiatorId,
      initiator: weekSwap.initiator,
      remark: weekSwap.remark,
      stateId: weekSwap.stateId,
      state: weekSwap.state,
      avUnavailable: weekSwap.avUnavailable,
      avFromTime: weekSwap.avFromTime,
      personId: weekSwap.personId,
      avToTime: weekSwap.avToTime,
      managerResponsible: weekSwap.managerResponsible,
      managerSkipInvite: weekSwap.managerSkipInvite,
    );
  }

  Map<String, dynamic> toJson() => {
        "PersonId": personId,
        "DateEntryId": dateEntryId,
        "InitiatorId": initiatorId,
        "Initiator": initiator,
        "Remark": remark,
        "StateId": stateId,
        "State": state,
        "AvUnavailable": avUnavailable,
        "AvFromTime": avFromTime,
        "AvToTime": avToTime,
        "ManagerResponsible": managerResponsible,
        "ManagerSkipInvite": managerSkipInvite,
      };
}

class OpenServiceModel {
  final String? departmentId;
  final String? baseServiceId;
  final bool? subscribed;

  OpenServiceModel({
    this.departmentId,
    this.baseServiceId,
    this.subscribed,
  });

  factory OpenServiceModel.fromJson(Map<String, dynamic> json) {
    return OpenServiceModel(
      departmentId: json["DepartmentId"],
      baseServiceId: json["BaseServiceId"],
      subscribed: json["Subscribed"] ?? false,
    );
  }

  // Convert from week OpenWeekServiceModel to month OpenServiceModel
  factory OpenServiceModel.fromWeekService(OpenWeekServiceModel weekService) {
    return OpenServiceModel(
      departmentId: weekService.departmentId,
      baseServiceId: weekService.baseServiceId,
      subscribed: weekService.subscribed,
    );
  }

  Map<String, dynamic> toJson() => {
        "DepartmentId": departmentId,
        "BaseServiceId": baseServiceId,
        "Subscribed": subscribed,
      };
}

class CalendarEntry {
  String? id;
  String? title;
  String? relation;
  String? contactMedia;
  String? description;
  String? remark;

  CalendarEntry({
    this.id,
    this.title,
    this.relation,
    this.contactMedia,
    this.description,
    this.remark,
  });

  factory CalendarEntry.fromJson(Map<String, dynamic> json) => CalendarEntry(
        id: json["ID"],
        title: json["Title"],
        relation: json["Relation"],
        contactMedia: json["ContactMedia"],
        description: json["Description"],
        remark: json["Remark"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "Title": title,
        "Relation": relation,
        "ContactMedia": contactMedia,
        "Description": description,
        "Remark": remark,
      };
}
