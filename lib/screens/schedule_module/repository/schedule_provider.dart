import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../common/common_functions/common_dateformat_function.dart';
import '../../../main.dart';
import '../../../service/api_service/api_function.dart';
import '../../../service/api_service/server_constants.dart';

class scheduleApiProvider {
  DateTime selectedDate = DateTime.now();
  static int weekNumber =
      (DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays /
              7)
          .ceil();

  Future<Response?> scheduleWeekApiCall(
      BuildContext context, String iosYearWeek) async {
    try {
      // print("ISOWEEKYEAR ========>${DateFormatFunctions.IosYearWeekData(selectedDate)}");

      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearWeek": iosYearWeek
      };

      Response response = await APIFunction.postAPICall(
        apiName:
            ServerConstant.base_url + ServerConstant.scheduleSwapAndOpenWeek,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      print("ISOYearWeek ========>${iosYearWeek}");

      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> scheduleMonthApiCall(
      BuildContext context, String iosYearMonth) async {
    try {
      print(
          "ISOWEEKYEAR ========>${DateFormatFunctions.IosYearWeekData(selectedDate)}");

      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonth": iosYearMonth
      };

      Response response = await APIFunction.postAPICall(
        apiName:
            ServerConstant.base_url + ServerConstant.scheduleSwapAndOpenMonth,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
      return null;
    }
  }

  Future<Response?> scheduleDateDepartmentApiCall(
      BuildContext context, String iSODate) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISODate": iSODate
      };

      print("query##${query}");
      Response response = await APIFunction.postAPICall(
        apiName:
            ServerConstant.base_url + ServerConstant.scheduleDepartmentList,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> scheduleDateApiCall(
      BuildContext context, String iosDate, String guid) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISODate": iosDate,
        "guid": guid
      };
      print("query## scheduleDate ${query}");
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.schedulePerDate,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> scheduleCalendarMonthApiCall(
      BuildContext context, String iosDate) async {
    try {
      final query = {
        "ISOYearMonth": iosDate,
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.scheduleCalendarData,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> setSwapAndServiceApiCall(
      BuildContext context,
      String personId,
      String dateEntryId,
      String stateId,
      String nextStateId,
      String? remark) async {
    try {
      final query = {
        "ChangeServiceSwapState": {
          "PersonId": personId,
          "DateEntryId": dateEntryId,
          "StateId": stateId,
          "NextStateId": nextStateId,
          "Remark": remark
        },
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };
      print("response =======>$personId");
      print("response =======>$dateEntryId");
      print("response =======>$stateId");
      print("response =======>$nextStateId");
      print("response =======>$remark");

      Response response = await APIFunction.postAPICall(
        apiName:
            ServerConstant.base_url + ServerConstant.scheduleSetSwapService,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> cancelSwapAndServiceApiCall(
    BuildContext context,
    String personId,
    String dateEntryId,
    String stateId,
    String nextStateId,
  ) async {
    try {
      final query = {
        "ChangeServiceSwapState": {
          "PersonId": personId,
          "DateEntryId": dateEntryId,
          "StateId": stateId,
          "NextStateId": nextStateId,
        },
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };
      print("response =======>$query");
      print("response =======>$dateEntryId");
      print("response =======>$stateId");
      print("response =======>$nextStateId");

      Response response = await APIFunction.postAPICall(
        apiName:
            ServerConstant.base_url + ServerConstant.scheduleSetSwapService,
        header: {
          ServerConstant.Header_Content_Key:
              ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
