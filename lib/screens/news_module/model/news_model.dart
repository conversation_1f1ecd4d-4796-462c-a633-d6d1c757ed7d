class NewsModel {
  String? dataObjectId;
  String? title;
  String? intro;
  String? content;
  DateTime? startDate;
  String? author;
  String? authorId;
  String? initialsPerson;
  String? photoId;
  bool? autoOpen;
  bool? isAdmin;
  List<FileElement>? files;
  String? filesHtml;
  bool? isImageAvailable;

  NewsModel({
    this.dataObjectId,
    this.title,
    this.intro,
    this.content,
    this.startDate,
    this.author,
    this.authorId,
    this.initialsPerson,
    this.photoId,
    this.autoOpen,
    this.isAdmin,
    this.files,
    this.filesHtml,
    this.isImageAvailable,
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) => NewsModel(
    dataObjectId: json["DataObjectId"],
    title: json["Title"],
    intro: json["Intro"],
    content: json["Content"],
    startDate: DateTime.parse(json["StartDate"]),
    author: json["Author"],
    authorId: json["AuthorId"],
    initialsPerson: json["InitialsPerson"],
    photoId: json["PhotoId"],
    autoOpen: json["AutoOpen"],
    isAdmin: json["IsAdmin"],
    files: json["Files"] == null ? [] : List<FileElement>.from(json["Files"]!.map((x) => FileElement.fromJson(x))),
    filesHtml: json["FilesHTML"],
  );

  Map<String, dynamic> toJson() => {
    "DataObjectId": dataObjectId,
    "Title": title,
    "Intro": intro,
    "Content": content,
    "StartDate": startDate?.toIso8601String(),
    "Author": author,
    "AuthorId": authorId,
    "InitialsPerson": initialsPerson,
    "PhotoId": photoId,
    "AutoOpen": autoOpen,
    "IsAdmin": isAdmin,
    "Files": files == null ? [] : List<dynamic>.from(files!.map((x) => x.toJson())),
    "FilesHTML": filesHtml,
  };
}

class FileElement {
  String dataObjectId;
  String title;
  String fileType;
  String creator;
  DateTime date;

  FileElement({
    required this.dataObjectId,
    required this.title,
    required this.fileType,
    required this.creator,
    required this.date,
  });

  factory FileElement.fromJson(Map<String, dynamic> json) => FileElement(
    dataObjectId: json["DataObjectId"],
    title: json["Title"],
    fileType: json["FileType"],
    creator: json["Creator"],
    date: DateTime.parse(json["Date"]),
  );

  Map<String, dynamic> toJson() => {
    "DataObjectId": dataObjectId,
    "Title": title,
    "FileType": fileType,
    "Creator": creator,
    "Date":
    "${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}",
  };
}
