class NewsReadSuccessResponse {
  List<String> result;
  bool done;
  String newGuid;
  dynamic newTitle;
  dynamic redirectUrl;

  NewsReadSuccessResponse({
    required this.result,
    required this.done,
    required this.newGuid,
    required this.newTitle,
    required this.redirectUrl,
  });

  factory NewsReadSuccessResponse.fromJson(Map<String, dynamic> json) => NewsReadSuccessResponse(
        result: List<String>.from(json["Result"].map((x) => x)),
        done: json["Done"],
        newGuid: json["NewGuid"],
        newTitle: json["NewTitle"],
        redirectUrl: json["RedirectURL"],
      );

  Map<String, dynamic> toJson() => {
        "Result": List<dynamic>.from(result.map((x) => x)),
        "Done": done,
        "NewGuid": newGuid,
        "NewTitle": newTitle,
        "RedirectURL": redirectUrl,
      };
}
