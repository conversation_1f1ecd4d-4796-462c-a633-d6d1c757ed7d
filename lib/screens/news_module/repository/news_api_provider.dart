import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

import '../../../main.dart';

class NewsApiProvider {
  Future<Response?> newsApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.news,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> newsDocumentApiCall(BuildContext context, String guid) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "guid": guid
      };
      print("queryqueryqueryquery${query}");

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.getProfilePic,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> newsAutoOpenApi(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.newsAutoOpen,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
      if (error == 401) {
        print('---login fail user');
        customSnackBar(
          context: context,
          message: 'login fail user',
        );
      }
    }
    return null;
  }

  Future<Response?> autoOpenNewsReadApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.newsAutoOpenSuccess,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
