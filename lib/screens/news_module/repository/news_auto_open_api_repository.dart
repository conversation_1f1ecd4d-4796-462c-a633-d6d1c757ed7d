import 'package:flutter/material.dart';
import 'package:staff_medewerker/app/db/model/download_file_model.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/screens/news_module/model/news_model.dart';
import 'package:staff_medewerker/screens/news_module/model/news_response_model.dart';
import 'package:staff_medewerker/screens/news_module/repository/news_api_provider.dart';

class NewsAutoOpenApiRepository {
  final newsProvider = NewsApiProvider();

  Future<List<NewsModel>?> newsAutoOpenApi({required BuildContext context}) async {
    final response = await newsProvider.newsAutoOpenApi(context);
    print('---login fail user${response?.statusCode}');
    if (response?.statusCode == 401) {
      print('---login fail user');
      customSnackBar(
        context: context,
        message: 'login fail user',
      );
    } else if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<NewsModel> newsAutoOpenList = [];

      for (var item in dataList) {
        newsAutoOpenList.add(NewsModel.fromJson(item));
      }

      return newsAutoOpenList;
    } else {
      return null;
    }
  }

  Future<NewsReadSuccessResponse?> autoOpenNewsRead({required BuildContext context}) async {
    final response = await newsProvider.autoOpenNewsReadApiCall(context);

    if (response != null) {
      return NewsReadSuccessResponse.fromJson(response.data);
    } else {
      return null;
    }
  }

  Future<DownloadFileModel?> newsDocumentApiCall({required BuildContext context, required String guid}) async {
    final response = await newsProvider.newsDocumentApiCall(context, guid);

    if (response != null) {
      if (response.data != null) {
        return DownloadFileModel.fromJson(response.data);
      }
    } else {
      //return null;
    }
  }
}
