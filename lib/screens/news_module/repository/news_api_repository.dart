import 'package:flutter/material.dart';
import 'package:staff_medewerker/app/db/model/download_file_model.dart';
import 'package:staff_medewerker/screens/news_module/model/news_model.dart';
import 'package:staff_medewerker/screens/news_module/repository/news_api_provider.dart';

class NewsApiRepository {
  final newsProvider = NewsApiProvider();

  Future<List<NewsModel>?> newsApi({required BuildContext context}) async {
    final response = await newsProvider.newsApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<NewsModel> news = [];

      for (var item in dataList) {
        news.add(NewsModel.fromJson(item));
      }

      return news;
    } else {
      return null;
    }
  }

  Future<DownloadFileModel?> newsDocumentApiCall({required BuildContext context, required String guid}) async {
    final response = await newsProvider.newsDocumentApiCall(context, guid);

    if (response != null) {

      if(response.data != null) {
        return DownloadFileModel.fromJson(response.data);
      }else{
        return null;

      }
    } else {
      return null;
    }
  }
}
