import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/notification_module/model/notification_model.dart';
import 'package:staff_medewerker/screens/notification_module/repository/notification_repository.dart';

part 'notification_state.dart';

class NotificationCubit extends Cubit<NotificationState> {
  NotificationCubit() : super(NotificationInitial());

  final notificationApiRepository notificationApi = notificationApiRepository();

  ValueNotifier<bool> isNotificationLoading = ValueNotifier(false);
  ValueNotifier<bool> isReadNotificationLoading = ValueNotifier(false);
  List<NotificationResponseModel> notificationList = [];

  Future<void> fetchNotificationList({
    required BuildContext context,
  }) async {
    isNotificationLoading.value = true;
    final response = await notificationApi.getNotificationApi(
      context: context,
    );

    if (response != null) {
      notificationList = response;
    }
    isNotificationLoading.value = false;
    // notificationList.forEach((element) {
    //  if( element.statusCode == 200) {
    //    customSnackBar(
    //      context: navigatorKey.currentContext!,
    //      message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
    //      actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
    //    );
    //  }else{
    //
    //  }
    // });
    emit(NotificationInitial());
  }

  Future<void> fetchReadNotificationList({required BuildContext context, required int id}) async {
    isReadNotificationLoading.value = true;
    final response = await notificationApi.readNotificationApi(
      context: context,
      id: id,
    );
    print("response ======>$response");
    if (response?.statusCode == 200 && response?.data['Done'] == true) {
      // notificationList.removeWhere((notification) => notification.id == id);
    }
    isReadNotificationLoading.value = false;
  }

  void removeNotificationList(int notificationId) {
    notificationList.removeWhere((notification) => notification.id == notificationId);

    emit(NotificationInitial());
  }
}
