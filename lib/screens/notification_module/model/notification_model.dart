class NotificationResponseModel {
  final int? id;
  bool? notificationRead;
  final String? created;
  final String? license;
  final String? title;
  final String? content;
  final String? guid;
  final String? type;
  final String? taskId;
  final int statusCode;

  NotificationResponseModel({
    this.id,
    this.notificationRead,
    this.created,
    this.license,
    this.title,
    this.content,
    this.guid,
    this.type,
    this.taskId,
    required this.statusCode,
  });

  factory NotificationResponseModel.fromJson(Map<String, dynamic> json, int statusCode) {
    return NotificationResponseModel(
        statusCode: statusCode,
        id: json['id'],
        notificationRead: json['NotificationRead'],
        created: json['Created'],
        license: json['License'],
        title: json['Title'],
        content: json['Content'],
        guid: json['guid'],
        taskId: json['TaskId'],
        type: json['Type']);
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "NotificationRead": notificationRead,
        "Created": created,
        "License": license,
        "Title": title,
        "Content": content,
        "guid": guid,
        "TaskId": taskId,
        "Type": type,
      };
}
