import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/notification_module/model/notification_model.dart';

import 'notification_provider.dart';

class notificationApiRepository {
  final notificationProvider = notificationApiProvider();

  Future<List<NotificationResponseModel>?> getNotificationApi({required BuildContext context}) async {
    final response = await notificationProvider.getNotificationApiCall(context);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> notificationList = response.data;
      List<NotificationResponseModel> notificationDetail = [];

      for (var item in notificationList) {
        notificationDetail.add(NotificationResponseModel.fromJson(item, response.statusCode!));
      }

      return notificationDetail;
    } else {
      return null;
    }
  }

  Future<Response?> readNotificationApi({required BuildContext context, required int id}) async {
    final response = await notificationProvider.readNotificationApiCall(context, id);
    return response;
  }
}
