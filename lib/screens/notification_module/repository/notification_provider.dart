import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../main.dart';
import '../../../service/api_service/api_function.dart';
import '../../../service/api_service/server_constants.dart';

class notificationApiProvider {
  Future<Response?> getNotificationApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey}
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.notificationList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> readNotificationApiCall(BuildContext context, int id) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "Id": id
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.readNotification,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
