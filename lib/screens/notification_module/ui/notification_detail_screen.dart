import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/custom_widgets/appbar_custom.dart';
import '../../../common/custom_widgets/spacebox.dart';
import '../../../utils/appsize.dart';
import '../model/notification_model.dart';

class NotificationDetailScreen extends StatelessWidget {
  final NotificationResponseModel? notificationData;
  final int? notificationId; // Pass the id of the notification to remove.

  const NotificationDetailScreen({Key? key, this.notificationData, this.notificationId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print('--------->$notificationId');
    print('--------->${notificationData?.content}');
   // final notificationBloc = BlocProvider.of<NotificationCubit>(context);

    return Scaffold(
      appBar: CustomAppBar(
          title: notificationData?.title ?? 'Rooster opnieuw gepubliceerd',
          onTap: () {
            Navigator.pop(context);
          }),
      body: Column(
        children: [
          SpaceV(AppSize.h20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
            child: Html(
              data: notificationData?.content ?? 'Rooster opnieuw gepubliceerd',
              onLinkTap: (url, attributes, element) {
                print(url);
                print(attributes);
                print(element);
                if (url != null) {
                  launchUrl(Uri.parse(url));
                }
              },
              style: {
                // You can style the HTML content here
                'body': Style(
                  fontSize: FontSize(AppSize.sp15),
                  color: context.themeColors.textColor,
                ),
                'a': Style(
                  textDecoration: TextDecoration.underline,
                  color: Colors.blue, // Set the link color
                ),
              },
            ),
          )
        ],
      ),
    );
  }
}
