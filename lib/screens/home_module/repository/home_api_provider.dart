import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/home_module/model/first_shift_module.dart';

import 'home_api_repository.dart';

class homeApiRepository {
  final homeProvider = homeApiProvider();

  Future<List<FirstShiftResponseModel>?> plannedShiftForWeekApi(
      {required BuildContext context, required String iosYearWeek}) async {
    final response = await homeProvider.plannedShiftForWeekApiCall(context: context, iOSYearWeek: iosYearWeek);
    if (response != null) {
      List<dynamic> planWeekList = response.data;
      print('----------->week-list ${planWeekList.length}');
      List<FirstShiftResponseModel> planWeekDetailList = [];
      planWeekDetailList.clear();

      for (var item in planWeekList) {
        planWeekDetailList.add(FirstShiftResponseModel.fromJson(item, response.statusCode!));
      }

      return planWeekDetailList;
    } else {
      return null;
    }
  }

  Future<List<FirstShiftResponseModel>?> plannedShiftForMonthApi(
      {required BuildContext context, required String ISOYearMonth}) async {
    final response = await homeProvider.plannedShiftForMonthApiCall(context: context, ISOYearMonth: ISOYearMonth);
    print("response ===============>${response}");
    if (response != null && response.data is List<dynamic>) {
      List<dynamic> planMonthList = response.data;
      print('----------->week-list ${planMonthList.length}');
      List<FirstShiftResponseModel> planMonthDetailList = [];
      planMonthDetailList.clear();

      for (var item in planMonthList) {
        planMonthDetailList.add(FirstShiftResponseModel.fromJson(item, response.statusCode!));
      }

      return planMonthDetailList;
    } else {
      return null;
    }
  }

  Future<FirstShiftResponseModel?> firstUpcomingShiftApi({required BuildContext context, String? iosYearWeek}) async {
    final response = await homeProvider.firstUpcomingShiftApiCall(context);
    print("response ===============>${response}");
    if (response != null && response.data != null) {
      return FirstShiftResponseModel.fromJson(response.data, response.statusCode!);
    } else {
      return null;
    }
  }
}
