import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';

import '../../../../../service/api_service/api_function.dart';
import '../../../../../service/api_service/server_constants.dart';
import '../../../common/common_functions/common_dateformat_function.dart';

class homeApiProvider {
  Future<Response?> firstUpcomingShiftApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        // "ISODate": DateFormatFunctions.formatDate(DateTime.now())
      };
      print("firstUpcomingShiftApiCall query${query}");
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.firstShift,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> plannedShiftForWeekApiCall({required BuildContext context, required String iOSYearWeek}) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearWeek": iOSYearWeek
      };
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.schedulePerWeek,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      print('----------->week-list ${response}');

      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> plannedShiftForMonthApiCall({required BuildContext context, required String ISOYearMonth}) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISOYearMonth": ISOYearMonth
      };
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.schedulePerMonth,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}
