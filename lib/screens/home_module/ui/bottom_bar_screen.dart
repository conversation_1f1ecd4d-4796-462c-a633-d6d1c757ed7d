import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ionicons/ionicons.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:staff_medewerker/common/custom_widgets/common_drawer.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/availability_module/ui/availability_screen.dart';
import 'package:staff_medewerker/screens/home_module/ui/home_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_main_screen.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';
import 'package:staff_medewerker/screens/news_module/news_auto_open_screen.dart';
import 'package:staff_medewerker/screens/news_module/news_screen.dart';
import 'package:staff_medewerker/screens/notification_module/bloc/notification_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/schedule_screen.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class BottomBarScreen extends StatefulWidget {
  final String? url;
  final bool isFromFirstTime;
  final bool? isApiCall;
  const BottomBarScreen(
      {Key? key, this.url = "", this.isFromFirstTime = false, this.isApiCall})
      : super(key: key);

  @override
  _BottomBarScreenState createState() => _BottomBarScreenState();
}

class _BottomBarScreenState extends State<BottomBarScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      // await PermissionProvider.checkForPermission();
      await prefs.setString(AppConstants.lastPosition, AppConstants.homeScreen);
      print("============>lastPositionSet $APIKey");
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    final newsBloc = BlocProvider.of<NewsCubit>(context);

    if (AppLifecycleState.resumed == state) {
      print("resumed callled");
    }
    if (AppLifecycleState.paused == state) {
      print("paused callled");
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (widget.isApiCall??false) {
          newsBloc.newsAutoOpenApiCall(context: context).then((value) {
            if (newsBloc.newsAutoOpenList.length != 0) {
              // AppNavigation.nextScreen(navigatorKey.currentContext!, NewsAutoOpenScreen());
              Navigator.push(
                context,
                PageRouteBuilder(
                  transitionDuration: const Duration(milliseconds: 300),
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      NewsAutoOpenScreen(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    // Apply custom animation for navigation
                    var begin = Offset(0.0, 1.0);
                    var end = Offset.zero;
                    var tween = Tween(begin: begin, end: end);
                    var offsetAnimation = animation.drive(tween);
                    return SlideTransition(
                      position: offsetAnimation,
                      child: child,
                    );
                  },
                ),
              );
            }
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      endDrawer: CommonDrawer(),
      key: homeScaffoldKey,
      resizeToAvoidBottomInset: true,
      body: PersistentTabView(
        context,
        controller: persistentTabController,
        screens: _buildScreens(),
        items: _navBarsItems(),
        onItemSelected: (value) {
          if (value == 5) {
            persistentTabController.jumpToTab(newIndex);
            homeScaffoldKey.currentState?.openEndDrawer();
          } else {
            print("value $value");
            newIndex = value;
          }
        },
        decoration: NavBarDecoration(
            border: Border(
                top:
                    BorderSide(width: 1, color: Colors.grey.withOpacity(0.3)))),
        backgroundColor: context.themeColors.cardColor,

        padding: EdgeInsets.all(1),
        handleAndroidBackButtonPress: true, // Default is true.
        confineToSafeArea: true,
        resizeToAvoidBottomInset:
            true, // This needs to be true if you want to move up the screen when keyboard appears. Default is true.
        stateManagement: true, // Default is true.
        hideNavigationBarWhenKeyboardAppears: true,
        popBehaviorOnSelectedNavBarItemPress: PopBehavior.all,
        animationSettings: const NavBarAnimationSettings(
          navBarItemAnimation: ItemAnimationSettings(
            // Navigation Bar's items animation properties.
            duration: Duration(milliseconds: 200),
            curve: Curves.ease,
          ),
          screenTransitionAnimation: ScreenTransitionAnimationSettings(
            // Screen transition animation on change of selected tab.
            animateTabTransition: true,
            duration: Duration(milliseconds: 200),
            screenTransitionAnimationType: ScreenTransitionAnimationType.fadeIn,
          ),
        ),
        navBarStyle: NavBarStyle.style12,
      ),
    );
  }

  List<Widget> _buildScreens() {
    return [
      HomeScreen(
        isFromFirstTime: widget.isFromFirstTime,
        isApiCall: widget.isApiCall,
      ),
      AvailabilityScreen(),
      const ScheduleScreen(),
      HoursScreen(),
      const NewsScreen(),
      Container()
    ];
  }

  List<PersistentBottomNavBarItem> _navBarsItems() {
    return [
      PersistentBottomNavBarItem(
          icon: Icon(Ionicons.home),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
      PersistentBottomNavBarItem(
          icon: Icon(
            Ionicons.checkmark_circle,
          ),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
      PersistentBottomNavBarItem(
          icon: Icon(
            Ionicons.calendar,
          ),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
      PersistentBottomNavBarItem(
          icon: Icon(
            Ionicons.time,
          ),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
      PersistentBottomNavBarItem(
          icon: Icon(
            Ionicons.newspaper,
          ),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
      PersistentBottomNavBarItem(
          icon: Stack(
            clipBehavior: Clip.none,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Ionicons.menu,
                  ),
                ],
              ),
              Positioned(
                right: -6,
                top: 10,
                //right: 30,
                child: BlocBuilder<NotificationCubit, NotificationState>(
                  builder: (ctx, state) {
                    final notificationBloc = ctx.read<NotificationCubit>();

                    if (notificationBloc.notificationList.isNotEmpty) {
                      return Container(
                        height: AppSize.w12,
                        width: AppSize.w12,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.lightModeRedColor),
                        child: Center(
                            child: Text(
                          notificationBloc.notificationList.length.toString(),
                          style: context.textTheme.bodySmall?.copyWith(
                              color: AppColors.white, fontSize: AppSize.sp8),
                        )),
                      );
                    } else {
                      return Container(
                        width: 0,
                      );
                    }
                  },
                ),
              )
            ],
          ),
          activeColorPrimary: context.themeColors.primaryColor,
          inactiveColorPrimary: context.themeColors.darkGreyColor,
          iconSize: AppSize.sp22),
    ];
  }
}

final GlobalKey<ScaffoldState> homeScaffoldKey = GlobalKey<ScaffoldState>();
//
// class BottomBarScreen extends StatefulWidget {
//   final String? url;
//   final bool isFromFirstTime;
//   const BottomBarScreen({Key? key, this.url = "", this.isFromFirstTime = false}) : super(key: key);
//
//   @override
//   State<BottomBarScreen> createState() => _BottomBarScreenState();
// }
//
// class _BottomBarScreenState extends State<BottomBarScreen> {
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
//       // await PermissionProvider.checkForPermission();
//       await prefs.setString(AppConstants.lastPosition, AppConstants.homeScreen);
//       print("============> $APIKey");
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<BottomBarCubit, BottomBarState>(
//       builder: (ctx, state) {
//         final bottomBarCubit = ctx.read<BottomBarCubit>();
//         print("===========>${bottomBarCubit.state.currentIndex}");
//         return WillPopScope(
//             child: Container(
//               color: AppColors.primaryColor,
//               child: SafeArea(
//                 child: Scaffold(
//                   endDrawer: CommonDrawer(bottomBarCubit: bottomBarCubit),
//                   key: homeScaffoldKey,
//                   resizeToAvoidBottomInset: true,
//                   body: Stack(children: <Widget>[
//                     _buildOffstageNavigator(0, bottomBarCubit),
//                     _buildOffstageNavigator(1, bottomBarCubit),
//                     _buildOffstageNavigator(2, bottomBarCubit),
//                     _buildOffstageNavigator(3, bottomBarCubit),
//                     _buildOffstageNavigator(4, bottomBarCubit),
//                     _buildOffstageNavigator(5, bottomBarCubit),
//                   ]),
//                   bottomNavigationBar: BottomNavigation(
//                     currentTab: bottomBarCubit.state.currentIndex,
//                     onSelectTab: (index) {
//                       bottomBarCubit.selectTab(context: context, tabItem: index);
//                     },
//                   ),
//                 ),
//               ),
//             ),
//             onWillPop: () async {
//               final isFirstRouteInCurrentTab =
//                   !await bottomBarCubit.navigatorKeys[bottomBarCubit.state.currentIndex].currentState!.maybePop();
//               if (isFirstRouteInCurrentTab) {
//                 SystemChannels.platform.invokeMethod('SystemNavigator.pop');
//               }
//               return isFirstRouteInCurrentTab;
//             });
//       },
//     );
//   }
//
//   Widget _buildOffstageNavigator(int index, BottomBarCubit bottomBarCubit) {
//     return Offstage(
//       offstage: bottomBarCubit.state.currentIndex != index,
//       child: TabNavigator(
//         isFromFirstTime: widget.isFromFirstTime,
//         navigatorKey: bottomBarCubit.navigatorKeys[index],
//         index: index,
//       ),
//     );
//   }
// }
