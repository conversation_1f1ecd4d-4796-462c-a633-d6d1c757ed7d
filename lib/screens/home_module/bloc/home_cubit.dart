import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:isoweek/isoweek.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/home_module/model/first_shift_module.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../repository/home_api_provider.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(HomeInitial());
  ValueNotifier<bool> isShiftFirstLoading = ValueNotifier(true);
  ValueNotifier<bool> displayWeek = ValueNotifier(false);
  ValueNotifier<bool> isPlannedWeekLoading = ValueNotifier(true);
  ValueNotifier<bool> isPlannedMonthLoading = ValueNotifier(true);
  final homeApiRepository HomeApiRepository = homeApiRepository();

  FirstShiftResponseModel? firstShiftList;
  List<FirstShiftResponseModel> planShiftForWeekList = [];
  List<FirstShiftResponseModel> planShiftForMonthList = [];

  String formattedDate = '';
  String day = '';
  String monthName = DateFormat.MMMM(appDB.language).format(DateTime.now());
  String weekNumber = Week.current().weekNumber.toString();
  String summaryValue = '';

  Future<void> firstUpcomingShift({
    required BuildContext context,
    String? iosYearWeek,
  }) async {
    isShiftFirstLoading.value = true;
    final response = await HomeApiRepository.firstUpcomingShiftApi(
      context: context,
    );
    isShiftFirstLoading.value = false;
    if (response?.statusCode == 200 && response != null) {
      log("response ===============>${response}");
      firstShiftList = response;
    } else {
      log("response ===============>${response}");
      // customSnackBar(
      //   context: navigatorKey.currentContext!,
      //   message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
      //   actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      // );
    }
  }

  Future<void> plannedShiftForWeekApiData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isPlannedWeekLoading.value = true;
    final response = await HomeApiRepository.plannedShiftForWeekApi(
      context: context,
      iosYearWeek: iosYearWeek,
    );
    isPlannedWeekLoading.value = false;
    if (response != null) {
      log("response ===============>${response}");
      log("planShiftForWeekList ===============>${planShiftForWeekList.length}");
      planShiftForWeekList = response;
    } else {
      log("response ===============>${response}");
    }
  }

  Future<void> plannedShiftFoMonthApiData({
    required BuildContext context,
    required String ISOYearMonth,
  }) async {
    try {
      isPlannedMonthLoading.value = true;

      final response = await HomeApiRepository.plannedShiftForMonthApi(
        context: context,
        ISOYearMonth: ISOYearMonth,
      );
      isPlannedMonthLoading.value = false;
      if (response != null) {
        log("response ===============>${response}");
        planShiftForMonthList = response;
      } else {
        log("response ===============>${response}");
      }
    } catch (e) {
      isPlannedMonthLoading.value = false;
    }
  }

  updateSummaryValue(bool value) {
    displayWeek.value = value;
    emit(HomeInitial());
  }
}
