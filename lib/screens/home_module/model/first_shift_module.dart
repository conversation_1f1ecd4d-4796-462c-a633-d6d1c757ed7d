class FirstShiftResponseModel {
  final String? PersonId;
  final String? Department;
  final String? Date;
  final String? ISODate;
  final String? TimeFrom;
  final String? TimeUntil;
  final String? CostCenters;
  final String? DayRemark;
  final int statusCode;

  FirstShiftResponseModel({
    this.PersonId,
    this.Department,
    this.Date,
    this.ISODate,
    this.TimeFrom,
    this.TimeUntil,
    this.CostCenters,
    this.DayRemark,
    required this.statusCode,
  });

  factory FirstShiftResponseModel.fromJson(
      Map<String, dynamic> json, int statusCode) {
    return FirstShiftResponseModel(
      statusCode: statusCode,
      PersonId: json['PersonId'],
      Department: json['Department'],
      Date: json['Date'],
      ISODate: json['ISODate'],
      TimeFrom: json['TimeFrom'],
      TimeUntil: json['TimeUntil'],
      CostCenters: json['CostCenters'],
      DayRemark: json['DayRemark'],
    );
  }
}
