import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';

import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class HomeNewContainerWidget extends StatelessWidget {
  final bool isNews;
  final String? newsDate;
  final String? newsText;
  final String? userImg;
  final String? url;
  final String? localUrl;
  final int index;

  const HomeNewContainerWidget({
    Key? key,
    this.isNews = false,
    this.newsDate,
    this.newsText,
    this.userImg,
    this.url,
    this.localUrl,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final newsBloc = BlocProvider.of<NewsCubit>(navigatorKey.currentContext!);

    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.only(bottom: AppSize.h10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: context.themeColors.homeShadowColor,
            //spreadRadius: 5,
            blurRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.circular(AppSize.r4),
        color: context.themeColors.homeContainerColor,
      ),
      child: Padding(
        padding: EdgeInsets.only(
            left: AppSize.w8, right: AppSize.w8, top: isNews ? 0 : AppSize.h8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            isNews
                ? Padding(
                    padding: EdgeInsets.only(top: AppSize.h8),
                    child: Text(
                      AppLocalizations.of(context)!.latestNews,
                      style: context.textTheme.titleLarge?.copyWith(
                          fontSize: AppSize.sp14,
                          color: context.themeColors.darkGreyColor),
                    ),
                  )
                : Container(),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ValueListenableBuilder(
                      valueListenable: newsBloc.isProfileLoading,
                      builder: (BuildContext context, isProfileLoading,
                          Widget? child) {
                        log("downLoadImageList else1=====>${newsBloc.newsList[index].isImageAvailable}");

                        if (isProfileLoading) {
                          return Container(
                            alignment: Alignment.center,
                            height: AppSize.h36,
                            width: AppSize.h36,
                            child: Text(
                              newsBloc.newsList[index].initialsPerson ?? '',
                              style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.white,
                                  fontSize: AppSize.sp15),
                            ),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.primaryColor,
                            ),
                          );
                        } else {
                          if (newsBloc.imageWidgets[index] is Image) {
                            // Check if the widget is an Image
                            return Container(
                              height: AppSize.h36,
                              width: AppSize.h36,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                  image: (newsBloc.imageWidgets[index] as Image)
                                      .image, // Cast to Image and access image property
                                  fit: BoxFit
                                      .cover, // You can adjust the fit as needed
                                ),
                              ),
                            );
                          } else {
                            return Container(
                              alignment: Alignment.center,
                              height: AppSize.h36,
                              width: AppSize.h36,
                              child: Text(
                                newsBloc.newsList[index].initialsPerson ?? '',
                                style: context.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.white,
                                    fontSize: AppSize.sp15),
                              ),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.primaryColor,
                              ),
                            );
                          }
                        }
                      }),
                  SpaceH(AppSize.w10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Text(
                        newsBloc.newsList[index].author ?? '',
                        textAlign: TextAlign.center,
                        style: context.textTheme.titleMedium
                            ?.copyWith(fontSize: AppSize.sp13),
                      ),
                      SpaceV(AppSize.h2),
                      Text(
                        newsDate ?? '',
                        textAlign: TextAlign.center,
                        style: context.textTheme.titleMedium?.copyWith(
                            fontSize: AppSize.sp13,
                            color: context.themeColors.darkGreyColor),
                      ),
                    ],
                  )
                ],
              ),
            ),
            Text(
              newsText ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: context.textTheme.titleMedium?.copyWith(
                  height: 1.4,
                  fontSize: AppSize.sp15,
                  color: AppColors.primaryColor),
            ),
            SpaceV(AppSize.h10),
          ],
        ),
      ),
    );
  }
}

class HomeNewContainerShimmerWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.only(bottom: AppSize.h10),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: context.themeColors.homeShadowColor,
            //spreadRadius: 5,
            blurRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.circular(AppSize.r4),
        color: context.themeColors.homeContainerColor,
      ),
      child: ShimmerWidget(
        // baseColor: AppColors.red,
        // highlightColor: AppColors.primaryColor,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.all(AppSize.sp14),
                  height: AppSize.h40,
                  width: AppSize.w40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.white,
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                            bottom: AppSize.sp4, right: AppSize.sp10),
                        height: AppSize.h10,
                        color: AppColors.white,
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: AppSize.sp4, right: AppSize.sp10),
                        height: AppSize.h10,
                        color: AppColors.white,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Container(
              margin: EdgeInsets.only(
                  bottom: AppSize.h10, left: AppSize.sp10, right: AppSize.sp10),
              height: AppSize.h10,
              color: AppColors.white,
            )
          ],
        ),
      ),
    );
  }
}
