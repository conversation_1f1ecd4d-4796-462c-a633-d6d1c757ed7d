import 'package:flutter/material.dart';

import '../../../common/custom_widgets/shimmer_effect.dart';
import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';


class ShiftShimmerWidget extends StatelessWidget {
  const ShiftShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppSize.h10,horizontal: AppSize.w10),
        child: Container( height: AppSize.h46,
          color: AppColors.white,),
      ),
    );
  }
}


class WorkHourShimmerWidget extends StatelessWidget {
  const WorkHourShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppSize.h10,horizontal: AppSize.w10),
        child: Container( height: AppSize.h46,
          color: AppColors.white,),
      ),
    );
  }
}