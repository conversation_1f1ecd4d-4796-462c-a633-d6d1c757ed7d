import 'package:flutter/cupertino.dart';

import '../../../common/custom_widgets/shimmer_effect.dart';
import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class HomeShiftContainerShimmerWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(bottom: AppSize.sp4, right: AppSize.sp40),
                      height: AppSize.h20,
                      color: AppColors.white,
                    ),
                    Container(
                      margin: EdgeInsets.only(top: AppSize.sp4, right: AppSize.sp72),
                      height: AppSize.h10,
                      color: AppColors.white,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
