import 'dart:ui';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class LanguageCubit extends Cubit<Locale> {
  final Locale locale;
  LanguageCubit({required this.locale}) : super(locale);

  Future<void> setLocale(Locale locale) async {
    print(locale);
    emit(locale);
    await SharedPreferences.getInstance().then((value) => value.setString(AppConstants.currentLanguage, locale.languageCode));
  }

  Locale get currentLocale => state;
}

class SupportedLocales {
  static final all = [
    'en',
    'nl',
  ];
}
