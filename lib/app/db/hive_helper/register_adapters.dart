import 'package:hive/hive.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/address_data.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/media_data.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/profile_data.dart';
import 'package:staff_medewerker/app/db/app_db_models/user_credential.dart';
import 'package:staff_medewerker/app/db/app_db_models/user_data.dart';

void registerAdapters() {
  Hive.registerAdapter(UserDataAdapter());
  Hive.registerAdapter(UserCredentialModelAdapter());
  Hive.registerAdapter(ProfileDataAdapter());
  Hive.registerAdapter(AddressAdapter());
  Hive.registerAdapter(MediaAdapter());
}
