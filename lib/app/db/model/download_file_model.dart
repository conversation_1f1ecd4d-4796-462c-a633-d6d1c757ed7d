class DownloadFileModel {
  String? fileId;
  String? name;
  String? description;
  String? fileName;
  String? fileContent;
  String? cloudURL;
  String? fileType;

  DownloadFileModel(
      {this.fileId, this.name, this.cloudURL, this.description, this.fileContent, this.fileName, this.fileType});

  factory DownloadFileModel.fromJson(Map<String, dynamic> json) => DownloadFileModel(
        fileId: json["FileId"],
        name: json["Name"],
        description: json["Description"],
        fileName: json["FileName"],
        fileContent: json["FileContent"],
        cloudURL: json["CloudURL"],
        fileType: json["FileType"],
      );

  Map<String, dynamic> toJson() => {
        "FileId": fileId,
        "Name": name,
        "Description": description,
        "FileName": fileName,
        "FileContent": fileContent,
        "CloudURL": cloudURL,
        "FileType": fileType,
      };
}
