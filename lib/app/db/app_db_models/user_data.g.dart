// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserDataAdapter extends TypeAdapter<UserData> {
  @override
  final int typeId = 0;

  @override
  UserData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserData(
      personId: fields[0] as String,
      UserNamePerson: fields[1] as String?,
      UserCallNamePerson: fields[2] as String?,
      InitialsPerson: fields[3] as String?,
      PhotoId: fields[4] as String?,
      UserAccountMail: fields[5] as String?,
      LanguageId: fields[6] as String?,
      Language: fields[7] as String?,
      Culture: fields[8] as String?,
      DefaultSkinId: fields[9] as String?,
      DefaultSkinChanged: fields[10] as String?,
      DefaultSkinPath: fields[11] as String?,
      LicenseId: fields[12] as String?,
      LicenseName: fields[13] as String?,
      LicenseSupportMessage: fields[15] as String?,
      LicenseSupportMessageMobile: fields[16] as String?,
      MustChangePassword: fields[17] as bool?,
      MustEnterTwoFactorAuthenticationToken: fields[18] as bool?,
      APIKey: fields[14] as String?,
      AppFunctionRightsList: (fields[19] as List?)?.cast<dynamic>(),
      IPAddressesSpecificUseList: (fields[20] as List?)?.cast<dynamic>(),
      profileData: fields[21] as ProfileData?,
    );
  }

  @override
  void write(BinaryWriter writer, UserData obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.personId)
      ..writeByte(1)
      ..write(obj.UserNamePerson)
      ..writeByte(2)
      ..write(obj.UserCallNamePerson)
      ..writeByte(3)
      ..write(obj.InitialsPerson)
      ..writeByte(4)
      ..write(obj.PhotoId)
      ..writeByte(5)
      ..write(obj.UserAccountMail)
      ..writeByte(6)
      ..write(obj.LanguageId)
      ..writeByte(7)
      ..write(obj.Language)
      ..writeByte(8)
      ..write(obj.Culture)
      ..writeByte(9)
      ..write(obj.DefaultSkinId)
      ..writeByte(10)
      ..write(obj.DefaultSkinChanged)
      ..writeByte(11)
      ..write(obj.DefaultSkinPath)
      ..writeByte(12)
      ..write(obj.LicenseId)
      ..writeByte(13)
      ..write(obj.LicenseName)
      ..writeByte(14)
      ..write(obj.APIKey)
      ..writeByte(15)
      ..write(obj.LicenseSupportMessage)
      ..writeByte(16)
      ..write(obj.LicenseSupportMessageMobile)
      ..writeByte(17)
      ..write(obj.MustChangePassword)
      ..writeByte(18)
      ..write(obj.MustEnterTwoFactorAuthenticationToken)
      ..writeByte(19)
      ..write(obj.AppFunctionRightsList)
      ..writeByte(20)
      ..write(obj.IPAddressesSpecificUseList)
      ..writeByte(21)
      ..write(obj.profileData);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
