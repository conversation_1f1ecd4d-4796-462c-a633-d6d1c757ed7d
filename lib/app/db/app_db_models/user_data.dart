import 'package:hive/hive.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/profile_data.dart';

part 'user_data.g.dart';

@HiveType(typeId: 0)
class UserData extends HiveObject {
  UserData({
    required this.personId,
    this.UserNamePerson,
    this.UserCallNamePerson,
    this.InitialsPerson,
    this.PhotoId,
    this.UserAccountMail,
    this.LanguageId,
    this.Language,
    this.Culture,
    this.DefaultSkinId,
    this.DefaultSkinChanged,
    this.DefaultSkinPath,
    this.LicenseId,
    this.LicenseName,
    this.LicenseSupportMessage,
    this.LicenseSupportMessageMobile,
    this.MustChangePassword,
    this.MustEnterTwoFactorAuthenticationToken,
    this.APIKey,
    this.AppFunctionRightsList,
    this.IPAddressesSpecificUseList,
    this.profileData,
  });

  // user data
  @HiveField(0)
  String personId;
  @HiveField(1)
  String? UserNamePerson;
  @HiveField(2)
  String? UserCallNamePerson;
  @HiveField(3)
  String? InitialsPerson;
  @HiveField(4)
  String? PhotoId;
  @HiveField(5)
  String? UserAccountMail;
  @HiveField(6)
  String? LanguageId;
  @HiveField(7)
  String? Language;
  @HiveField(8)
  String? Culture;
  @HiveField(9)
  String? DefaultSkinId;
  @HiveField(10)
  String? DefaultSkinChanged;
  @HiveField(11)
  String? DefaultSkinPath;
  @HiveField(12)
  String? LicenseId;
  @HiveField(13)
  String? LicenseName;
  @HiveField(14)
  String? APIKey;
  @HiveField(15)
  String? LicenseSupportMessage;
  @HiveField(16)
  String? LicenseSupportMessageMobile;
  @HiveField(17)
  bool? MustChangePassword;
  @HiveField(18)
  bool? MustEnterTwoFactorAuthenticationToken;
  @HiveField(19)
  List? AppFunctionRightsList;
  @HiveField(20)
  List? IPAddressesSpecificUseList;
  @HiveField(21)
  ProfileData? profileData;
}
