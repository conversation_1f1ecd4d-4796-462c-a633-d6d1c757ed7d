// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProfileDataAdapter extends TypeAdapter<ProfileData> {
  @override
  final int typeId = 7;

  @override
  ProfileData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProfileData(
      socialSecurityNumber: fields[0] as String?,
      initials: fields[1] as String?,
      callName: fields[2] as String?,
      firstName: fields[3] as String?,
      middleName: fields[4] as String?,
      lastName: fields[5] as String?,
      fullName: fields[6] as String?,
      birthDate: fields[7] as String?,
      placeOfBirth: fields[8] as String?,
      bankAccount: fields[9] as String?,
      bankName: fields[10] as String?,
      age: fields[11] as int?,
      countryOfBirth: fields[12] as String?,
      gender: fields[13] as String?,
      maritalState: fields[14] as String?,
      nationality: fields[15] as String?,
      addresses: (fields[16] as List?)?.cast<Address>(),
      media: (fields[17] as List?)?.cast<Media>(),
      statusCode: fields[18] as int?,
      isApiCallDone: fields[19] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ProfileData obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.socialSecurityNumber)
      ..writeByte(1)
      ..write(obj.initials)
      ..writeByte(2)
      ..write(obj.callName)
      ..writeByte(3)
      ..write(obj.firstName)
      ..writeByte(4)
      ..write(obj.middleName)
      ..writeByte(5)
      ..write(obj.lastName)
      ..writeByte(6)
      ..write(obj.fullName)
      ..writeByte(7)
      ..write(obj.birthDate)
      ..writeByte(8)
      ..write(obj.placeOfBirth)
      ..writeByte(9)
      ..write(obj.bankAccount)
      ..writeByte(10)
      ..write(obj.bankName)
      ..writeByte(11)
      ..write(obj.age)
      ..writeByte(12)
      ..write(obj.countryOfBirth)
      ..writeByte(13)
      ..write(obj.gender)
      ..writeByte(14)
      ..write(obj.maritalState)
      ..writeByte(15)
      ..write(obj.nationality)
      ..writeByte(16)
      ..write(obj.addresses)
      ..writeByte(17)
      ..write(obj.media)
      ..writeByte(18)
      ..write(obj.statusCode)
      ..writeByte(19)
      ..write(obj.isApiCallDone);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProfileDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
