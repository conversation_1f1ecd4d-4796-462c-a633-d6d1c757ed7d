import 'package:hive/hive.dart';

part 'media_data.g.dart';

@HiveType(typeId: 9)
class Media {
  @HiveField(0)
  String? media;
  @HiveField(1)
  String? mediaType;
  @HiveField(2)
  String? roleType;
  @HiveField(3)
  bool? isPrimary;
  @HiveField(4)
  String? comment;

  Media({
    this.media,
    this.mediaType,
    this.roleType,
    this.isPrimary,
    this.comment,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
    media: json["Media"],
    mediaType: json["MediaType"],
    roleType: json["RoleType"],
    isPrimary: json["IsPrimary"],
    comment: json["Comment"],
  );

  Map<String, dynamic> toJson() => {
    "Media": media,
    "MediaType": mediaType,
    "RoleType": roleType,
    "IsPrimary": isPrimary,
    "Comment": comment,
  };
}
