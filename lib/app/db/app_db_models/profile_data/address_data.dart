import 'package:hive/hive.dart';

part 'address_data.g.dart';

@HiveType(typeId: 8)
class Address {
  @HiveField(0)
  String? addressType;
  @HiveField(1)
  String? street;
  @HiveField(2)
  String? number;
  @HiveField(3)
  String? zipCode;
  @HiveField(4)
  String? city;

  Address({
    this.addressType,
    this.street,
    this.number,
    this.zipCode,
    this.city,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
    addressType: json["AddressType"],
    street: json["Street"],
    number: json["Number"],
    zipCode: json["ZipCode"],
    city: json["City"],
  );

  Map<String, dynamic> toJson() => {
    "AddressType": addressType,
    "Street": street,
    "Number": number,
    "ZipCode": zipCode,
    "City": city,
  };
}
