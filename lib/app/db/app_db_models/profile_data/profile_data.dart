import 'package:staff_medewerker/app/db/app_db_models/profile_data/address_data.dart';
import 'package:hive/hive.dart';
import 'package:staff_medewerker/app/db/app_db_models/profile_data/media_data.dart';

part 'profile_data.g.dart';

@HiveType(typeId: 7)
class ProfileData {
  @HiveField(0)
  String? socialSecurityNumber;
  @HiveField(1)
  String? initials;
  @HiveField(2)
  String? callName;
  @HiveField(3)
  String? firstName;
  @HiveField(4)
  String? middleName;
  @HiveField(5)
  String? lastName;
  @HiveField(6)
  String? fullName;
  @HiveField(7)
  String? birthDate;
  @HiveField(8)
  String? placeOfBirth;
  @HiveField(9)
  String? bankAccount;
  @HiveField(10)
  String? bankName;
  @HiveField(11)
  int? age;
  @HiveField(12)
  String? countryOfBirth;
  @HiveField(13)
  String? gender;
  @HiveField(14)
  String? maritalState;
  @HiveField(15)
  String? nationality;
  @HiveField(16)
  List<Address>? addresses;
  @HiveField(17)
  List<Media>? media;
  @HiveField(18)
  int? statusCode;
  @HiveField(19)
  bool isApiCallDone;

  ProfileData(
      {this.socialSecurityNumber,
      this.initials,
      this.callName,
      this.firstName,
      this.middleName,
      this.lastName,
      this.fullName,
      this.birthDate,
      this.placeOfBirth,
      this.bankAccount,
      this.bankName,
      this.age,
      this.countryOfBirth,
      this.gender,
      this.maritalState,
      this.nationality,
      this.addresses,
      this.media,
      this.statusCode,
      required this.isApiCallDone});

  factory ProfileData.fromJson(Map<String, dynamic> json, int statusCode) =>
      ProfileData(
          socialSecurityNumber: json["SocialSecurityNumber"],
          initials: json["Initials"],
          callName: json["CallName"],
          firstName: json["FirstName"],
          middleName: json["MiddleName"],
          lastName: json["LastName"],
          fullName: json["FullName"],
          birthDate: json["BirthDate"],
          placeOfBirth: json["PlaceOfBirth"],
          bankAccount: json["BankAccount"],
          bankName: json["BankName"],
          age: json["Age"],
          countryOfBirth: json["CountryOfBirth"],
          gender: json["Gender"],
          maritalState: json["MaritalState"],
          nationality: json["Nationality"],
          addresses: List<Address>.from(
              (json["Addresses"] ?? []).map((x) => Address.fromJson(x))),
          media: json["Media"] != null
              ? List<Media>.from((json["Media"]??[]).map((x) => Media.fromJson(x)))
              : null,
          statusCode: statusCode,
          isApiCallDone: false);

  Map<String, dynamic> toJson() => {
        "SocialSecurityNumber": socialSecurityNumber,
        "Initials": initials,
        "CallName": callName,
        "FirstName": firstName,
        "MiddleName": middleName,
        "LastName": lastName,
        "FullName": fullName,
        "BirthDate": birthDate,
        "PlaceOfBirth": placeOfBirth,
        "BankAccount": bankAccount,
        "BankName": bankName,
        "Age": age,
        "CountryOfBirth": countryOfBirth,
        "Gender": gender,
        "MaritalState": maritalState,
        "Nationality": nationality,
        "Addresses":
            List<dynamic>.from((addresses?.map((x) => x.toJson()) ?? [])),
        "Media": List<dynamic>.from((media?.map((x) => x.toJson())) ?? []),
      };
}
